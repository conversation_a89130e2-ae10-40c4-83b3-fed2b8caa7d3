// package com.campain.myvoterapp

// import android.view.WindowManager.LayoutParams
// import io.flutter.embedding.android.FlutterActivity
// import io.flutter.embedding.engine.FlutterEngine
// import android.content.Intent
// import android.net.Uri
// import android.os.Bundle

// class MainActivity: FlutterActivity() {
//     override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
//         window.addFlags(LayoutParams.FLAG_SECURE)
//         super.configureFlutterEngine(flutterEngine)
//     }
//     override fun onCreate(savedInstanceState: Bundle?) {
//         super.onCreate(savedInstanceState)

//         // Check if this activity was started by a custom URL
//         val intent: Intent = getIntent()
//         val action: String? = intent.action
//         val data: Uri? = intent.data

//         if (Intent.ACTION_VIEW == action && data != null) {
//             val scheme: String? = data.scheme
//             if ("posterapp-political" == scheme) {
//                 // Handle the action you want when the URL is opened
//                 // For example, you can launch a specific screen or perform a task
//             }
//         }
//     }
// }
package com.postersforb.BB // Ensure this matches your package name

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.WindowManager.LayoutParams
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        window.addFlags(LayoutParams.FLAG_SECURE)
        super.configureFlutterEngine(flutterEngine)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check if this activity was started by a custom URL
        val intent: Intent = intent
        val action: String? = intent.action
        val data: Uri? = intent.data

        if (Intent.ACTION_VIEW == action && data != null) {
            val scheme: String? = data.scheme
            if ("posterapp-political" == scheme) {
                // Handle the action you want when the URL is opened
                // For example, you can launch a specific screen or perform a task
            }
        }
    }
}
