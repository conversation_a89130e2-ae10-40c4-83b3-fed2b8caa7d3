import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hexcolor/hexcolor.dart';

class PurchasePlans extends StatelessWidget {
  final String planDuration;
  final int planAmount;
  final bool isSelected;
  final bool showDiscount;
  final int gstPercentage;
  final String planId;
  final bool autopay;

  PurchasePlans({
    Key? key,
    required this.autopay,
    required this.planDuration,
    required this.planAmount,
    required this.isSelected,
    this.showDiscount = false,
    required this.gstPercentage,
    required this.planId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);

    // Display "499 + 18%" or "99 + 18%"
    String mainText = planDuration == '12 months' ? "499" : "99";
    String discountText = "18% GST";

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            color: themeData.colorScheme.surface,
            borderRadius: const BorderRadius.all(Radius.circular(15)),
            boxShadow: [
              BoxShadow(
                color: themeData.colorScheme.shadow.withAlpha(30),
                blurRadius: 12,
              ),
            ],
            border: Border.all(
              width: 2,
              color: isSelected
                  ? themeData.colorScheme.primary
                  : Colors.transparent,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: mainText,
                            style: TextStyle(
                              fontSize: 32,
                              fontFamily: 'Montserrat',
                              fontWeight: FontWeight.w800,
                              color: isSelected
                                  ? themeData.colorScheme.primary
                                  : themeData.colorScheme.onSurface,
                            ),
                          ),
                          TextSpan(
                            text: ' + ',
                            style: TextStyle(
                              fontSize: 20,
                              fontFamily: 'Montserrat',
                              fontWeight: FontWeight.w800,
                              color: themeData.colorScheme.onSurface,
                            ),
                          ),
                          TextSpan(
                            text: discountText,
                            style: TextStyle(
                              fontSize: 10, // Adjust the size as needed
                              fontFamily: 'Montserrat',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      planDuration,
                      style: TextStyle(
                        fontSize: 20,
                        fontFamily: 'Montserrat',
                        fontWeight: FontWeight.w600,
                        color: themeData.colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                  width: 24,
                ),
                planDuration == '12 months'
                    ? Stack(
                        alignment: Alignment.center,
                        children: [
                          Text(
                            '₹998',
                            style: TextStyle(
                              fontSize: 20,
                              color: themeData.colorScheme.outline,
                            ),
                          ),
                          Container(
                            color: themeData.colorScheme.primary,
                            height: 1,
                            width: 50,
                          )
                        ],
                      )
                    : SizedBox(),
                Spacer(),
                isSelected
                    ? SvgPicture.asset('Asset/Icons/TickCircle.svg', height: 32)
                    : Container(
                        height: 32,
                        width: 32,
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                          border:
                              Border.all(width: 2, color: HexColor('#454545')),
                        ),
                      ),
                const SizedBox(
                  height: 16,
                )
              ],
            ),
          ),
        ),
        Positioned(
          top: -16,
          child: showDiscount
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: _buildDiscountLabel(context),
                )
              : const SizedBox(height: 0),
        ),
      ],
    );
  }

  Widget _buildDiscountLabel(BuildContext context) {
    ThemeData themeData = Theme.of(context);

    return Container(
      alignment: Alignment.center,
      width: 120,
      height: 30,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: themeData.colorScheme.primary,
      ),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: 'SAVE ',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                  ),
            ),
            TextSpan(
              text: '50%',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    fontSize: 12, // Adjust the size as needed
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
