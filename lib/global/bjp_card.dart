import 'dart:io';

import 'package:flutter/material.dart';

class BJPCard extends StatelessWidget {
  final String name;
  final String position;
  final String state;
  final File? userImage;
  final String appuserid;

  BJPCard({
    required this.name,
    required this.position,
    required this.state,
    required this.appuserid,
    this.userImage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: MediaQuery.of(context).size.height * 0.3,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        image: DecorationImage(
          image: AssetImage(
            'Asset/Political Logos/76.jpeg',
          ),
          fit: BoxFit.fill,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // Top red box with center circle
          Stack(
            clipBehavior: Clip.none,
            children: [
              // Container(
              //   height: 80,
              //   decoration: BoxDecoration(
              //     color: Colors.orange[700],
              //     borderRadius: BorderRadius.only(
              //       topLeft: Radius.circular(10),
              //       topRight: Radius.circular(10),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.symmetric(horizontal: 20.0),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //       children: [
              //         Container(width: 60), // Placeholder to center the text
              //         Text(
              //           'BHARTIYA JANTA PARTY',
              //           style: TextStyle(
              //             color: Colors.white,
              //             fontWeight: FontWeight.bold,
              //             fontSize: 18,
              //           ),
              //         ),
              //         Container(width: 60), // Placeholder to center the text
              //       ],
              //     ),
              //   ),
              // ),
              // Positioned(
              // top: 55,
              // left: (MediaQuery.of(context).size.width / 2) - 50,
              // child: CircleAvatar(
              //   radius: 40,
              //   backgroundColor: Colors.white,
              //   backgroundImage: AssetImage(
              //     'Asset/Political Logos/BJP-Logo.png',
              //   ),
              // ),
              // ),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.055,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(25.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 80,
                    width: 80,
                    // radius: 0,
                    decoration: BoxDecoration(
                        color: Color.fromARGB(255, 248, 203, 144),
                        border: Border.all(color: Colors.black),
                        image: DecorationImage(
                          image: userImage != null
                              ? FileImage(userImage!)
                              : AssetImage('Asset/Images/RemoveBackground.jpg')
                                  as ImageProvider,
                        )),
                    // Replace with actual image URL or asset
                  ),
                  SizedBox(width: 18),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //  SizedBox(height: 18),
                      Text(
                        'Id No. - $appuserid',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 5),
                      Text(
                        '$name',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 5),
                      Text(
                        '$position',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 5),
                      Text(
                        '$state',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Bottom red box
          // Container(
          //   height: 50,
          //   decoration: BoxDecoration(
          //     color: Colors.orange[700],
          //     borderRadius: BorderRadius.only(
          //       bottomLeft: Radius.circular(10),
          //       bottomRight: Radius.circular(10),
          //     ),
          //   ),
          //   child: Row(
          //     children: [
          //       SizedBox(
          //         width: 30,
          //       ),
          //       Center(
          //         child: Text(
          //           ' Poster For Bharat',
          //           style: TextStyle(
          //             color: Colors.white,
          //             fontWeight: FontWeight.bold,
          //             fontSize: 14,
          //           ),
          //         ),
          //       ),
          //       SizedBox(
          //         width: 200,
          //       ),
          //       // SizedBox(width: MediaQuery.of(context).size.width / 2),
          //       Center(
          //         child: CircleAvatar(
          //           radius: 25,
          //           backgroundColor: Colors.white,
          //           backgroundImage: AssetImage(
          //             'Asset/Logo/Poster-App-Logo.png',
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }
}

// Column(
//         children: [
//           // Top red box with center circle
//           Stack(
//             clipBehavior: Clip.none,
//             children: [
//               Container(
//                 height: 80,
//                 decoration: BoxDecoration(
//                   color: Colors.orange[700],
//                   borderRadius: BorderRadius.only(
//                     topLeft: Radius.circular(10),
//                     topRight: Radius.circular(10),
//                   ),
//                 ),
//                 child: Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Container(width: 60), // Placeholder to center the text
//                       Text(
//                         'BHARTIYA JANTA PARTY',
//                         style: TextStyle(
//                           color: Colors.white,
//                           fontWeight: FontWeight.bold,
//                           fontSize: 18,
//                         ),
//                       ),
//                       Container(width: 60), // Placeholder to center the text
//                     ],
//                   ),
//                 ),
//               ),
//               Positioned(
//                 top: 55,
//                 left: (MediaQuery.of(context).size.width / 2) - 50,
//                 child: CircleAvatar(
//                   radius: 40,
//                   backgroundColor: Colors.white,
//                   backgroundImage: AssetImage(
//                     'Asset/Political Logos/BJP-Logo.png',
//                   ),
//                 ),
//               ),
//             ],
//           ),
//           SizedBox(
//             height: 40,
//           ),
//           Expanded(
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   CircleAvatar(
//                     radius: 50,
//                     backgroundImage: userImage != null
//                         ? FileImage(userImage!)
//                         : AssetImage('Asset/Images/RemoveBackground.jpg')
//                             as ImageProvider, // Replace with actual image URL or asset
//                   ),
//                   SizedBox(width: 18),
//                   Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         'Name: $name',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       SizedBox(height: 5),
//                       Text(
//                         'Position: $position',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       SizedBox(height: 5),
//                       Text(
//                         'State: $state',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           // Bottom red box
//           Container(
//             height: 50,
//             decoration: BoxDecoration(
//               color: Colors.orange[700],
//               borderRadius: BorderRadius.only(
//                 bottomLeft: Radius.circular(10),
//                 bottomRight: Radius.circular(10),
//               ),
//             ),
//             child: Row(
//               children: [
//                 SizedBox(
//                   width: 30,
//                 ),
//                 Center(
//                   child: Text(
//                     ' Poster For Bharat',
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontWeight: FontWeight.bold,
//                       fontSize: 14,
//                     ),
//                   ),
//                 ),
//                 SizedBox(
//                   width: 200,
//                 ),
//                 // SizedBox(width: MediaQuery.of(context).size.width / 2),
//                 Center(
//                   child: CircleAvatar(
//                     radius: 25,
//                     backgroundColor: Colors.white,
//                     backgroundImage: AssetImage(
//                       'Asset/Logo/Poster-App-Logo.png',
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
