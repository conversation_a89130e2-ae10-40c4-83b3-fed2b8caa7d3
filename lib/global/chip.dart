import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomChip extends StatefulWidget {
  final String text;
  final bool showIcon;
  final Color? color;
  final Function(String value)? onRemove;
  const CustomChip(
      {super.key,
      required this.text,
      required this.showIcon,
      this.color,
      this.onRemove});

  @override
  State<CustomChip> createState() => _CustomChipState();
}

class _CustomChipState extends State<CustomChip> {
  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
          color: widget.color ?? theme.colorScheme.primaryContainer,
          border: widget.showIcon
              ? Border.all(color: theme.colorScheme.primary, width: 2)
              : Border(),
          borderRadius: BorderRadius.circular(5)),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.text,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                fontFamily: 'Montserrat',
                color: theme.colorScheme.onBackground),
          ),
          const SizedBox(
            width: 12,
          ),
          widget.showIcon
              ? GestureDetector(
                  onTap: () {
                    widget.onRemove?.call(widget.text);
                  },
                  child: SvgPicture.asset(
                    'Assets/Svg/cross_icon.svg',
                    color: theme.colorScheme.primary,
                  ))
              : SizedBox()
        ],
      ),
    );
  }
}
