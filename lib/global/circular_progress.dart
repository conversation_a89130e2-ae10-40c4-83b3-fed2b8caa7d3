import 'package:flutter/material.dart';

class CustomCircularBar extends StatefulWidget {
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;
  const CustomCircularBar(
      {required this.progressColor,
      required this.backgroundColor,
      required this.strokeWidth,
      super.key});

  @override
  State<CustomCircularBar> createState() => _CustomCircularBarState();
}

class _CustomCircularBarState extends State<CustomCircularBar> {
  @override
  Widget build(BuildContext context) {
    return CircularProgressIndicator(
      color: widget.progressColor,
      backgroundColor: widget.backgroundColor,
      strokeWidth: widget.strokeWidth,
      strokeCap: StrokeCap.round,
    );
  }
}
