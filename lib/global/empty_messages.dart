import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class EmptyMessages extends StatefulWidget {
  const EmptyMessages({super.key});

  @override
  State<EmptyMessages> createState() => _EmptyMessagesState();
}

class _EmptyMessagesState extends State<EmptyMessages> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset('Assets/Svg/login-image.svg', height: 52),
          const SizedBox(
            height: 20,
          ),
          Text(
            'You don\'t have any chats yet',
            style: TextStyle(
                fontFamily: 'Montserrat',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: themeData.colorScheme.onBackground),
          )
        ],
      ),
    );
  }
}
