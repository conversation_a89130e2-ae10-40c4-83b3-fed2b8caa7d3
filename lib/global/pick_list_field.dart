import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PickListField extends StatefulWidget {
  final List<int> listItems;
  final String title;
  final String selectedValue;
  final Function(int) callback;
  const PickListField(
      {super.key,
      required this.listItems,
      required this.title,
      required this.callback,
      required this.selectedValue});

  @override
  State<PickListField> createState() => _PickListFieldState();
}

class _PickListFieldState extends State<PickListField> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return GestureDetector(
      onTap: () async {
        await bottomSheetBuilder(themeData);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                fontFamily: 'SpaceG',
                color: themeData.colorScheme.onBackground),
          ),
          const SizedBox(
            height: 8,
          ),
          Container(
            decoration: BoxDecoration(
                border: Border.all(color: themeData.colorScheme.onBackground)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    widget.selectedValue,
                    style: TextStyle(
                        fontFamily: 'SpaceG',
                        fontSize: 16,
                        fontWeight: FontWeight.w800,
                        color: themeData.colorScheme.onBackground),
                  ),
                ),
                const Spacer(),
                SizedBox(
                    height: 44,
                    width: 44,
                    child: SvgPicture.asset('Assets/Svg/drop.svg'))
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future bottomSheetBuilder(ThemeData themeData) async {
    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return ListView.builder(
              padding: const EdgeInsets.all(24),
              itemCount: widget.listItems.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    widget.callback(widget.listItems[index]);
                    Navigator.pop(context);
                  },
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        widget.listItems[index].toString(),
                        style: TextStyle(
                            fontFamily: 'SpaceG',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: themeData.colorScheme.onBackground),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                );
              });
        });
  }
}
