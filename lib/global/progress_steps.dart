import 'package:flutter/material.dart';

class StepsIndicator extends StatefulWidget {
  final int stepCount;
  final Color? completedColor;
  final Color? incompleteColor;
  final int stepCompleted;

  const StepsIndicator(
      {super.key,
      required this.stepCount,
      this.completedColor,
      this.incompleteColor,
      required this.stepCompleted});

  @override
  State<StepsIndicator> createState() => _StepsIndicatorState();
}

class _StepsIndicatorState extends State<StepsIndicator> {
  @override
  Widget build(BuildContext context) {
    var theme = Theme.of(context);
    return Container(
      alignment: Alignment.center,
      height: 6,
      width: MediaQuery.of(context).size.width,
      child: ListView.builder(
          itemCount: widget.stepCount,
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Container(
                width:
                    MediaQuery.of(context).size.width / (widget.stepCount + 1),
                decoration: BoxDecoration(
                    color: widget.stepCompleted > index
                        ? widget.completedColor ?? theme.colorScheme.primary
                        : widget.incompleteColor ?? theme.colorScheme.surface,
                    borderRadius: BorderRadius.circular(20)),
              ),
            );
          }),
    );
  }
}
