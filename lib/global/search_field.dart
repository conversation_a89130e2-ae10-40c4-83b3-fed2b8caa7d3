import 'package:flutter/material.dart';

class SearchField extends StatefulWidget {
  final TextEditingController controller;
  final Widget label;
  final String? hint;
  final Widget? prefixWidget;
  final bool isSearchField;
  final List<String>? searchResults;
  final Function(String) onChanged;
  final Function(String)? onSubmitted;
  final Function(String)? callback;
  final bool isLoading;
  const SearchField({
    super.key,
    required this.controller,
    required this.label,
    required this.onChanged,
    required this.isLoading,
    required this.isSearchField,
    this.callback,
    this.onSubmitted,
    this.searchResults,
    this.prefixWidget,
    this.hint,
  });

  @override
  State<SearchField> createState() => _SearchFieldState();
}

class _SearchFieldState extends State<SearchField> {
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Column(
      children: [
        TextFormField(
          controller: widget.controller,
          cursorColor: theme.colorScheme.primary,
          decoration: InputDecoration(
            label: widget.label,
            prefix: widget.prefixWidget,
            suffixIcon: widget.controller.text != ''
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        widget.controller.text = '';
                      });
                    },
                    child: Icon(
                      Icons.highlight_remove_rounded,
                      color: theme.colorScheme.primary,
                    ))
                : const SizedBox(),
            filled: true,
            fillColor: theme.colorScheme.surface,
            labelStyle: TextStyle(
                fontFamily: 'Montserrat',
                fontWeight: FontWeight.w300,
                fontSize: 14,
                color: theme.colorScheme.primary),
            hintText: widget.hint ?? '',
            hintStyle: TextStyle(
                fontFamily: 'Montserrat',
                fontWeight: FontWeight.w300,
                fontSize: 14,
                color: theme.colorScheme.primary.withAlpha(100)),
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                    width: 1, color: theme.colorScheme.primary.withAlpha(60)),
                borderRadius: BorderRadius.circular(15)),
            focusedBorder: OutlineInputBorder(
                borderSide:
                    BorderSide(width: 1, color: theme.colorScheme.primary),
                borderRadius: BorderRadius.circular(15)),
          ),
          style: TextStyle(
              fontFamily: 'Montserrat',
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: theme.colorScheme.onSurface),
          onFieldSubmitted: (value) {
            widget.onSubmitted!(value);
            // widget.controller.text != '' || widget.isSearchField != false ? searchResults(theme) : {};
          },
          onChanged: (value) {
            widget.onChanged(value);
            // widget.controller.text != '' || widget.isSearchField != false ? searchResults(theme) : {};
          },
        ),
        // widget.searchResults != null ? const SizedBox(height: 16,) : const SizedBox(),
        // widget.controller.text != '' || widget.isSearchField != false ? searchResults(theme) : const SizedBox()
      ],
    );
  }

  // Widget searchResults(ThemeData theme){
  //   return Container(
  //     alignment: widget.isLoading == false ? Alignment.topLeft : Alignment.center,
  //       constraints: BoxConstraints(
  //         maxHeight: widget.searchResults!.length >= 8 ? 300 : double.infinity,
  //       ),
  //       decoration: BoxDecoration(
  //           color: theme.colorScheme.surface,
  //           borderRadius: BorderRadius.circular(15),
  //           border: Border.all(width: 1, color: theme.colorScheme.outline)
  //       ),
  //       child: widget.isLoading == false ? SingleChildScrollView(
  //         child: ListView.builder(
  //             itemCount: widget.searchResults?.length,
  //             shrinkWrap: true,
  //             padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
  //             scrollDirection: Axis.vertical,
  //             physics: const NeverScrollableScrollPhysics(),
  //             itemBuilder: (BuildContext context, int index){
  //               return Column(
  //                 mainAxisAlignment: MainAxisAlignment.start,
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   const SizedBox(height: 20,),
  //                   GestureDetector(
  //                     onTap: () {
  //                       setState(() {
  //                         widget.callback?.call(widget.searchResults![index]);
  //                         widget.controller.text = '';
  //                         widget.searchResults?.clear();
  //                       });
  //                     },
  //                     child: Text(
  //                       widget.searchResults![index],
  //                       style: TextStyle(
  //                         color: theme.colorScheme.surface,
  //                         fontSize: 16,
  //                         fontFamily: 'Montserrat',
  //                       ),
  //                     ),
  //                   ),
  //                   const SizedBox(height: 12,),
  //                   index != widget.searchResults!.length-1 ?
  //                   Divider(height: 2, color: theme.colorScheme.outline.withAlpha(100),) : const SizedBox()
  //                 ],
  //               );
  //             }
  //         ),
  //       ) : Padding(
  //         padding: const EdgeInsets.all(16),
  //           child: CircularProgressIndicator(color: theme.colorScheme.primary, strokeWidth: 4, backgroundColor: theme.colorScheme.primaryContainer,))
  //   );
  // }
}
