import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hexcolor/hexcolor.dart';

class SelectableContainerList extends StatefulWidget {
  final String label;
  final TextStyle? style;
  final bool isSelected;
  final bool showIcon;
  final String? iconPath;
  final Function() onTap;
  const SelectableContainerList(
      {super.key,
      required this.label,
      required this.isSelected,
      required this.showIcon,
      this.style,
      this.iconPath,
      required this.onTap});

  @override
  State<SelectableContainerList> createState() =>
      _SelectableContainerListState();
}

class _SelectableContainerListState extends State<SelectableContainerList>
    with TickerProviderStateMixin {
  late AnimationController animationController;
  double scale = 1;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    animationController = AnimationController(
        vsync: this,
        upperBound: 1,
        lowerBound: 0.95,
        value: 1,
        duration: const Duration(milliseconds: 100));

    animationController.addListener(() {
      setState(() {
        scale = animationController.value;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        await animationController.reverse();
        await animationController.forward();
        widget.onTap();
      },
      child: Transform.scale(
        scale: scale,
        child: Container(
          width: MediaQuery.of(context).size.width,
          alignment: Alignment.center,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color:
                  widget.isSelected ? HexColor("#FFC20D") : HexColor("#070A0A"),
              border: Border.all(
                  color: widget.isSelected
                      ? HexColor("#FFC20D")
                      : HexColor("#222A2A"))),
          child: Column(
            children: [
              widget.showIcon
                  ? SvgPicture.asset(
                      widget.iconPath ?? '',
                      height: 44,
                      width: 44,
                    )
                  : const SizedBox(),
              widget.showIcon
                  ? const SizedBox(
                      height: 16,
                    )
                  : const SizedBox(),
              Text(
                widget.label,
                style: widget.style ??
                    TextStyle(
                        fontSize: 20,
                        color: widget.isSelected
                            ? HexColor("#0B0F0F")
                            : HexColor("#FFC20D"),
                        fontFamily: 'Montserrat',
                        fontWeight: FontWeight.w700),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
