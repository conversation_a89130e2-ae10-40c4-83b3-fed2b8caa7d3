import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomSnackBar {
  final BuildContext context;
  final String iconPath;
  final String message;
  final Color backgroundColor;
  final Color textColor;
  const CustomSnackBar(
      {required this.context,
      required this.iconPath,
      required this.backgroundColor,
      required this.textColor,
      required this.message});

  void showCustomSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: backgroundColor,
        content: Row(
          children: [
            SvgPicture.asset(
              iconPath,
              height: 32,
              width: 32,
            ),
            const SizedBox(
              width: 12,
            ),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                    color: textColor,
                    fontSize: 16,
                    fontFamily: 'Mukta',
                    fontWeight: FontWeight.w600),
              ),
            )
          ],
        )));
  }
}
