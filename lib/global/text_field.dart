import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';

class CustomTextField extends StatefulWidget {
  final TextEditingController controller;
  final Widget label;
  final String? hint;
  final int? maxLength;
  final double? fontSize;
  final Widget? prefixWidget;
  final bool? showSuffixIcon;
  final TextAlign? textAlign;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextInputType? inputType;
  final Function(String) onChanged;
  final Function(String)? onSubmitted;
  final Function(String)? callback;
  final bool isLoading;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.label,
    required this.onChanged,
    required this.isLoading,
    this.textInputAction,
    this.textAlign,
    this.fontSize,
    this.maxLength,
    this.focusNode,
    this.inputType,
    this.showSuffixIcon,
    this.callback,
    this.onSubmitted,
    this.prefixWidget,
    this.hint,
  });

  @override
  State<CustomTextField> createState() => _CustomTextField();
}

class _CustomTextField extends State<CustomTextField> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return TextFormField(
      controller: widget.controller,
      cursorColor: themeData.colorScheme.primary,
      focusNode: widget.focusNode ?? FocusNode(),
      textAlign: widget.textAlign ?? TextAlign.left,
      keyboardType: widget.inputType ?? TextInputType.text,
      maxLength: widget.maxLength ?? 1000,
      textInputAction: widget.textInputAction ?? TextInputAction.done,
      decoration: InputDecoration(
        label: widget.label,
        prefixIcon: widget.prefixWidget,
        counterText: '',
        suffixIcon:
            widget.controller.text != '' && (widget.showSuffixIcon ?? true)
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        widget.controller.text = '';
                      });
                    },
                    child: Icon(
                      Icons.highlight_remove_rounded,
                      color: themeData.colorScheme.primary,
                    ))
                : null,
        filled: true,
        fillColor: themeData.colorScheme.primaryContainer,
        hintText: widget.hint ?? '',
        hintStyle: TextStyle(
            fontFamily: 'Montserrat',
            fontWeight: FontWeight.w300,
            fontSize: 14,
            color: HexColor("#4C5B5B")),
        enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
                width: 1,
                color: widget.controller.text.isNotEmpty
                    ? themeData.colorScheme.primary
                    : Colors.transparent),
            borderRadius: BorderRadius.circular(15)),
        focusedBorder: OutlineInputBorder(
            borderSide:
                BorderSide(width: 1, color: themeData.colorScheme.primary),
            borderRadius: BorderRadius.circular(15)),
      ),
      style: TextStyle(
          fontFamily: 'Montserrat',
          fontWeight: FontWeight.w600,
          fontSize: widget.fontSize ?? 16,
          color: themeData.colorScheme.primary),
      onFieldSubmitted: (value) {
        widget.onSubmitted!(value);
      },
      onChanged: (value) {
        widget.onChanged(value);
      },
    );
  }
}
