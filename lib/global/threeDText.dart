import 'package:flutter/material.dart';

class ThreeDTextWidget extends StatelessWidget {
  final String text;
  final double fontSize;
  final Color textColor;
  final Color borderColor;
  final Color shadowColor;
  final double strokeWidth;
  final double depthOffset;

  const ThreeDTextWidget({
    Key? key,
    required this.text,
    this.fontSize = 26.0,
    this.textColor = const Color.fromARGB(255, 254, 242, 0),
    this.borderColor = Colors.black,
    this.shadowColor = Colors.black,
    this.strokeWidth = 4.0,
    this.depthOffset = 3.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // First shadow for 3D effect
        Transform.translate(
          offset: Offset(depthOffset, depthOffset),
          child: Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: shadowColor,
            ),
          ),
        ),
        // Second shadow for deeper effect
        Transform.translate(
          offset: Offset(depthOffset - 1, depthOffset - 1),
          child: Text(
            text,
            style: TextStyle(
              fontSize: fontSize + 1,
              fontWeight: FontWeight.bold,
              color: shadowColor,
            ),
          ),
        ),
        // Text outline (border)
        Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = strokeWidth
              ..color = borderColor,
          ),
        ),
        // Main text color (top layer)
        Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ],
    );
  }
}
