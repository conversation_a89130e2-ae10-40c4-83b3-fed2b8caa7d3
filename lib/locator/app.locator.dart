import 'package:get_it/get_it.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombarservice.dart';

import 'package:bjpnew/screens/template/templates_viewModel.dart';

import 'package:bjpnew/services/imagesizeservice.dart';
import 'package:bjpnew/services/leaderPhotoservice.dart';
import 'package:bjpnew/services/test.dart';
import 'package:bjpnew/services/textsizeservice.dart';

final locator = GetIt.instance;

void setupLocator() {
  locator.registerLazySingleton(() => UserService());

  locator.registerLazySingleton<ImageSizeService>(() => ImageSizeService());
  locator.registerFactory<TemplatesViewModel>(() => TemplatesViewModel());

  locator.registerLazySingleton(() => TextSizeService());

  locator.registerLazySingleton(() => ImageService());

  locator.registerLazySingleton(() => BottomBarService());
}
