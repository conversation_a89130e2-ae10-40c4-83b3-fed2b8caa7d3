import 'dart:convert';
import 'dart:developer';

import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/services/local_notification_manager.dart';
import 'package:bjpnew/services/referrer_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:http/http.dart' as http;
import 'package:bjpnew/locator/app.locator.dart';

import 'package:bjpnew/services/notification_service.dart';
import 'package:bjpnew/route/route.dart';
import 'package:bjpnew/theme/theme_setup.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked_themes/stacked_themes.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:bjpnew/utils/Singletons/prefs_singleton.dart';
import 'package:timezone/data/latest.dart' as db;
import 'package:timezone/timezone.dart' as tz;
import 'package:android_play_install_referrer/android_play_install_referrer.dart';
// Import necessary packages and files

late LocalNotificationControllerManager localNotifManager;
late String referrerGlobal = "";

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await ThemeManager.initialise();
  await Firebase.initializeApp();
  await FlutterDownloader.initialize();
  await PushNotificationService().setupInteractedMessage();
  await Prefs.init();
  await FacebookAppEvents().logCompletedRegistration();

  //NOTIFICATIONS local Initilization for bday..
  db.initializeTimeZones();
  tz.setLocalLocation(tz.getLocation('Asia/Kolkata'));
  await LocalNotificationControllerManager.initializeNotifications();

  setupLocator();

  final prefs = await SharedPreferences.getInstance();
  final String state = prefs.getString('CategorySelected') ?? '';
  final configUrl = Uri.parse(
      'https://backend.designboxconsuting.com/poster/config/v1/config?state=${state}');
  try {
    final response = await http.get(configUrl);
    if (response.statusCode == 200) {
      await Prefs.instance.setString('config', response.body);
      print("config");
      print(response.body);
      var res = jsonDecode(response.body);
      final prefs = await SharedPreferences.getInstance();
      final String? token = prefs.getString('token');
      final String? userId = prefs.getString('userId');

      await prefs.setString('shareLink', res["shareLink"]);
      await prefs.setBool('forceUpdate', res["forceUpdate"]);

      print(res["forceUpdate"]);
      if (res["popupMessage"] != null) {
        await prefs.setBool('popupMessage', true);
      }
      print(userId);
      print(token);
    } else {
      // Handle error
      print('Failed to load config: ${response.statusCode}');
    }
  } catch (e) {
    // Handle network error
    print('Failed to load config: $e');
  }

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({
    Key? key,
  }) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late bool isAuthenticated;
  final appRouter = AutoRouter();
  final navigatorKey = GlobalKey<NavigatorState>();
  final ReferrerService _referrerService = ReferrerService();

  @override
  void initState() {
    super.initState();
    _initializeReferrer();
  }

  Future<void> _initializeReferrer() async {
    referrerGlobal = await _referrerService.fetchReferrerData();
  }

  @override
  Widget build(BuildContext context) {
    return ThemeBuilder(
      themes: getThemes(),
      builder: (context, regularTheme, darkTheme, themeMode) =>
          MaterialApp.router(
        routerConfig: appRouter.config(),
        debugShowCheckedModeBanner: false,
        themeMode: themeMode,
        theme: regularTheme,
        darkTheme: darkTheme,
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaleFactor: 0.75),
            //  data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
            child: child!,
          );
        },
      ),
    );
  }
}
