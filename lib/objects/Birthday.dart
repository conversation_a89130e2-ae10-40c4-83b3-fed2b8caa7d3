import 'dart:convert';

class Birthday {
  final int id;
  final String? posterId;
  final String? name;
  final String? imagePath;
  final DateTime? dob;

  Birthday({
    int? id,
    this.posterId,
    required this.name,
    required this.imagePath,
    required this.dob,
  }) : id = id ?? _generateId(name, dob);

  static int _generateId(String? name, DateTime? dob) {
    final String base = '${name ?? ''}-${dob?.toIso8601String() ?? ''}';
    return base.hashCode;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'posterId': posterId,
      'name': name,
      'imagePath': imagePath,
      'dob': dob?.toIso8601String(),
    };
  }

  static Birthday fromJson(Map<String, dynamic> json) {
    return Birthday(
      id: json['id'],
      posterId: json['posterId'],
      name: json['name'],
      imagePath: json['imagePath'],
      dob: json['dob'] != null ? DateTime.parse(json['dob']) : null,
    );
  }

  factory Birthday.fromAPI(Map<String, dynamic> json) {
    return Birthday(
      posterId: json["posterId"],
      name: json["name"],
      imagePath: json["url"],
      dob: json["dob"] != null
          ? DateTime.fromMillisecondsSinceEpoch(json["dob"])
          : null,
    );
  }

  static String encode(List<Birthday> birthdays) => json.encode(
        birthdays
            .map<Map<String, dynamic>>((birthday) => birthday.toJson())
            .toList(),
      );

  static List<Birthday> decode(String birthdays) =>
      (json.decode(birthdays) as List<dynamic>)
          .map<Birthday>((item) => Birthday.fromJson(item))
          .toList();

  Birthday copyWith({
    int? id,
    String? posterId,
    String? name,
    String? imagePath,
    DateTime? dob,
  }) {
    return Birthday(
      id: id ?? this.id,
      posterId: posterId ?? this.posterId,
      name: name ?? this.name,
      imagePath: imagePath ?? this.imagePath,
      dob: dob ?? this.dob,
    );
  }
}
