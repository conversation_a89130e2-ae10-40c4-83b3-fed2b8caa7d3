// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i16;
import 'package:bjpnew/screens/bottombar.dart/bottombar.dart' as _i6;
import 'package:bjpnew/screens/bottombar.dart/create_poster.dart' as _i2;
import 'package:bjpnew/screens/create-birthday-poster/create-birthday-poster.dart'
    as _i1;
import 'package:bjpnew/screens/edit-profile-mvvm/edit_profile_view.dart' as _i3;
import 'package:bjpnew/screens/home-mvvm/home_view.dart' as _i4;
import 'package:bjpnew/screens/login-mvvm/login-view.dart' as _i5;
import 'package:bjpnew/screens/notification_mvvm/notification_view.dart' as _i7;
import 'package:bjpnew/screens/onboarding-mvvm/onboarding_view.dart' as _i8;
import 'package:bjpnew/screens/onboarding-mvvm/select_party_view.dart' as _i13;
import 'package:bjpnew/screens/party-list/party_list_view.dart' as _i9;
import 'package:bjpnew/screens/payment-init-mvvm/payment_init_view.dart'
    as _i10;
import 'package:bjpnew/screens/premium-screen-mvvm/phonepe_premiumview.dart'
    as _i11;
import 'package:bjpnew/screens/profile-mvvm/profile_view.dart' as _i12;
import 'package:bjpnew/screens/settings-mvvm/settings_view.dart' as _i14;
import 'package:bjpnew/screens/upi-mvvm/upi-view.dart' as _i15;
import 'package:flutter/material.dart' as _i17;

abstract class $AutoRouter extends _i16.RootStackRouter {
  $AutoRouter({super.navigatorKey});

  @override
  final Map<String, _i16.PageFactory> pagesMap = {
    BirthdayPosterViewRoute.name: (routeData) {
      final args = routeData.argsAs<BirthdayPosterViewRouteArgs>(
          orElse: () => const BirthdayPosterViewRouteArgs());
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i1.BirthdayPosterView(
          key: args.key,
          pageIndex: args.pageIndex,
        ),
      );
    },
    CreatePosterViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i2.CreatePosterView(),
      );
    },
    EditProfileViewRoute.name: (routeData) {
      final args = routeData.argsAs<EditProfileViewRouteArgs>(
          orElse: () => const EditProfileViewRouteArgs());
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i3.EditProfileView(
          key: args.key,
          onDetailsSaved: args.onDetailsSaved,
          isHome: args.isHome,
        ),
      );
    },
    HomeViewRoute.name: (routeData) {
      final args = routeData.argsAs<HomeViewRouteArgs>(
          orElse: () => const HomeViewRouteArgs());
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i4.HomeView(
          key: args.key,
          isBottomsheet: args.isBottomsheet,
        ),
      );
    },
    LoginViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i5.LoginView(),
      );
    },
    MyBottomBarViewRoute.name: (routeData) {
      final args = routeData.argsAs<MyBottomBarViewRouteArgs>(
          orElse: () => const MyBottomBarViewRouteArgs());
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i6.MyBottomBarView(
          key: args.key,
          showBottomCard: args.showBottomCard,
        ),
      );
    },
    NotificationViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i7.NotificationView(),
      );
    },
    OnboardingDetailsViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i8.OnboardingDetailsView(),
      );
    },
    PartyListViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i9.PartyListView(),
      );
    },
    PaymentInitViewRoute.name: (routeData) {
      final args = routeData.argsAs<PaymentInitViewRouteArgs>();
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i10.PaymentInitView(
          key: args.key,
          packageId: args.packageId,
          targetApp: args.targetApp,
        ),
      );
    },
    PremiumViewRoute.name: (routeData) {
      final args = routeData.argsAs<PremiumViewRouteArgs>();
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i11.PremiumView(
          key: args.key,
          imageUrl: args.imageUrl,
          isPoster: args.isPoster,
          isBirthday: args.isBirthday,
          birthdayName: args.birthdayName,
          birthdayPhoto: args.birthdayPhoto,
          isTestUser: args.isTestUser,
        ),
      );
    },
    ProfileViewRoute.name: (routeData) {
      final args = routeData.argsAs<ProfileViewRouteArgs>();
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i12.ProfileView(
          key: args.key,
          onProfileDetailsChange: args.onProfileDetailsChange,
        ),
      );
    },
    SelectPartyViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i13.SelectPartyView(),
      );
    },
    SettingsViewRoute.name: (routeData) {
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const _i14.SettingsView(),
      );
    },
    UPIViewRoute.name: (routeData) {
      final args = routeData.argsAs<UPIViewRouteArgs>();
      return _i16.AutoRoutePage<dynamic>(
        routeData: routeData,
        child: _i15.UPIView(
          key: args.key,
          duration: args.duration,
          amount: args.amount,
          packageId: args.packageId,
          apiKey: args.apiKey,
        ),
      );
    },
  };
}

/// generated route for
/// [_i1.BirthdayPosterView]
class BirthdayPosterViewRoute
    extends _i16.PageRouteInfo<BirthdayPosterViewRouteArgs> {
  BirthdayPosterViewRoute({
    _i17.Key? key,
    int? pageIndex,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          BirthdayPosterViewRoute.name,
          args: BirthdayPosterViewRouteArgs(
            key: key,
            pageIndex: pageIndex,
          ),
          initialChildren: children,
        );

  static const String name = 'BirthdayPosterViewRoute';

  static const _i16.PageInfo<BirthdayPosterViewRouteArgs> page =
      _i16.PageInfo<BirthdayPosterViewRouteArgs>(name);
}

class BirthdayPosterViewRouteArgs {
  const BirthdayPosterViewRouteArgs({
    this.key,
    this.pageIndex,
  });

  final _i17.Key? key;

  final int? pageIndex;

  @override
  String toString() {
    return 'BirthdayPosterViewRouteArgs{key: $key, pageIndex: $pageIndex}';
  }
}

/// generated route for
/// [_i2.CreatePosterView]
class CreatePosterViewRoute extends _i16.PageRouteInfo<void> {
  const CreatePosterViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          CreatePosterViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreatePosterViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i3.EditProfileView]
class EditProfileViewRoute
    extends _i16.PageRouteInfo<EditProfileViewRouteArgs> {
  EditProfileViewRoute({
    _i17.Key? key,
    void Function()? onDetailsSaved,
    bool? isHome,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          EditProfileViewRoute.name,
          args: EditProfileViewRouteArgs(
            key: key,
            onDetailsSaved: onDetailsSaved,
            isHome: isHome,
          ),
          initialChildren: children,
        );

  static const String name = 'EditProfileViewRoute';

  static const _i16.PageInfo<EditProfileViewRouteArgs> page =
      _i16.PageInfo<EditProfileViewRouteArgs>(name);
}

class EditProfileViewRouteArgs {
  const EditProfileViewRouteArgs({
    this.key,
    this.onDetailsSaved,
    this.isHome,
  });

  final _i17.Key? key;

  final void Function()? onDetailsSaved;

  final bool? isHome;

  @override
  String toString() {
    return 'EditProfileViewRouteArgs{key: $key, onDetailsSaved: $onDetailsSaved, isHome: $isHome}';
  }
}

/// generated route for
/// [_i4.HomeView]
class HomeViewRoute extends _i16.PageRouteInfo<HomeViewRouteArgs> {
  HomeViewRoute({
    _i17.Key? key,
    bool? isBottomsheet,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          HomeViewRoute.name,
          args: HomeViewRouteArgs(
            key: key,
            isBottomsheet: isBottomsheet,
          ),
          initialChildren: children,
        );

  static const String name = 'HomeViewRoute';

  static const _i16.PageInfo<HomeViewRouteArgs> page =
      _i16.PageInfo<HomeViewRouteArgs>(name);
}

class HomeViewRouteArgs {
  const HomeViewRouteArgs({
    this.key,
    this.isBottomsheet,
  });

  final _i17.Key? key;

  final bool? isBottomsheet;

  @override
  String toString() {
    return 'HomeViewRouteArgs{key: $key, isBottomsheet: $isBottomsheet}';
  }
}

/// generated route for
/// [_i5.LoginView]
class LoginViewRoute extends _i16.PageRouteInfo<void> {
  const LoginViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          LoginViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i6.MyBottomBarView]
class MyBottomBarViewRoute
    extends _i16.PageRouteInfo<MyBottomBarViewRouteArgs> {
  MyBottomBarViewRoute({
    _i17.Key? key,
    bool? showBottomCard,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          MyBottomBarViewRoute.name,
          args: MyBottomBarViewRouteArgs(
            key: key,
            showBottomCard: showBottomCard,
          ),
          initialChildren: children,
        );

  static const String name = 'MyBottomBarViewRoute';

  static const _i16.PageInfo<MyBottomBarViewRouteArgs> page =
      _i16.PageInfo<MyBottomBarViewRouteArgs>(name);
}

class MyBottomBarViewRouteArgs {
  const MyBottomBarViewRouteArgs({
    this.key,
    this.showBottomCard,
  });

  final _i17.Key? key;

  final bool? showBottomCard;

  @override
  String toString() {
    return 'MyBottomBarViewRouteArgs{key: $key, showBottomCard: $showBottomCard}';
  }
}

/// generated route for
/// [_i7.NotificationView]
class NotificationViewRoute extends _i16.PageRouteInfo<void> {
  const NotificationViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          NotificationViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i8.OnboardingDetailsView]
class OnboardingDetailsViewRoute extends _i16.PageRouteInfo<void> {
  const OnboardingDetailsViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          OnboardingDetailsViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'OnboardingDetailsViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i9.PartyListView]
class PartyListViewRoute extends _i16.PageRouteInfo<void> {
  const PartyListViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          PartyListViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'PartyListViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i10.PaymentInitView]
class PaymentInitViewRoute
    extends _i16.PageRouteInfo<PaymentInitViewRouteArgs> {
  PaymentInitViewRoute({
    _i17.Key? key,
    required String packageId,
    required String? targetApp,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          PaymentInitViewRoute.name,
          args: PaymentInitViewRouteArgs(
            key: key,
            packageId: packageId,
            targetApp: targetApp,
          ),
          initialChildren: children,
        );

  static const String name = 'PaymentInitViewRoute';

  static const _i16.PageInfo<PaymentInitViewRouteArgs> page =
      _i16.PageInfo<PaymentInitViewRouteArgs>(name);
}

class PaymentInitViewRouteArgs {
  const PaymentInitViewRouteArgs({
    this.key,
    required this.packageId,
    required this.targetApp,
  });

  final _i17.Key? key;

  final String packageId;

  final String? targetApp;

  @override
  String toString() {
    return 'PaymentInitViewRouteArgs{key: $key, packageId: $packageId, targetApp: $targetApp}';
  }
}

/// generated route for
/// [_i11.PremiumView]
class PremiumViewRoute extends _i16.PageRouteInfo<PremiumViewRouteArgs> {
  PremiumViewRoute({
    _i17.Key? key,
    required String imageUrl,
    required bool isPoster,
    bool? isBirthday,
    String? birthdayName,
    String? birthdayPhoto,
    bool isTestUser = false,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          PremiumViewRoute.name,
          args: PremiumViewRouteArgs(
            key: key,
            imageUrl: imageUrl,
            isPoster: isPoster,
            isBirthday: isBirthday,
            birthdayName: birthdayName,
            birthdayPhoto: birthdayPhoto,
            isTestUser: isTestUser,
          ),
          initialChildren: children,
        );

  static const String name = 'PremiumViewRoute';

  static const _i16.PageInfo<PremiumViewRouteArgs> page =
      _i16.PageInfo<PremiumViewRouteArgs>(name);
}

class PremiumViewRouteArgs {
  const PremiumViewRouteArgs({
    this.key,
    required this.imageUrl,
    required this.isPoster,
    this.isBirthday,
    this.birthdayName,
    this.birthdayPhoto,
    this.isTestUser = false,
  });

  final _i17.Key? key;

  final String imageUrl;

  final bool isPoster;

  final bool? isBirthday;

  final String? birthdayName;

  final String? birthdayPhoto;

  final bool isTestUser;

  @override
  String toString() {
    return 'PremiumViewRouteArgs{key: $key, imageUrl: $imageUrl, isPoster: $isPoster, isBirthday: $isBirthday, birthdayName: $birthdayName, birthdayPhoto: $birthdayPhoto, isTestUser: $isTestUser}';
  }
}

/// generated route for
/// [_i12.ProfileView]
class ProfileViewRoute extends _i16.PageRouteInfo<ProfileViewRouteArgs> {
  ProfileViewRoute({
    _i17.Key? key,
    required void Function() onProfileDetailsChange,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          ProfileViewRoute.name,
          args: ProfileViewRouteArgs(
            key: key,
            onProfileDetailsChange: onProfileDetailsChange,
          ),
          initialChildren: children,
        );

  static const String name = 'ProfileViewRoute';

  static const _i16.PageInfo<ProfileViewRouteArgs> page =
      _i16.PageInfo<ProfileViewRouteArgs>(name);
}

class ProfileViewRouteArgs {
  const ProfileViewRouteArgs({
    this.key,
    required this.onProfileDetailsChange,
  });

  final _i17.Key? key;

  final void Function() onProfileDetailsChange;

  @override
  String toString() {
    return 'ProfileViewRouteArgs{key: $key, onProfileDetailsChange: $onProfileDetailsChange}';
  }
}

/// generated route for
/// [_i13.SelectPartyView]
class SelectPartyViewRoute extends _i16.PageRouteInfo<void> {
  const SelectPartyViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          SelectPartyViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'SelectPartyViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i14.SettingsView]
class SettingsViewRoute extends _i16.PageRouteInfo<void> {
  const SettingsViewRoute({List<_i16.PageRouteInfo>? children})
      : super(
          SettingsViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'SettingsViewRoute';

  static const _i16.PageInfo<void> page = _i16.PageInfo<void>(name);
}

/// generated route for
/// [_i15.UPIView]
class UPIViewRoute extends _i16.PageRouteInfo<UPIViewRouteArgs> {
  UPIViewRoute({
    _i17.Key? key,
    required String duration,
    required int amount,
    required String packageId,
    required String apiKey,
    List<_i16.PageRouteInfo>? children,
  }) : super(
          UPIViewRoute.name,
          args: UPIViewRouteArgs(
            key: key,
            duration: duration,
            amount: amount,
            packageId: packageId,
            apiKey: apiKey,
          ),
          initialChildren: children,
        );

  static const String name = 'UPIViewRoute';

  static const _i16.PageInfo<UPIViewRouteArgs> page =
      _i16.PageInfo<UPIViewRouteArgs>(name);
}

class UPIViewRouteArgs {
  const UPIViewRouteArgs({
    this.key,
    required this.duration,
    required this.amount,
    required this.packageId,
    required this.apiKey,
  });

  final _i17.Key? key;

  final String duration;

  final int amount;

  final String packageId;

  final String apiKey;

  @override
  String toString() {
    return 'UPIViewRouteArgs{key: $key, duration: $duration, amount: $amount, packageId: $packageId, apiKey: $apiKey}';
  }
}
