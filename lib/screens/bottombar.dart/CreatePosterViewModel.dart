import 'dart:convert';

import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/services/birthday_service.dart';
import 'package:bjpnew/services/user_db_operations.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:stacked/stacked.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class CreatePosterViewModel extends BaseViewModel {
  //  String? selectedImagePath;
  List<String> localImages = [];
  // List<String> localBirthday = ['Asset/Images/birthday.jpeg'];
  List<String> localBirthday = [];
  List<String> reversedImageData = [];
  List<String> reversedBirthDay = [];

  BirthdayService bday = BirthdayService();
  List<Birthday> birthday = []; //this contains only todays' birthday
  int? selectedBirthday = null;

  void setSelectedBirthday(int num) {
    selectedBirthday = num;
    notifyListeners();
  }

  bool _premiumStatus = false;
  bool get premiumStatus => _premiumStatus;
  set premiumStatus(value) {
    _premiumStatus = value;
    notifyListeners();
  }

  Future checkPremiumStatus() async {
    _premiumStatus = await UserDatabase().checkPremiumStatus() ?? false;
    print(_premiumStatus);
    print("premium user");
    notifyListeners();
  }

  CreatePosterViewModel() {
    onModelReady();
  }

  void onModelReady() async {
    await loadLocalImages();
    await checkPremiumStatus();
    await fetchPosterData();
    await fetchBirthdays();
  }

  Future<void> fetchBirthdays() async {
    var allBirthdays = await bday.fetchBirthdays();
    final today = DateTime.now();
    var todayBirthdays = allBirthdays.where((birthday) {
      return birthday.dob?.day == today.day &&
          birthday.dob?.month == today.month;
    }).toList();
    birthday = todayBirthdays;
    if (birthday.isEmpty) {
      selectedBirthday = null;
    }
    notifyListeners();
  }

  Future<void> loadLocalImages() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    localImages = prefs.getStringList('localImages') ?? [];
    reversedImageData = List.from(localImages.reversed);

    localBirthday = prefs.getStringList('localBirthday') ?? [];
    reversedBirthDay = List.from(localBirthday.reversed);
    notifyListeners();
  }

  void addImage(String imagePath) {
    localImages.add(imagePath);
    reversedImageData = List.from(localImages.reversed);
    saveImages();
    notifyListeners();
  }

  void addbirthImage(String imagePath) {
    localImages.add(imagePath);
    reversedImageData = List.from(localImages.reversed);
    saveImages();
    notifyListeners();
  }

  Future<void> savebirthImages() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setStringList('localBirthday', localBirthday);
  }

  Future<void> saveImages() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setStringList('localImages', localImages);
  }

  Future<void> loadImagesFromMemory() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    print('ggg  ${prefs.getStringList('localImages')}');
    localImages = prefs.getStringList('localImages') ?? [];
    reversedImageData = localImages.reversed.toList();
    notifyListeners();
  }

  Future<void> loadbirthImagesFromMemory() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    print('ggg  ${prefs.getStringList('localBirthday')}');
    localBirthday = prefs.getStringList('localBirthday') ?? [];
    reversedBirthDay = localBirthday.reversed.toList();
    notifyListeners();
  }

  Future<bool> isPopUpShown() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool ans = prefs.getString('isPopUpShown') == 'true';
    return ans;
  }

  Future<void> setPopUp(bool isPopUpShown) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('isPopUpShown', isPopUpShown ? 'true' : 'false');
  }

//fetches birthday poster data..
  Future<List<String>> fetchPosterData() async {
    final prefs = await SharedPreferences.getInstance();
    final String token = prefs.getString('token') ?? '';
    final String userId = prefs.getString('userId') ?? '';
    final String state = prefs.getString('CategorySelected') ?? '';

    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/v1/poster?state=${state}&categorId=category');
    final headers = {
      'token': token,
      'app-user-id': userId,
    };

    try {
      final response = await http.get(url, headers: headers);

      print(response.body);
      if (response.statusCode == 200) {
        print('Data fetched successfully');
        var data = json.decode(response.body);
        List<String> urls = (data['posts'] as List)
            .map((post) => post['url'] as String)
            .toList();
        localBirthday = urls;
        notifyListeners(); //update ui..
        print('Extracted URLs: $urls');
        return localBirthday;
      } else {
        print('Failed to fetch data: ${response.statusCode}');
        return localBirthday;
      }
    } catch (e) {
      print('Error: $e');
      return localBirthday;
    }
  }
}

class MakePosterViewModel extends BaseViewModel {
  String? selectedImagePath;
  final CreatePosterViewModel parentViewModel;

  MakePosterViewModel(this.parentViewModel);

  Future<void> pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      CroppedFile? croppedImage = await ImageCropper().cropImage(
        sourcePath: image.path,
        aspectRatioPresets: [
          CropAspectRatioPreset.square,
          CropAspectRatioPreset.ratio3x2,
          CropAspectRatioPreset.original,
          CropAspectRatioPreset.ratio4x3,
          CropAspectRatioPreset.ratio16x9,
        ],
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Crop Image',
            toolbarColor: Colors.deepOrange,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: false,
          ),
        ],
      );

      if (croppedImage != null) {
        selectedImagePath = croppedImage.path;
        notifyListeners();
      }
    }
  }

  Future<bool> uploadImage() async {
    if (selectedImagePath != null) {
      parentViewModel.addImage(selectedImagePath!);
      clearSelectedImage();
      return true;
    } else {
      return false;
    }
  }

  void clearSelectedImage() {
    selectedImagePath = null;
    notifyListeners();
  }
}
