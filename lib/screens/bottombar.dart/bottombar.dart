// ignore_for_file: must_be_immutable

import 'package:auto_route/auto_route.dart';

import 'package:flutter/material.dart';

import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombarviewmodel.dart';
import 'package:bjpnew/screens/bottombar.dart/create_poster.dart';

import 'package:bjpnew/screens/home-mvvm/home_view.dart';

import 'package:stacked/stacked.dart';

@RoutePage()
class MyBottomBarView extends StatefulWidget {
  MyBottomBarView({Key? key, this.showBottomCard}) : super(key: key);
  bool? showBottomCard;
  @override
  State<MyBottomBarView> createState() => _MyBottomBarViewState();
}

class _MyBottomBarViewState extends State<MyBottomBarView> {
  late PersistentTabController _bottomBarController;

  @override
  void initState() {
    super.initState();
    _bottomBarController = PersistentTabController(initialIndex: 0);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<MyBottomBarViewModel>.reactive(
        viewModelBuilder: () => MyBottomBarViewModel(),
        builder: (context, viewModel, child) => Scaffold(
              body: HomeView(),
              // body: PersistentTabView(
              //       context,
              //       controller: _bottomBarController,
              //       // confineInSafeArea: true,
              //       handleAndroidBackButtonPress: true,
              //       screens: buildScreens(),
              //       navBarHeight: 50,
              //       decoration: NavBarDecoration(
              //         borderRadius: BorderRadius.all(Radius.circular(10)),
              //         border: Border.all(color: Colors.black),
              //       ),
              //       items: _buildItems(),
              //       // bottomScreenMargin: MediaQuery.of(context).viewInsets.bottom > 0 ? 0 : 70,
              //       // resizeToAvoidBottomInset: false,
              //       onItemSelected: (value) =>
              //           {_bottomBarController.jumpToTab(value)},
              //       navBarStyle: NavBarStyle.style3,
              //       backgroundColor: Colors.white,
              //       // itemAnimationProperties: ItemAnimationProperties(
              //       //   duration: Duration(milliseconds: 200),
              //       //   curve: Curves.ease,
              //       // ),
              //       // screenTransitionAnimation: ScreenTransitionAnimation(
              //       //   animateTabTransition: true,
              //       //   curve: Curves.ease,
              //       //   duration: Duration(milliseconds: 200),
              //       // ),
              //     ),
            ));
  }

  List<Widget> buildScreens() {
    List<Widget> screens = [HomeView(), CreatePosterView()];
    return screens;
  }

  List<PersistentBottomNavBarItem> _buildItems() {
    List<PersistentBottomNavBarItem> items = [
      PersistentBottomNavBarItem(
        icon: Icon(Icons.home),
        contentPadding: 2,
        inactiveIcon: Icon(Icons.home),
        iconSize: 20,
        title: "Daily Posters",
        // textStyle: TextStyle(fontFamily: 'Gilroy-Bold', fontWeight: FontWeight.bold, fontSize: 11),
        activeColorPrimary: Colors.red,
        scrollToTopOnNavBarItemPress: false,
        inactiveColorPrimary: Colors.grey,
      ),
      PersistentBottomNavBarItem(
        contentPadding: 2,
        iconSize: 20,
        scrollToTopOnNavBarItemPress: false,
        icon: Icon(Icons.add),
        inactiveIcon: Icon(Icons.add),
        title: "Create Your Poster",
        // textStyle: TextStyle(fontFamily: 'Gilroy-Bold', fontWeight: FontWeight.bold, fontSize: 11),
        activeColorPrimary: Colors.red,
        inactiveColorPrimary: Colors.grey,
      ),
    ];
    return items;
  }
}

// @RoutePage()
// class MyBottomBarView extends StatefulWidget {
//    bool? showBottomCard;
//   MyBottomBarView({Key? key,   this.showBottomCard}) : super(key: key);

//   @override
//   State<MyBottomBarView> createState() => _MyBottomBarViewState();
// }

// class _MyBottomBarViewState extends State<MyBottomBarView> {
//   late PersistentTabController _bottomBarController;

//   @override
//   void initState() {
//     super.initState();
//     _bottomBarController = PersistentTabController(initialIndex: 0);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return ViewModelBuilder<MyBottomBarViewModel>.reactive(
//       viewModelBuilder: () => MyBottomBarViewModel(),
//       builder: (context, viewModel, child) {
//         return Scaffold(
//           body: Stack(
//             children: [
//               // This is the body content
//               buildScreens()[_bottomBarController.index],
//               // This is the bottom bar, shown conditionally based on the view model state
//               Visibility(
//                 visible: viewModel.isBottomBarVisible,
//                 child: PersistentTabView(
//                   context,
//                   controller: _bottomBarController,
//                   handleAndroidBackButtonPress: true,
//                   screens: buildScreens(),
//                   navBarHeight: 50,
//                   decoration: NavBarDecoration(
//                     borderRadius: BorderRadius.all(Radius.circular(10)),
//                     border: Border.all(color: Colors.black),
//                   ),
//                   items: _buildItems(),
//                   onItemSelected: (value) => _bottomBarController.jumpToTab(value),
//                   navBarStyle: NavBarStyle.style3,
//                   backgroundColor: Colors.white,
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   List<Widget> buildScreens() {
//     return [
//       HomeView(),
//       CreatePosterView(),
//     ];
//   }

//   List<PersistentBottomNavBarItem> _buildItems() {
//     return [
//       PersistentBottomNavBarItem(
//         icon: Icon(Icons.home),
//         contentPadding: 2,
//         inactiveIcon: Icon(Icons.home),
//         iconSize: 20,
//         title: "Daily Posters",
//         activeColorPrimary: Colors.red,
//         scrollToTopOnNavBarItemPress: false,
//         inactiveColorPrimary: Colors.grey,
//       ),
//       PersistentBottomNavBarItem(
//         contentPadding: 2,
//         iconSize: 20,
//         scrollToTopOnNavBarItemPress: false,
//         icon: Icon(Icons.add),
//         inactiveIcon: Icon(Icons.add),
//         title: "Create Your Poster",
//         activeColorPrimary: Colors.red,
//         inactiveColorPrimary: Colors.grey,
//       ),
//     ];
//   }
// }
