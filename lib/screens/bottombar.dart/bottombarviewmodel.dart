import 'package:bjpnew/locator/app.locator.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombarservice.dart';
import 'package:stacked/stacked.dart';

class MyBottomBarViewModel extends ReactiveViewModel {
  bool? showBottomCard;
  final BottomBarService _bottomBarService = locator<BottomBarService>();

  bool get isBottomBarVisible => _bottomBarService.isBottomBarVisible;

  void setBottomBarVisibility(bool isVisible) {
    _bottomBarService.setBottomBarVisibility(isVisible);
    notifyListeners(); // notify UI
  }

  @override
  List<ReactiveServiceMixin> get reactiveServices => [_bottomBarService];
}
