import 'dart:developer';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/screens/bottombar.dart/CreatePosterViewModel.dart';
import 'package:bjpnew/screens/bottombar.dart/friends_birthday.dart';
import 'package:bjpnew/screens/bottombar.dart/friends_birthday_remote.dart';
import 'package:bjpnew/screens/template/template_right_1.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:bjpnew/services/user_detail_service.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:stacked/stacked.dart';

@RoutePage()
class CreatePosterView extends StatefulWidget {
  const CreatePosterView({super.key});

  @override
  State createState() => _CreatePosterViewState();
}

class _CreatePosterViewState extends State<CreatePosterView> {
  bool isPoster = true;
  bool isBirthday = false;
  bool isFriendsBirthday = false;
  // List localBirthday = ['Asset/Images/ss.jpeg'];
  List localBirthday = [];
  final ScreenshotController _userScreenshotController = ScreenshotController();
  late UserDetailService userDetailService;
  var name = '';
  var dob = '';
  var userId = '';
  final CreatePosterViewModel _viewModel = CreatePosterViewModel();

  //template nameplate changes..
  TemplatesViewModel tempVm = TemplatesViewModel();
  final ScreenshotController _Stripcontroller = ScreenshotController();
  final GlobalKey _stripKey = GlobalKey();
  Color bottomColor = Colors.grey;

  @override
  void initState() {
    super.initState();
    userDetailService = UserDetailService();
    checkUserDetails();
    _viewModel.fetchPosterData();
  }

  Future<void> showDetailsDialog() async {
    final TextEditingController nameController =
        TextEditingController(text: name);
    final TextEditingController dobController =
        TextEditingController(text: dob);

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Enter Your Details'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Name'),
              ),
              TextField(
                controller: dobController,
                readOnly: true, // Prevent manual input
                decoration: const InputDecoration(
                  labelText: 'Date of Birth',
                  hintText: 'Pick your DOB',
                ),
                onTap: () async {
                  // Show the date picker
                  DateTime? selectedDate = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                  );

                  if (selectedDate != null) {
                    // Format the date and set it in the dobController
                    dobController.text = "${selectedDate.toLocal()}"
                        .split(' ')[0]; // Ex: 2023-01-01
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (userDetailService
                        .isValidString(nameController.text.trim()) &&
                    userDetailService
                        .isValidString(dobController.text.trim())) {
                  // Save details
                  userDetailService.saveUserDetails(userDetails: {
                    'name': nameController.text.trim(),
                    'dob': dobController.text.trim(),
                  });

                  // Update state
                  setState(() {
                    name = nameController.text.trim();
                    dob = dobController.text.trim();
                  });

                  Navigator.of(context).pop();
                } else {
                  // Show an error if inputs are invalid
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter valid details')),
                  );
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  Future<void> checkUserDetails() async {
    final userData = await userDetailService.getUserDetails();
    log("USERDATA => " + userData.toString());
    name = userData?['name'] ?? '';
    dob = userData?['dob'] ?? '';
    userId = userData?['userId'];
    log("USERDATA => Name - " + name + " DOB - " + dob);
    if (!userDetailService.isValidString(name) ||
        !userDetailService.isValidString(dob)) {
      showDetailsDialog();
    }
  }

  void _shareReferralLink(String userId, String name, String dob) {
    String baseUrl = "https://gumbotech.github.io/posterforb_referral/";

    String encodedName = Uri.encodeComponent(name);
    String encodedDob = Uri.encodeComponent(dob);
    String shareUrl =
        "$baseUrl?utm_source=organic&utm_medium=referral&userid=$userId&name=$encodedName&dob=$encodedDob";

    Share.share(shareUrl);
    log("Shared Link: $shareUrl");
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder.reactive(
      viewModelBuilder: () => _viewModel,
      onViewModelReady: (viewModel) async {
        await tempVm.initialize();
        viewModel.onModelReady();
      },
      builder: (context, viewModel, child) => Scaffold(
        appBar: AppBar(
          title: const Text('Create Poster'),
        ),
        floatingActionButton: isFriendsBirthday
            ? FloatingActionButton.extended(
                onPressed: () {
                  checkUserDetails().whenComplete(() {
                    _shareReferralLink(userId, name, dob);
                  });
                },
                label: Text(
                  'Refer your friend to\n add your birthday',
                  style: TextStyle(
                      fontSize: 16), // You can adjust the font size here
                ),
                icon: Icon(Icons.group_add_rounded),
              )
            : null,
        body: Column(
          children: [
            Container(
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Flexible(
                    flex: 1,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          isPoster = true;
                          isBirthday = false;
                          isFriendsBirthday = false;
                        });
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return Dialog(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Container(
                                width: MediaQuery.of(context).size.width * 0.6,
                                height:
                                    MediaQuery.of(context).size.height * 0.6,
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.white,
                                ),
                                child: Column(
                                  children: [
                                    Uploaddialog(parentViewModel: viewModel),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                      icon: const Icon(Icons.upload),
                      label: const Text(
                        'Make Your Poster',
                        maxLines: 2, // Limit the text to 2 lines
                        overflow: TextOverflow
                            .ellipsis, // Add ellipsis if text overflows
                      ),
                    ),
                  ),
                  // const SizedBox(width: 6), // Space between buttons
                  // Flexible(
                  //   flex: 8, // The second button takes more space
                  //   child: ElevatedButton.icon(
                  //     onPressed: () {
                  //       setState(() async {
                  //         isPoster = false;
                  //         isBirthday = true;
                  //         isFriendsBirthday = false;
                  //         await viewModel.fetchPosterData();
                  //       });
                  //     },
                  //     icon: const Icon(Icons.cake),
                  //     label: const Text(
                  //       'Make Birthday Poster',
                  //       maxLines: 2, // Limit the text to 2 lines
                  //       overflow: TextOverflow
                  //           .ellipsis, // Add ellipsis if text overflows
                  //     ),
                  //   ),
                  // ),
                  // const SizedBox(width: 6),
                  // Flexible(
                  //   flex: 8, // The third button takes up less space
                  //   child: ElevatedButton.icon(
                  //     onPressed: () {
                  //       setState(() {
                  //         isPoster = false;
                  //         isBirthday = false;
                  //         isFriendsBirthday = true;
                  //       });
                  //     },
                  //     icon: const Icon(Icons.group),
                  //     label: const Text(
                  //       "Friends' Bday",
                  //       maxLines: 2, // Limit the text to 2 lines
                  //       overflow: TextOverflow
                  //           .ellipsis, // Add ellipsis if text overflows
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
            if (isPoster) ...[
              _buildPosterContent(viewModel),
            ] else if (isBirthday) ...[
              _buildBirthdayContent(viewModel),
            ] else if (isFriendsBirthday) ...[
              // YourFriendsBdayView(
              //   isPremium: viewModel.premiumStatus,
              // ), // Display the new view
              YourFriendsBdayViewRemote(
                isPremium: viewModel.premiumStatus,
              ), // Display the new view
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPosterContent(CreatePosterViewModel viewModel) {
    return viewModel.localImages.isNotEmpty
        ? Expanded(
            child: ListView.builder(
              reverse: true,
              itemCount: viewModel.localImages.length,
              itemBuilder: (context, index) {
                String imageUrlOrPath = viewModel.localImages[index];
                bool isNetworkImage = imageUrlOrPath.startsWith('https://');
                return Template_right_1(
                  imageUrl: imageUrlOrPath,
                  premiumStatus: viewModel.premiumStatus,
                  showCTA: true,
                  isPoster: true,
                  isHOme: false,
                  isHttp: isNetworkImage,
                  onImageAdded: () {
                    setState(() {});
                  },
                );
              },
            ),
          )
        : Container(
            padding: EdgeInsets.all(20),
            child: Text(
                "No Poster Data Available.. click on make your poster button and Upload Poster."),
          );
  }

  Widget _buildBirthdayContent(CreatePosterViewModel viewModel) {
    localBirthday = viewModel.localBirthday;
    if (viewModel.localBirthday.isEmpty) {
      localBirthday = ['Asset/Images/ss.jpeg'];
    }

    return localBirthday.isNotEmpty
        ? Expanded(
            // height: (MediaQuery.of(context).size.height * 0.9) *
            //     viewModel.localImages.length,
            child: ListView.builder(
              reverse: false,
              itemCount: localBirthday.length,
              // physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                String imageUrlOrPath = localBirthday[index];

                bool isNetworkImage = imageUrlOrPath.startsWith('https://');
                print(localBirthday[index]);
                print(viewModel.premiumStatus);

                return Template_right_1(
                  isBirthDay: true,
                  imageUrl: imageUrlOrPath,
                  premiumStatus: viewModel.premiumStatus,
                  showCTA: true,
                  isPoster: true,
                  isHttp: isNetworkImage,
                  isHOme: true,
                  isBottomsheet: true,
                  onImageAdded: () {
                    setState(() {});
                  },
                );
              },
            ),
          )
        : Container(
            padding: EdgeInsets.all(20),
            child: Text(
              "No Poster Data Available.. click on make your poster button and Upload Poster.",
            ),
          );
  }

  Future<void> _shareScreenshot() async {
    try {
      // Capture screenshot
      final image = await _userScreenshotController.capture();
      if (image == null) return;

      // Save the image locally
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = File('${directory.path}/screenshot.png');
      await imagePath.writeAsBytes(image);

      // Share on WhatsApp
      await Share.shareXFiles([XFile(imagePath.path)],
          text: "Check out my photo!");
    } catch (e) {
      print("Error sharing screenshot: $e");
    }
  }

  String _displayedName = "Click to add a name"; // Default text
  TextEditingController _nameController = TextEditingController();

  void _editName(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Enter Name'),
        content: TextField(
          controller: _nameController,
          decoration: InputDecoration(hintText: 'Enter your name'),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close the dialog
            },
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _displayedName = _nameController.text.isEmpty
                    ? "No name provided"
                    : _nameController.text;
              });
              Navigator.of(context).pop(); // Close the dialog
            },
            child: Text('Done'),
          ),
        ],
      ),
    );
  }

  String? _selectedImagePath;

  final ImagePicker _imagePicker = ImagePicker();

  Future<void> _pickImage() async {
    // final XFile? pickedImage =
    //     await _imagePicker.pickImage(source: ImageSource.gallery);

    ThemeData themeData = Theme.of(context);
    final XFile? pickedImage = await UserImage().pickImage(themeData);

    if (pickedImage != null) {
      setState(() {
        _selectedImagePath = pickedImage.path;
      });
    }
  }

  Widget Uploaddialog({required CreatePosterViewModel parentViewModel}) {
    return ViewModelBuilder<MakePosterViewModel>.reactive(
        viewModelBuilder: () => MakePosterViewModel(parentViewModel),
        builder: (context, viewModel, child) => Column(
              children: [
                Row(
                  children: [
                    Text(
                      "Select Your Poster",
                      style:
                          TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    Spacer(),
                    IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(Icons.cancel))
                  ],
                ),
                SizedBox(height: 10),
                GestureDetector(
                  onTap: () async {
                    await viewModel.pickImage();
                  },
                  child: DottedBorder(
                    color: Colors.grey,
                    strokeWidth: 2,
                    dashPattern: [6, 6],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(12),
                    child: Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * 0.3,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: viewModel.selectedImagePath != null
                          ? Image.file(
                              File(viewModel.selectedImagePath!),
                              fit: BoxFit.cover,
                            )
                          : const Center(
                              child: Icon(
                                Icons.upload_file,
                                color: Colors.grey,
                                size: 50,
                              ),
                            ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        await viewModel.pickImage();
                      },
                      child: const Text('Pick Poster'),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width * 0.03),
                    ElevatedButton(
                      onPressed: () async {
                        var success = await viewModel.uploadImage();
                        if (success) {
                          viewModel.clearSelectedImage();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Photo uploaded successfully!')),
                          );
                          Navigator.pop(context);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Failed to upload photo.')),
                          );
                        }
                      },
                      child: const Text('Done'),
                    ),
                  ],
                ),
              ],
            ));
  }
}
