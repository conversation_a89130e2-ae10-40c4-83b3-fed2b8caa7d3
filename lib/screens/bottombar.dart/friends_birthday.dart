import 'dart:developer';
import 'dart:io';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/services/local_notification_manager.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bjpnew/objects/Birthday.dart';

class YourFriendsBdayView extends StatefulWidget {
  final bool isPremium;
  const YourFriendsBdayView({Key? key, required final bool isPremium})
      : this.isPremium = isPremium,
        super(key: key);

  @override
  State<YourFriendsBdayView> createState() => _YourFriendsBdayViewState();
}

class _YourFriendsBdayViewState extends State<YourFriendsBdayView> {
  List<Birthday> birthdays = [];
  List<Birthday> upcomingBirthdays = [];
  String? _addBirthDayImagePath;
  final ImagePicker _imagePicker = ImagePicker();
  bool isBirthPopUpShown = false;

  @override
  void initState() {
    super.initState();

    LocalNotificationControllerManager.checkScheduledNotifications();
    _loadBirthdays();
  }

  Future<void> _loadBirthdays() async {
    final prefs = await SharedPreferences.getInstance();
    final String? birthdaysString = prefs.getString('birthdays');

    if (birthdaysString != null && birthdaysString.isNotEmpty) {
      try {
        birthdays = Birthday.decode(birthdaysString);

        _updateUpcomingBirthdays();
        if (!isBirthPopUpShown) {
          _checkTodayBirthdays();
          isBirthPopUpShown = true;
        }
        setState(() {});
      } catch (e) {
        print("Error loading birthdays: $e");
      }
    }
  }

  void _updateUpcomingBirthdays() {
    final today = DateTime.now();
    upcomingBirthdays = birthdays.where((birthday) {
      final dob = birthday.dob;
      final nextBirthday = DateTime(today.year, dob!.month, dob.day);
      return nextBirthday.isAfter(today) &&
          nextBirthday.isBefore(today.add(const Duration(days: 7)));
    }).toList();
  }

  void _checkTodayBirthdays() {
    final today = DateTime.now();
    final todayBirthdays = birthdays.where((birthday) {
      return birthday.dob?.day == today.day &&
          birthday.dob?.month == today.month;
    }).toList();

    if (todayBirthdays.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showTodayBirthdayPopup(todayBirthdays);
      });
    }
  }

  List localBirthday = ['Asset/Images/ss.jpeg'];
  void _showTodayBirthdayPopup(List<Birthday> todayBirthdays) {
    final List<ScreenshotController> controllers = List.generate(
      todayBirthdays.length,
      (_) => ScreenshotController(),
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          insetPadding: EdgeInsets.all(16),
          contentPadding: EdgeInsets.fromLTRB(18, 0, 18, 20),
          titlePadding: const EdgeInsets.only(
              left: 16, right: 8, top: 12), // Minimal padding
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                "Today's Birthdays 🎉",
                style: TextStyle(fontSize: 22),
              ),
              IconButton(
                icon: const Icon(Icons.close, size: 22),
                padding: EdgeInsets.zero, // Remove IconButton padding
                constraints:
                    const BoxConstraints(), // Minimal constraints for IconButton
                onPressed: () {
                  Navigator.of(context).pop(); // Close the dialog
                },
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min, // Dynamically adjust height
              children: todayBirthdays.asMap().entries.map((entry) {
                final index = entry.key;
                final birthday = entry.value;
                String imageUrlOrPath = localBirthday[0];

                return Column(
                  children: [
                    Screenshot(
                      controller: controllers[index], // Use a unique controller
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 8.0),
                        width: double.infinity,
                        child: Stack(
                          children: [
                            // Background Image
                            Container(
                              constraints: const BoxConstraints(
                                minHeight: 200, // Minimum height
                                maxHeight: 600, // Maximum height
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                image: DecorationImage(
                                  image: AssetImage(imageUrlOrPath),
                                  fit: BoxFit.fill,
                                ),
                              ),
                            ),
                            // User Image and Name
                            Positioned(
                              bottom: 320,
                              left: 0,
                              right: 0,
                              child: Container(
                                width: 160,
                                height: 160,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                  border: Border.all(color: Colors.grey),
                                  image: DecorationImage(
                                    image: (birthday.imagePath != null &&
                                            birthday.imagePath!.isNotEmpty)
                                        ? FileImage(File(birthday.imagePath!))
                                        : const AssetImage(
                                                'Asset/SVG/ImagePlaceholder.svg')
                                            as ImageProvider,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                            ),

                            Positioned(
                              bottom: 260,
                              left: 0,
                              right: 0,
                              child: Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 50),
                                width: MediaQuery.of(context).size.width * 0.4,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 5),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: Text(
                                  birthday.name ?? '',
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 24.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    // Share Poster Button
                    SizedBox(
                      height: 40,
                      child: PrimaryButton(
                        iconPath: 'Asset/Icons/Whatsapp-Icon.svg',
                        isEnabled: true,
                        isLoading: false,
                        onTap: () async {
                          if (widget.isPremium) {
                            await _shareScreenshot(controllers[index]);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content:
                                  Text('Purchase Premium To Share Posters.'),
                              duration: Duration(seconds: 1),
                            ));
                          }
                        },
                        height: 48,
                        color: Colors.green,
                        label: "| Share This Photo",
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
          // actions: [
          //   TextButton(
          //     onPressed: () => Navigator.of(context).pop(),
          //     child: const Text("Dismiss"),
          //   ),
          // ],
        );
      },
    );
  }

// Function to share a screenshot
  Future<void> _shareScreenshot(ScreenshotController controller) async {
    try {
      // Capture screenshot
      final image = await controller.capture();
      if (image == null) return;

      // Save the image locally
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = File(
          '${directory.path}/screenshot_${DateTime.now().millisecondsSinceEpoch}.png');
      await imagePath.writeAsBytes(image);

      // Share on WhatsApp
      await Share.shareXFiles([XFile(imagePath.path)],
          text: "Check out my photo!");
    } catch (e) {
      print("Error sharing screenshot: $e");
    }
  }

  Future<void> _saveBirthdays() async {
    final prefs = await SharedPreferences.getInstance();
    String jsonList = Birthday.encode(birthdays);
    await prefs.setString('birthdays', jsonList);
    log("BIRTHDAY Save = $jsonList");
  }

  Future<void> _pickBirthDayImage(int? index, Birthday bd) async {
    final XFile? pickedImage =
        await _imagePicker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      setState(() {
        _addBirthDayImagePath = pickedImage.path;
        Navigator.of(context).pop();
        Birthday curr = bd.copyWith(imagePath: pickedImage.path);
        _addOrEditBirthday(index: index, birthdayToEdit: curr);
        log("Add Image = " + _addBirthDayImagePath!);
      });
    }
  }

  void _addOrEditBirthday({int? index, Birthday? birthdayToEdit}) {
    String name = birthdayToEdit?.name ?? '';
    DateTime? dob = birthdayToEdit?.dob;

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              Text(
                index == null ? "Add Birthday" : "Edit Birthday",
                style: TextStyle(
                    fontFamily: 'Montserrat',
                    fontWeight: FontWeight.bold,
                    fontSize: 28),
              ),
              const SizedBox(height: 10),
              TextField(
                decoration: const InputDecoration(labelText: "Name"),
                controller: TextEditingController(text: name),
                onChanged: (value) {
                  name = value;
                },
              ),
              TextField(
                decoration: const InputDecoration(labelText: "Date of Birth"),
                readOnly: true,
                controller: TextEditingController(
                    text: dob != null
                        ? dob?.toLocal().toString().split(' ')[0]
                        : ''),
                onTap: () async {
                  dob = await showDatePicker(
                    context: context,
                    initialDate: dob ?? DateTime.now(),
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                  );
                },
              ),
              SizedBox(height: 10),
              GestureDetector(
                onTap: () {
                  var bd = Birthday(
                    name: name,
                    imagePath:
                        birthdayToEdit?.imagePath ?? _addBirthDayImagePath,
                    dob: dob,
                  );
                  _pickBirthDayImage(index, bd);
                },
                child: Container(
                  height: 150,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: birthdayToEdit != null &&
                          birthdayToEdit.imagePath != null &&
                          birthdayToEdit.imagePath!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: Image.file(File(birthdayToEdit.imagePath!),
                              fit: BoxFit.cover),
                        )
                      : _addBirthDayImagePath != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: Image.file(File(_addBirthDayImagePath!),
                                  fit: BoxFit.cover),
                            )
                          : Center(child: Text("Tap to select an image")),
                ),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  if (name.isNotEmpty &&
                      dob != null &&
                      (_addBirthDayImagePath != null ||
                          birthdayToEdit?.imagePath != null)) {
                    setState(() {
                      if (index == null) {
                        final cbd = Birthday(
                          name: name,
                          imagePath: _addBirthDayImagePath,
                          dob: dob,
                        );
                        birthdays.add(cbd);
                        var len = birthdays.length;
                        LocalNotificationControllerManager.scheduleNotification(
                            cbd, len - 1);
                      } else {
                        int ind = index;
                        birthdays[ind] = Birthday(
                          name: name,
                          imagePath: _addBirthDayImagePath ??
                              birthdayToEdit!.imagePath,
                          dob: dob,
                        );
                        updateNotif(birthdayToEdit, ind);
                      }
                      _addBirthDayImagePath = null;

                      _saveBirthdays();
                      _updateUpcomingBirthdays(); // Update upcoming birthdays after editing
                    });
                    Navigator.popUntil(context, (route) => route.isFirst);
                    setState(() {});
                  }
                },
                child: Text(index == null ? "Add Birthday" : "Update Birthday"),
              ),
            ],
          ),
        );
      },
    );
  }

  void updateNotif(Birthday? birthday, int ind) async {
    if (birthday != null) {
      await LocalNotificationControllerManager.cancelNotificationsWithId(
          birthday.id);
    }
    await LocalNotificationControllerManager.scheduleNotification(
        birthdays[ind], ind); //schedule new notification..
  }

  void _deleteBirthday(Birthday birthday) {
    setState(() {
      birthdays.remove(birthday);
      _saveBirthdays();
      _updateUpcomingBirthdays(); // Update upcoming birthdays after deletion
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: CustomScrollView(
        slivers: [
          // Upcoming Birthdays Section
          if (upcomingBirthdays.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16.0),
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.lightBlue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Upcoming Birthdays",
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16.0),
                    // Use ListView.builder for the upcoming birthdays list
                    ListView.builder(
                      shrinkWrap:
                          true, // Allow ListView to take only the space it needs
                      physics:
                          NeverScrollableScrollPhysics(), // Disable scrolling for inner ListView
                      itemCount: upcomingBirthdays.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: _buildBirthdayTile(
                              upcomingBirthdays[index], index),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],

          // All Birthdays Section
          if (birthdays.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 16.0),
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "All Birthdays",
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16.0),
                    // Use ListView.builder for the birthdays list
                    ListView.builder(
                      shrinkWrap:
                          true, // Allow ListView to take only the space it needs
                      physics:
                          NeverScrollableScrollPhysics(), // Disable scrolling for inner ListView
                      itemCount: birthdays.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: _buildBirthdayTile(birthdays[index], index),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            // Add Birthday Button Section
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.fromLTRB(16.0, 0, 16.0, 300.0),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Center(
                    child: ElevatedButton(
                      onPressed: () {
                        _addOrEditBirthday(); // Open the add/edit birthday dialog
                      },
                      child: const Text("Add Birthday"),
                    ),
                  ),
                ),
              ),
            ),
          ],

          // No Birthdays Available
          if (birthdays.isEmpty && upcomingBirthdays.isEmpty)
            SliverFillRemaining(
              hasScrollBody: false,
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 70),
                    const Icon(Icons.cake, size: 50, color: Colors.grey),
                    const SizedBox(height: 10),
                    const Text(
                      "No birthdays available.",
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20), // Add space for the button
                    ElevatedButton(
                      onPressed: () {
                        _addOrEditBirthday(); // Open the add/edit birthday dialog
                      },
                      child: const Text("Add Birthday"),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBirthdayTile(Birthday birthday, int index) {
    final formattedDate = birthday.dob != null
        ? "${birthday.dob!.day.toString().padLeft(2, '0')}-${birthday.dob!.month.toString().padLeft(2, '0')}-${birthday.dob!.year}"
        : "N/A";

    return ListTile(
      title: Text(birthday.name ?? "Unnamed"),
      subtitle: Text("DOB: $formattedDate"),
      leading: CircleAvatar(
        backgroundImage:
            (birthday.imagePath != null && birthday.imagePath!.isNotEmpty)
                ? FileImage(File(birthday.imagePath!))
                : AssetImage('Asset/SVG/ImagePlaceholder.svg') as ImageProvider,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () =>
                _addOrEditBirthday(index: index, birthdayToEdit: birthday),
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteConfirmationDialog(birthday, index),
          ),
        ],
      ),
    );
  }

// Method to show confirmation dialog before deletion
  void _showDeleteConfirmationDialog(Birthday birthday, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Confirm Deletion"),
          content: Text("Are you sure you want to delete ${birthday.name}?"),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                await LocalNotificationControllerManager
                    .cancelNotificationsWithId(birthday.id);
                _deleteBirthday(birthday); // Proceed with deletion
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text("Delete"),
            ),
          ],
        );
      },
    );
  }
}
