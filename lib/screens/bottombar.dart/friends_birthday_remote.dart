import 'dart:developer';
import 'dart:io';
import 'package:bjpnew/global/CustomSecondaryButton.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/global/threeDText.dart';
import 'package:bjpnew/screens/bottombar.dart/CreatePosterViewModel.dart';
import 'package:bjpnew/screens/template/template_right_1.dart';
import 'package:bjpnew/services/birthday_service.dart';
import 'package:bjpnew/services/local_notification_manager.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bjpnew/objects/Birthday.dart';

class YourFriendsBdayViewRemote extends StatefulWidget {
  final bool isPremium;
  final void Function(Birthday)? passSeeMoreUserData;

  const YourFriendsBdayViewRemote({
    Key? key,
    required this.isPremium,
    this.passSeeMoreUserData,
  }) : super(key: key);

  @override
  State<YourFriendsBdayViewRemote> createState() =>
      _YourFriendsBdayViewRemoteState();
}

class _YourFriendsBdayViewRemoteState extends State<YourFriendsBdayViewRemote>
    with SingleTickerProviderStateMixin {
  List<Birthday> birthdays = [];
  List<Birthday> upcomingBirthdays = [];
  String? _addBirthDayImagePath;
  final ImagePicker _imagePicker = ImagePicker();
  bool isBirthPopUpShown = false;
  BirthdayService bday = BirthdayService();
  CreatePosterViewModel createPosterViewModel = CreatePosterViewModel();
  bool _isLoading = true;

  late TabController _tabController;
  Color _selectedColor = Colors.green[70] ?? Colors.green;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    _tabController.addListener(() {
      if (_tabController.index == 0) {
        _selectedColor =
            Colors.lightBlue[70] ?? Colors.lightBlue; // Color for first tab
      } else {
        _selectedColor =
            Colors.green[70] ?? Colors.green; // Color for second tab
      }
      setState(() {});
    });

    getBirthdayImages();
    LocalNotificationControllerManager.checkScheduledNotifications();
    _loadBirthdays();
  }

  void getBirthdayImages() async {
    var list = await createPosterViewModel.fetchPosterData();
    if (list != null && list.isNotEmpty) {
      localBirthday = list;
      setState(() {});
    }
  }

  Future<void> _loadBirthdays() async {
    // final prefs = await SharedPreferences.getInstance();
    // final String? birthdaysString = prefs.getString('birthdays');

    // if (birthdaysString != null && birthdaysString.isNotEmpty) {
    setState(() {
      _isLoading = true;
    });
    try {
      // birthdays = Birthday.decode(birthdaysString);
      birthdays = await bday.fetchBirthdays();
      log("LIST SIZE checkM AFTER : ${birthdays.length}");
      _updateUpcomingBirthdays();
      LocalNotificationControllerManager.ensureNotificationsSynced(birthdays);

      isBirthPopUpShown = await createPosterViewModel.isPopUpShown();
      if (!isBirthPopUpShown) {
        _addOrEditBirthday();

        _checkTodayBirthdays();
        isBirthPopUpShown = true;
        createPosterViewModel.setPopUp(true);
      }
      setState(() {});
    } catch (e) {
      print("Error loading birthdays: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
    // }
  }

  void _updateUpcomingBirthdays() {
    final today = DateTime.now();
    upcomingBirthdays = birthdays.where((birthday) {
      final dob = birthday.dob;
      final nextBirthday = DateTime(today.year, dob!.month, dob.day);
      return nextBirthday.isAfter(today) &&
          nextBirthday.isBefore(today.add(const Duration(days: 7)));
    }).toList();
  }

  void _checkTodayBirthdays() {
    final today = DateTime.now();
    final todayBirthdays = birthdays.where((birthday) {
      return birthday.dob?.day == today.day &&
          birthday.dob?.month == today.month;
    }).toList();

    if (todayBirthdays.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showTodayBirthdayPopup(todayBirthdays);
      });
    }
  }

  Widget _buildBirthdayItem(Birthday birthday) {
    String imageUrlOrPath = localBirthday[0];
    bool isNetworkImage = imageUrlOrPath.startsWith('https://');

    return Column(
      children: [
        Template_right_1(
          imageUrl: imageUrlOrPath,
          premiumStatus: widget.isPremium,
          showCTA: true,
          isPoster: true,
          isHOme: false,
          isHttp: isNetworkImage,
          isBirthDay: true,
          isBirthdayShare: true,
          birthdayName: birthday.name,
          birthdayPhoto: birthday.imagePath,
          onImageAdded: () {
            setState(() {}); // Ensure UI updates when image is added
          },
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(14, 0, 14, 12),
          child: CustomSecondaryButton(
            showIcon: false,
            leadingIcon: 'Asset/Icons/Download-Icon.svg',
            bgcolor: Colors.grey,
            onPressed: () {
              if (widget.passSeeMoreUserData != null) {
                widget.passSeeMoreUserData!(birthday);
                Navigator.of(context).pop();
              }
            },
            buttonText: "Show More Posters.",
            buttonColor: Colors.black,
          ),
        )
      ],
    );
  }

  List localBirthday = ['Asset/Images/ss.jpeg'];
  void _showTodayBirthdayPopup(List<Birthday> todayBirthdays, {String? title}) {
    final List<ScreenshotController> controllers = List.generate(
      todayBirthdays.length,
      (_) => ScreenshotController(),
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
            insetPadding: EdgeInsets.all(16),
            contentPadding: EdgeInsets.fromLTRB(8, 0, 8, 20),
            titlePadding: const EdgeInsets.only(
                left: 16, right: 8, top: 12), // Minimal padding
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title ?? "Today's Birthdays 🎉",
                  style: const TextStyle(fontSize: 22),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 22),
                  padding: EdgeInsets.zero, // Remove IconButton padding
                  constraints:
                      const BoxConstraints(), // Minimal constraints for IconButton
                  onPressed: () {
                    Navigator.of(context).pop(); // Close the dialog
                  },
                ),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                reverse: false,
                shrinkWrap: true,
                itemCount: todayBirthdays.length,
                itemBuilder: (context, index) {
                  return _buildBirthdayItem(todayBirthdays[index]);
                },
              ),
            )
            // content: SingleChildScrollView(
            //   child: SizedBox(
            //     width: double.infinity,
            //     child: Column(
            //       mainAxisSize: MainAxisSize.min, // Dynamically adjust height
            //       children: todayBirthdays.asMap().entries.map((entry) {
            //         final index = entry.key;
            //         final birthday = entry.value;
            //         String imageUrlOrPath = localBirthday[0];
            //         bool isNetworkImage = imageUrlOrPath.startsWith('https://');

            //         return Column(
            //           children: [
            //             Screenshot(
            //               controller: controllers[index], // Use a unique controller
            //               child: Container(
            //                 margin: const EdgeInsets.symmetric(vertical: 8.0),
            //                 width: double.infinity,
            //                 child: Stack(
            //                   children: [
            //                     // Background Image
            //                     Container(
            //                       constraints: const BoxConstraints(
            //                         minHeight: 200, // Minimum height
            //                         maxHeight: 550, // Maximum height
            //                       ),
            //                       decoration: BoxDecoration(
            //                         color: Colors.grey,
            //                         image: imageUrlOrPath.startsWith('http')
            //                             ? DecorationImage(
            //                                 image: NetworkImage(imageUrlOrPath),
            //                                 fit: BoxFit.fill,
            //                               )
            //                             : DecorationImage(
            //                                 image: AssetImage(imageUrlOrPath),
            //                                 fit: BoxFit.fill,
            //                               ),
            //                       ),
            //                     ),
            //                     // User Image and Name
            //                     Positioned(
            //                       bottom: 320,
            //                       left: 0,
            //                       right: 0,
            //                       child: Container(
            //                         width: 160,
            //                         height: 160,
            //                         decoration: BoxDecoration(
            //                           shape: BoxShape.circle,
            //                           color: Colors.white,
            //                           border: Border.all(color: Colors.grey),
            //                           image: DecorationImage(
            //                             image: _loadImage(birthday.imagePath),
            //                             fit: BoxFit.cover,
            //                             alignment: Alignment.topCenter,
            //                           ),
            //                         ),
            //                       ),
            //                     ),

            //                     Positioned(
            //                       top: 252,
            //                       left: 0,
            //                       right:
            //                           0, // Make sure it stretches within the Stack
            //                       child: GestureDetector(
            //                         child: Align(
            //                           alignment:
            //                               Alignment.center, // Center it properly
            //                           child: Container(
            //                             padding: const EdgeInsets.symmetric(
            //                                 horizontal: 8.0, vertical: 6.0),
            //                             decoration: BoxDecoration(
            //                               color: Colors.white,
            //                               borderRadius: BorderRadius.circular(8.0),
            //                             ),
            //                             child: ThreeDTextWidget(
            //                               text: birthday.name ?? '',
            //                               fontSize: 26.0,
            //                               textColor: Colors.yellow,
            //                             ),
            //                           ),
            //                         ),
            //                       ),
            //                     ),
            //                   ],
            //                 ),
            //               ),
            //             ),
            //             // Share Poster Button
            //             SizedBox(
            //               height: 40,
            //               child: PrimaryButton(
            //                 iconPath: 'Asset/Icons/Whatsapp-Icon.svg',
            //                 isEnabled: true,
            //                 isLoading: false,
            //                 onTap: () async {
            //                   if (widget.isPremium) {
            //                     await _shareScreenshot(controllers[index]);
            //                   } else {
            //                     ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            //                       content:
            //                           Text('Purchase Premium To Share Posters.'),
            //                       duration: Duration(seconds: 1),
            //                     ));
            //                   }
            //                 },
            //                 height: 48,
            //                 color: Colors.green,
            //                 label: "| Share This Photo",
            //               ),
            //             ),
            //           ],
            //         );
            //       }).toList(),
            //     ),
            //   ),
            // ),
            // actions: [
            //   TextButton(
            //     onPressed: () => Navigator.of(context).pop(),
            //     child: const Text("Dismiss"),
            //   ),
            // ],
            );
      },
    );
  }

// Function to share a screenshot
  Future<void> _shareScreenshot(ScreenshotController controller) async {
    try {
      // Capture screenshot
      final image = await controller.capture();
      if (image == null) return;

      // Save the image locally
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = File(
          '${directory.path}/screenshot_${DateTime.now().millisecondsSinceEpoch}.png');
      await imagePath.writeAsBytes(image);

      // Share on WhatsApp
      await Share.shareXFiles([XFile(imagePath.path)],
          text: "Check out my photo!");
    } catch (e) {
      print("Error sharing screenshot: $e");
    }
  }

  Future<void> _saveBirthdays() async {
    final prefs = await SharedPreferences.getInstance();
    String jsonList = Birthday.encode(birthdays);
    await prefs.setString('birthdays', jsonList);
    log("BIRTHDAY Save = $jsonList");
  }

  Future<void> _pickBirthDayImage(int? index, Birthday bd) async {
    ThemeData themeData = Theme.of(context);
    final XFile? pickedImage =
        // await _imagePicker.pickImage(source: ImageSource.gallery);
        await UserImage().pickImage(themeData, isSquare: true);
    if (pickedImage != null) {
      setState(() {
        _addBirthDayImagePath = pickedImage.path;
        Navigator.of(context).pop();
        Birthday curr = bd.copyWith(imagePath: pickedImage.path);
        _addOrEditBirthday(index: index, birthdayToEdit: curr);
        log("Add Image = " + _addBirthDayImagePath!);
      });
    }
  }

  void _addOrEditBirthday({int? index, Birthday? birthdayToEdit}) {
    String name = birthdayToEdit?.name ?? '';
    String posterId = birthdayToEdit?.posterId ?? '';
    DateTime? dob = birthdayToEdit?.dob;
    log("POSTER ID : ${posterId}");
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      index == null
                          ? "Create birthday list of your people \n(अपने लोगों के जन्मदिन की सूची बनाएं)"
                          : "Edit birthday list of your people \n(अपनी लोगों के जन्मदिन सूची एडिट करें)",
                      style: TextStyle(
                        fontFamily: 'Montserrat',
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                      softWrap: true,
                    ),
                    IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () {
                        Navigator.of(context).pop(); // Close modal
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                TextField(
                  decoration: const InputDecoration(
                      labelText:
                          "Add Name Of Birthday Person (जन्मदिन वाले व्यक्ति का नाम जोड़ें)"),
                  controller: TextEditingController(text: name),
                  onChanged: (value) {
                    name = value;
                  },
                ),
                TextField(
                  decoration: const InputDecoration(
                      labelText:
                          "Add DOB of birthday person (जन्मदिन की तारीख जोड़ें)"),
                  readOnly: true,
                  controller: TextEditingController(
                      text: dob != null
                          ? dob?.toLocal().toString().split(' ')[0]
                          : ''),
                  onTap: () async {
                    DateTime? pickedDate = await showDatePicker(
                      context: context,
                      initialDate: dob ?? DateTime.now(),
                      firstDate: DateTime(1900),
                      lastDate: DateTime.now(),
                      initialDatePickerMode: DatePickerMode.year,
                    );

                    if (pickedDate != null) {
                      setState(() {
                        dob = pickedDate;
                      });
                    }
                  },
                ),
                SizedBox(height: 10),
                GestureDetector(
                  onTap: () {
                    var bd = Birthday(
                      posterId: posterId,
                      name: name,
                      imagePath:
                          birthdayToEdit?.imagePath ?? _addBirthDayImagePath,
                      dob: dob,
                    );
                    _pickBirthDayImage(index, bd);
                  },
                  child: Container(
                    height: 150,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: birthdayToEdit != null &&
                            birthdayToEdit.imagePath != null &&
                            birthdayToEdit.imagePath!.isNotEmpty
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            // child: Image.file(File(birthdayToEdit.imagePath!),
                            //     fit: BoxFit.cover),
                            child: loadImage(birthdayToEdit.imagePath!),
                          )
                        : _addBirthDayImagePath != null
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(8.0),
                                // child: Image.file(File(_addBirthDayImagePath!),
                                //     fit: BoxFit.cover),
                                child: loadImage(_addBirthDayImagePath))
                            : Center(
                                child: Text(
                                    "Upload photo of birthday person \n (जन्मदिन वाले व्यक्ति की फोटो अपलोड करें)")),
                  ),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  onPressed: () {
                    if (name.isNotEmpty && dob != null
                        // &&
                        // (_addBirthDayImagePath != null ||
                        //     birthdayToEdit?.imagePath != null)
                        ) {
                      setState(() {
                        if (index == null) {
                          final cbd = Birthday(
                            name: name,
                            imagePath: _addBirthDayImagePath,
                            dob: dob,
                          );
                          // birthdays.add(cbd);
                          addBirthday(cbd);
                          // var len = birthdays.length;
                          LocalNotificationControllerManager
                              .scheduleNotification(cbd, 0);
                        } else {
                          int ind = index;
                          birthdays[ind] = Birthday(
                            posterId: birthdayToEdit?.posterId ?? '',
                            name: name,
                            imagePath: _addBirthDayImagePath ??
                                birthdayToEdit!.imagePath,
                            dob: dob,
                          );

                          updateBirthday(birthdays[ind]);
                          updateNotif(birthdayToEdit, ind);
                        }
                        _addBirthDayImagePath = null;

                        // _saveBirthdays();
                        _updateUpcomingBirthdays(); // Update upcoming birthdays after editing
                      });
                      Navigator.pop(context);
                      setState(() {});
                    }
                  },
                  child:
                      Text(index == null ? "Add Birthday" : "Update Birthday"),
                ),
              ],
            ),
          );
        });
      },
    );
  }

  addBirthday(Birthday cbd) async {
    // birthdays.add(cbd);
    await bday.addBirthday(cbd);
    _loadBirthdays();
  }

  updateBirthday(Birthday cbd) async {
    await bday.updateBirthday(cbd);
    _loadBirthdays();
  }

  deleteBirthday(String id) async {
    await bday.deleteBirthday(id);
    _loadBirthdays();
  }

  ImageProvider _loadImage(String? imagePath) {
    if (imagePath != null && imagePath.isNotEmpty) {
      if (imagePath.startsWith('http')) {
        return NetworkImage(imagePath);
      } else {
        File file = File(imagePath);
        if (file.existsSync()) {
          return FileImage(file);
        }
      }
    }
    return const AssetImage('Asset/SVG/ImagePlaceholder.svg');
  }

  Widget loadImage(String? imagePath) {
    if (imagePath != null && imagePath.isNotEmpty) {
      if (imagePath.startsWith("http")) {
        return Image.network(
          imagePath,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _placeholder(),
        );
      } else {
        File file = File(imagePath);
        if (file.existsSync()) {
          return Image.file(
            file,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => _placeholder(),
          );
        }
      }
    }
    return _placeholder();
  }

  Widget _placeholder() => const Center(child: Text("Tap to select an image"));

  void updateNotif(Birthday? birthday, int ind) async {
    if (birthday != null) {
      await LocalNotificationControllerManager.cancelNotificationsWithId(
          birthday.id);
    }
    await LocalNotificationControllerManager.scheduleNotification(
        birthdays[ind], ind); //schedule new notification..
  }

  void _deleteBirthday(Birthday birthday) {
    setState(() {
      birthdays.remove(birthday);
      // _saveBirthdays();
      if (birthday.posterId != null) {
        deleteBirthday(birthday.posterId!);
      }
      _updateUpcomingBirthdays(); // Update upcoming birthdays after deletion
    });
  }

  // @override
  // Widget build(BuildContext context) {
  //   return (_isLoading)
  //       ? SizedBox(
  //           height: 500,
  //           child: Center(
  //             child: CircularProgressIndicator(), // Loading indicator
  //           ),
  //         )
  //       : SizedBox(
  //           height: MediaQuery.of(context).size.height,
  //           child: CustomScrollView(
  //             slivers: [
  //               // Add Birthday Button Section
  //               SliverToBoxAdapter(
  //                 child: Container(
  //                   margin: const EdgeInsets.fromLTRB(16.0, 0, 16.0, 3),
  //                   child: Padding(
  //                     padding: const EdgeInsets.symmetric(vertical: 8.0),
  //                     child: Center(
  //                         // child: ElevatedButton(
  //                         //   onPressed: () {
  //                         //     _addOrEditBirthday(); // Open the add/edit birthday dialog
  //                         //   },
  //                         //   child: const Text("Add Birthday"),
  //                         // ),
  //                         child: SizedBox(
  //                       width:
  //                           double.infinity, // Makes the button take full width
  //                       child: ElevatedButton(
  //                         onPressed: () {
  //                           _addOrEditBirthday(); // Open the add/edit birthday dialog
  //                         },
  //                         style: ElevatedButton.styleFrom(
  //                           backgroundColor: Colors.orange, // Button color
  //                           foregroundColor: Colors.white, // Text color
  //                           shape: RoundedRectangleBorder(
  //                             borderRadius:
  //                                 BorderRadius.circular(8), // Rounded corners
  //                           ),
  //                         ),
  //                         child: Row(
  //                           mainAxisAlignment:
  //                               MainAxisAlignment.center, // Center content
  //                           children: [
  //                             Text(
  //                               "Add Birthday",
  //                               style: TextStyle(
  //                                 fontSize: 17,
  //                                 fontWeight: FontWeight.w500,
  //                               ),
  //                             ),
  //                             SizedBox(
  //                                 width: 8), // Spacing between text and icon
  //                             Icon(Icons.add, color: Colors.white), // Add icon
  //                           ],
  //                         ),
  //                       ),
  //                     )),
  //                   ),
  //                 ),
  //               ),

  //               // Upcoming Birthdays Section
  //               if (upcomingBirthdays.isNotEmpty) ...[
  //                 SliverToBoxAdapter(
  //                   child: Container(
  //                     margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
  //                     padding: const EdgeInsets.all(16.0),
  //                     decoration: BoxDecoration(
  //                       color: Colors.lightBlue[50],
  //                       borderRadius: BorderRadius.circular(8),
  //                     ),
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         const Text(
  //                           "Upcoming Week's Birthdays",
  //                           style: TextStyle(
  //                               fontSize: 20, fontWeight: FontWeight.bold),
  //                         ),
  //                         const SizedBox(height: 16.0),
  //                         // Use ListView.builder for the upcoming birthdays list
  //                         ListView.builder(
  //                           shrinkWrap:
  //                               true, // Allow ListView to take only the space it needs
  //                           physics:
  //                               NeverScrollableScrollPhysics(), // Disable scrolling for inner ListView
  //                           itemCount: upcomingBirthdays.length,
  //                           itemBuilder: (context, index) {
  //                             return _buildBirthdayTile(
  //                                 upcomingBirthdays[index], index);
  //                           },
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //               ],

  //               // All Birthdays Section
  //               if (birthdays.isNotEmpty) ...[
  //                 SliverToBoxAdapter(
  //                   child: Container(
  //                     margin: const EdgeInsets.fromLTRB(16.0, 0, 16.0, 300.0),
  //                     padding: const EdgeInsets.all(16.0),
  //                     decoration: BoxDecoration(
  //                       color: Colors.green[50],
  //                       borderRadius: BorderRadius.circular(8),
  //                     ),
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         const Text(
  //                           "All Birthdays",
  //                           style: TextStyle(
  //                               fontSize: 20, fontWeight: FontWeight.bold),
  //                         ),
  //                         const SizedBox(height: 16.0),
  //                         // Use ListView.builder for the birthdays list
  //                         ListView.builder(
  //                           shrinkWrap:
  //                               true, // Allow ListView to take only the space it needs
  //                           physics:
  //                               NeverScrollableScrollPhysics(), // Disable scrolling for inner ListView
  //                           itemCount: birthdays.length,
  //                           itemBuilder: (context, index) {
  //                             return _buildBirthdayTile(
  //                                 birthdays[index], index);
  //                           },
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //               ],

  //               // No Birthdays Available
  //               if (birthdays.isEmpty && upcomingBirthdays.isEmpty)
  //                 SliverFillRemaining(
  //                   hasScrollBody: false,
  //                   child: Center(
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.center,
  //                       children: [
  //                         const SizedBox(height: 70),
  //                         const Icon(Icons.cake, size: 50, color: Colors.grey),
  //                         const SizedBox(height: 10),
  //                         const Text(
  //                           "No birthdays available.",
  //                           style: TextStyle(
  //                               fontSize: 18, fontWeight: FontWeight.w500),
  //                           textAlign: TextAlign.center,
  //                         ),
  //                         const SizedBox(
  //                             height: 20), // Add space for the button
  //                         // ElevatedButton(
  //                         //   onPressed: () {
  //                         //     _addOrEditBirthday(); // Open the add/edit birthday dialog
  //                         //   },
  //                         //   child: const Text("Add Birthday"),
  //                         // ),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //             ],
  //           ),
  //         );
  // }

  @override
  Widget build(BuildContext context) {
    return (_isLoading)
        ? SizedBox(
            height: 500,
            child: Center(
              child: CircularProgressIndicator(), // Loading indicator
            ),
          )
        : Column(
            children: [
              //Add Birthday button
              Padding(
                padding: const EdgeInsets.fromLTRB(12, 0, 12, 6),
                child: Center(
                    child: SizedBox(
                  width: double.infinity, // Makes the button take full width
                  child: ElevatedButton(
                    onPressed: () {
                      _addOrEditBirthday();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Add Birthday",
                          style: TextStyle(
                            fontSize: 17,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.add, color: Colors.white),
                      ],
                    ),
                  ),
                )),
              ),
              // TabBar
              Container(
                margin: const EdgeInsets.fromLTRB(10, 0, 10, 8),
                padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TabBar(
                  dividerColor: Colors.transparent,
                  controller: _tabController,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.black,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: _selectedColor,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  tabs: [
                    _buildTab('Upcoming Birthdays'),
                    _buildTab('All Birthdays'),
                  ],
                ),
              ),

              // TabBarView
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Tab 1: Upcoming Birthdays
                    _buildUpcomingBirthdays(),

                    // Tab 2: All Birthdays
                    _buildAllBirthdays(),
                  ],
                ),
              ),
            ],
          );
  }

  Widget _buildTab(String text) {
    return Tab(
      height: 30,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon(icon, size: 18),
          SizedBox(width: 8),
          Text(text, style: TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  Widget _buildUpcomingBirthdays() {
    return (upcomingBirthdays.isNotEmpty)
        ? CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Colors.lightBlue[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "Upcoming Week's Birthdays",
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16.0),
                      // Use ListView.builder for the upcoming birthdays list
                      ListView.builder(
                        shrinkWrap:
                            true, // Allow ListView to take only the space it needs
                        physics:
                            NeverScrollableScrollPhysics(), // Disable scrolling for inner ListView
                        itemCount: upcomingBirthdays.length,
                        itemBuilder: (context, index) {
                          return _buildBirthdayTile(
                              upcomingBirthdays[index], index);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          )
        : _buildNoBirthdayPlaceholder("No Upcoming Birthdays.");
  }

  Widget _buildNoBirthdayPlaceholder(String text) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 70),
          const Icon(Icons.cake, size: 50, color: Colors.grey),
          const SizedBox(height: 10),
          Text(
            text,
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAllBirthdays() {
    return (birthdays.isNotEmpty)
        ? CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16.0, 0, 16.0, 300.0),
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "All Birthdays",
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16.0),
                      // Use ListView.builder for the birthdays list
                      ListView.builder(
                        shrinkWrap:
                            true, // Allow ListView to take only the space it needs
                        physics:
                            NeverScrollableScrollPhysics(), // Disable scrolling for inner ListView
                        itemCount: birthdays.length,
                        itemBuilder: (context, index) {
                          return _buildBirthdayTile(birthdays[index], index);
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          )
        : _buildNoBirthdayPlaceholder("No birthdays available");
  }

  Widget _buildBirthdayTile(Birthday birthday, int index) {
    final formattedDate = birthday.dob != null
        ? "${birthday.dob!.day.toString().padLeft(2, '0')}-${birthday.dob!.month.toString().padLeft(2, '0')}-${birthday.dob!.year}"
        : "N/A";

    return ListTile(
      contentPadding: EdgeInsets.fromLTRB(6, 0, 4, 0),
      title: Text(birthday.name ?? "Unnamed"),
      subtitle: Text("DOB: $formattedDate"),
      leading: CircleAvatar(backgroundImage: _loadImage(birthday.imagePath)),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextButton(
            onPressed: () {
              List<Birthday> list = [birthday];
              _showTodayBirthdayPopup(list,
                  title: "${birthday.name}'s poster:");
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(
                  horizontal: 8, vertical: 4), // Smaller padding
              minimumSize: Size(0, 0), // Remove default button constraints
              side:
                  BorderSide(color: Color.fromARGB(255, 85, 97, 103)), // Border
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6), // Rounded corners
              ),
            ),
            child: Text(
              "View Poster",
              style: TextStyle(
                fontSize: 12, // Smaller font
                fontWeight: FontWeight.w500,
                color: Color.fromARGB(255, 85, 97, 103),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.edit,
              size: 20,
              color: Color.fromARGB(255, 85, 97, 103),
            ),
            onPressed: () =>
                _addOrEditBirthday(index: index, birthdayToEdit: birthday),
          ),
          IconButton(
            icon: const Icon(
              Icons.delete,
              size: 20,
              color: Color.fromARGB(255, 85, 97, 103),
            ),
            onPressed: () => _showDeleteConfirmationDialog(birthday, index),
          ),
        ],
      ),
    );
  }

// Method to show confirmation dialog before deletion
  void _showDeleteConfirmationDialog(Birthday birthday, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Confirm Deletion"),
          content: Text("Are you sure you want to delete ${birthday.name}?"),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () async {
                await LocalNotificationControllerManager
                    .cancelNotificationsWithId(birthday.id);
                _deleteBirthday(birthday); // Proceed with deletion
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text("Delete"),
            ),
          ],
        );
      },
    );
  }
}
