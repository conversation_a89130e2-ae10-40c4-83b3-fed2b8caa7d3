import 'dart:developer';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/screens/bottombar.dart/CreatePosterViewModel.dart';
import 'package:bjpnew/screens/bottombar.dart/friends_birthday.dart';
import 'package:bjpnew/screens/bottombar.dart/friends_birthday_remote.dart';
import 'package:bjpnew/screens/template/template_right_1.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:bjpnew/services/user_detail_service.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';

@RoutePage()
class BirthdayPosterView extends StatefulWidget {
  final int? pageIndex; // Optional parameter

  const BirthdayPosterView({super.key, this.pageIndex});

  @override
  State createState() => _BirthdayPosterViewState();
}

class _BirthdayPosterViewState extends State<BirthdayPosterView>
    with SingleTickerProviderStateMixin {
  // List localBirthday = ['Asset/Images/ss.jpeg'];
  List localBirthday = [];
  final ScreenshotController _userScreenshotController = ScreenshotController();
  late UserDetailService userDetailService;
  var name = '';
  var dob = '';
  var userId = '';
  var userImage = '';
  final CreatePosterViewModel _viewModel = CreatePosterViewModel();

  //template nameplate changes..
  TemplatesViewModel tempVm = TemplatesViewModel();
  final ScreenshotController _Stripcontroller = ScreenshotController();
  final GlobalKey _stripKey = GlobalKey();
  Color bottomColor = Colors.grey;

  late TabController _tabController;
  final Color _selectedColor = Colors.orange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: 1);
    _tabController.addListener(() {
      getBD();
    });
    if (widget.pageIndex != null && widget.pageIndex == 0) {
      _tabController.index = 0;
    }

    userDetailService = UserDetailService();
    checkUserDetails();
    _viewModel.fetchPosterData();
    _viewModel.setPopUp(false);
  }

  Future<void> getBD() async {
    await _viewModel.fetchBirthdays();
    setState(() {});
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> showDetailsDialog() async {
    final TextEditingController nameController =
        TextEditingController(text: name);
    final TextEditingController dobController =
        TextEditingController(text: dob);

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
              'First Provide Your Birth Date \n(सबसे पहले अपनी जन्मतिथि दर्ज करें)'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                    labelText: 'Add Your Name (अपना नाम जोड़ें)'),
              ),
              TextField(
                controller: dobController,
                readOnly: true, // Prevent manual input
                decoration: const InputDecoration(
                  labelText: 'Add your date of birth (अपनी जन्मतिथि जोड़ें)',
                  hintText: 'Pick your DOB (अपनी जन्मतिथि चुनें)',
                ),
                onTap: () async {
                  // Show the date picker
                  DateTime? selectedDate = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now(),
                    initialDatePickerMode: DatePickerMode.year,
                  );

                  if (selectedDate != null) {
                    // Format the date and set it in the dobController
                    dobController.text = "${selectedDate.toLocal()}"
                        .split(' ')[0]; // Ex: 2023-01-01
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (userDetailService
                        .isValidString(nameController.text.trim()) &&
                    userDetailService
                        .isValidString(dobController.text.trim())) {
                  // Save details
                  userDetailService.saveUserDetails(userDetails: {
                    'name': nameController.text.trim(),
                    'dob': dobController.text.trim(),
                  });

                  // Update state
                  setState(() {
                    name = nameController.text.trim();
                    dob = dobController.text.trim();
                  });

                  Navigator.of(context).pop();
                } else {
                  // Show an error if inputs are invalid
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please enter valid details')),
                  );
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  Future<void> checkUserDetails() async {
    final prefs = await SharedPreferences.getInstance();

    final userData = await userDetailService.getUserDetails();
    log("USERDATA => " + userData.toString());
    name = userData?['name'] ?? '';
    dob = userData?['dob'] ?? '';
    userId = userData?['userId'];
    userImage = prefs.getString('UserImage') ?? '';

    log("USERDATA => Name - " +
        name +
        " DOB - " +
        dob +
        ' Image -' +
        userImage);
    if (!userDetailService.isValidString(name) ||
        !userDetailService.isValidString(dob)) {
      showDetailsDialog();
    }
  }

  void passSeeMoreUserData(Birthday birthday) {
    setState(() {
      seeMoreBirthday = birthday;
      _tabController.animateTo(0);
      _viewModel.fetchPosterData();
      _viewModel.fetchBirthdays();
    });
  }

  void _shareReferralLink(
      String userId, String name, String dob, String userImage) {
    String baseUrl = "https://referral.designboxconsuting.com/";

    String encodedName = Uri.encodeComponent(name);
    String encodedDob = Uri.encodeComponent(dob);
    String encodedImageUrl = Uri.encodeComponent(userImage);
    String shareUrl =
        "$baseUrl?utm_source=organic&utm_medium=referral&userid=$userId&name=$encodedName&dob=$encodedDob&image=$encodedImageUrl";

    Share.share(shareUrl);
    log("Shared Link: $shareUrl");
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder.reactive(
      viewModelBuilder: () => _viewModel,
      onViewModelReady: (viewModel) async {
        await tempVm.initialize();
        viewModel.onModelReady();
      },
      builder: (context, viewModel, child) => Scaffold(
        appBar: AppBar(
          title: const Text('Birthday Poster'),
        ),
        floatingActionButton: _tabController.index == 1
            ? FloatingActionButton.extended(
                backgroundColor: Colors.orange,
                onPressed: () {
                  checkUserDetails().whenComplete(() {
                    _shareReferralLink(userId, name, dob, userImage);
                  });
                },
                label: SizedBox(
                  child: Text(
                    'Refer your friend to add your birthday \n (अपने दोस्त को आपका जन्मदिन जोड़ने के लिए लिंक भेजें)',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18), // You can adjust the font size here
                  ),
                ),
                icon: Icon(
                  Icons.group_add_rounded,
                  color: Colors.white,
                ),
              )
            : null,
        // body: Column(
        //   children: [
        //     Container(
        //       width: double.infinity,
        //       child: Row(
        //         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        //         children: [
        //           Flexible(
        //             flex: 12, // The second button takes more space
        //             child: ElevatedButton.icon(
        //               onPressed: () {
        //                 setState(() {
        //                   isPoster = false;
        //                   isBirthday = true;
        //                   isFriendsBirthday = false;
        //                   viewModel.fetchPosterData();
        //                   viewModel.fetchBirthdays();
        //                 });
        //               },
        //               icon: const Icon(Icons.cake),
        //               label: const Text(
        //                 'Make Birthday Poster',
        //                 maxLines: 2, // Limit the text to 2 lines
        //                 overflow: TextOverflow
        //                     .ellipsis, // Add ellipsis if text overflows
        //               ),
        //             ),
        //           ),
        //           // const SizedBox(width: 6),
        //           Flexible(
        //             flex: 12, // The third button takes up less space
        //             child: ElevatedButton.icon(
        //               onPressed: () {
        //                 setState(() {
        //                   isPoster = false;
        //                   isBirthday = false;
        //                   isFriendsBirthday = true;
        //                 });
        //               },
        //               icon: const Icon(Icons.group),
        //               label: const Text(
        //                 "Make Birthday List",
        //                 maxLines: 2, // Limit the text to 2 lines
        //                 overflow: TextOverflow
        //                     .ellipsis, // Add ellipsis if text overflows
        //               ),
        //             ),
        //           ),
        //         ],
        //       ),
        //     ),
        //     if (isPoster) ...[
        //       _buildPosterContent(viewModel),
        //     ] else if (isBirthday) ...[
        //       _buildBirthdayContent(viewModel),
        //     ] else if (isFriendsBirthday) ...[
        //       // YourFriendsBdayView(
        //       //   isPremium: viewModel.premiumStatus,
        //       // ), // Display the new view
        //       YourFriendsBdayViewRemote(
        //         isPremium: viewModel.premiumStatus,
        //         passSeeMoreUserData: passSeeMoreUserData,
        //       ), // Display the new view
        //     ],
        //   ],
        // ),
        body: Column(
          children: [
            // TabBar
            Container(
              margin: const EdgeInsets.fromLTRB(
                  10, 0, 10, 5), // Adds margin around the TabBar
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              decoration: BoxDecoration(
                color: Colors.grey[200], // Background color of tab bar
                borderRadius: BorderRadius.circular(8), // Rounded edges
              ),
              child: TabBar(
                dividerColor: Colors.transparent,
                controller: _tabController,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.black,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: _selectedColor, // Background color of selected tab
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                tabs: [
                  _buildTab(Icons.cake, 'Make Birthday Poster'),
                  _buildTab(Icons.group, 'Make Birthday List'),
                ],
              ),
            ),

            // TabBarView
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Tab 1: Birthday Poster
                  _buildBirthdayContent(viewModel),

                  // Tab 2: Friends' Birthday List
                  YourFriendsBdayViewRemote(
                    isPremium: viewModel.premiumStatus,
                    passSeeMoreUserData: passSeeMoreUserData,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(IconData icon, String text) {
    return Tab(
      height: 30,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18), // Adjust icon size if needed
          SizedBox(width: 8), // Space between icon and text
          Text(text, style: TextStyle(fontSize: 16)), // Adjust text size
        ],
      ),
    );
  }

  Widget _buildPosterContent(CreatePosterViewModel viewModel) {
    return viewModel.localImages.isNotEmpty
        ? Expanded(
            child: ListView.builder(
              reverse: true,
              itemCount: viewModel.localImages.length,
              itemBuilder: (context, index) {
                String imageUrlOrPath = viewModel.localImages[index];
                bool isNetworkImage = imageUrlOrPath.startsWith('https://');
                return Template_right_1(
                  imageUrl: imageUrlOrPath,
                  premiumStatus: viewModel.premiumStatus,
                  showCTA: true,
                  isPoster: true,
                  isHOme: false,
                  isHttp: isNetworkImage,
                  onImageAdded: () {
                    setState(() {});
                  },
                );
              },
            ),
          )
        : Container(
            padding: EdgeInsets.all(20),
            child: Text(
                "No Poster Data Available.. click on make your poster button and Upload Poster."),
          );
  }

  Birthday? seeMoreBirthday = null;

  Widget _buildBirthdayContent(CreatePosterViewModel viewModel) {
    localBirthday = viewModel.localBirthday;
    // if (viewModel.localBirthday.isEmpty) {
    //   localBirthday = ['Asset/Images/ss.jpeg'];
    // }
    if (localBirthday.isEmpty) {
      localBirthday = ['Asset/Images/ss.jpeg'];
    }

    Birthday? selectedBirthday = seeMoreBirthday != null
        ? seeMoreBirthday
        : viewModel.birthday.isNotEmpty
            ? viewModel.birthday[viewModel.selectedBirthday ?? 0]
            : null;

    return localBirthday.isNotEmpty
        ? SingleChildScrollView(
            physics: AlwaysScrollableScrollPhysics(), // Ensure scrolling
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Birthday List
                SizedBox(height: 4),
                viewModel.birthday.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 25, vertical: 4),
                        child: Text(
                          "Today's Birthday.",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w500),
                        ),
                      )
                    : SizedBox(),
                Padding(
                  padding: const EdgeInsets.fromLTRB(25, 4, 12, 4),
                  child: Wrap(
                    spacing: 3.0,
                    runSpacing: 3.0,
                    children: List.generate(viewModel.birthday.length, (index) {
                      final birthday = viewModel.birthday[index];
                      final isSelected =
                          index == (viewModel.selectedBirthday ?? 0);

                      return SizedBox(
                        height: 30,
                        child: ElevatedButton(
                          onPressed: () async {
                            seeMoreBirthday = null;
                            viewModel.setSelectedBirthday(index);
                            viewModel.notifyListeners();
                            setState(() {});
                          },
                          style: ElevatedButton.styleFrom(
                            foregroundColor:
                                isSelected ? Colors.white : Colors.black,
                            backgroundColor: isSelected
                                ? Colors.red
                                : Colors.orange.withOpacity(0.1),
                            elevation: 0,
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: Text(
                            birthday.name ?? '',
                            style: TextStyle(
                                fontSize: 13, fontFamily: 'Work-Sans'),
                          ),
                        ),
                      );
                    }),
                  ),
                ),

                SizedBox(height: 7),

                // 📜 Poster List (Properly Scrollable)
                ListView.builder(
                  key: ValueKey(selectedBirthday?.posterId),
                  itemCount: localBirthday.length,
                  shrinkWrap: true, // Ensures it takes only required space
                  physics:
                      NeverScrollableScrollPhysics(), // Prevents inner scroll conflicts
                  itemBuilder: (context, index) {
                    String imageUrlOrPath = localBirthday[index];
                    bool isNetworkImage = imageUrlOrPath.startsWith('https://');

                    return Template_right_1(
                      isBirthDay: true,
                      imageUrl: imageUrlOrPath,
                      premiumStatus: viewModel.premiumStatus,
                      showCTA: true,
                      isPoster: true,
                      isHttp: isNetworkImage,
                      isHOme: true,
                      isBottomsheet: true,
                      birthdayName: selectedBirthday?.name ?? null,
                      birthdayPhoto: selectedBirthday?.imagePath ?? null,
                      onImageAdded: () {
                        setState(() {});
                      },
                    );
                  },
                ),
              ],
            ),
          )

        // ? Flexible(
        //     // height: (MediaQuery.of(context).size.height * 0.9) *
        //     //     viewModel.localImages.length,
        //     child: ListView.builder(
        //       reverse: false,
        //       itemCount: localBirthday.length,
        //       // physics: NeverScrollableScrollPhysics(),
        //       itemBuilder: (context, index) {
        //         String imageUrlOrPath = localBirthday[index];

        //         bool isNetworkImage = imageUrlOrPath.startsWith('https://');
        //         print(localBirthday[index]);
        //         print(viewModel.premiumStatus);

        //         return Template_right_1(
        //           isBirthDay: true,
        //           imageUrl: imageUrlOrPath,
        //           premiumStatus: viewModel.premiumStatus,
        //           showCTA: true,
        //           isPoster: true,
        //           isHttp: isNetworkImage,
        //           isHOme: true,
        //           isBottomsheet: true,
        //           onImageAdded: () {
        //             setState(() {});
        //           },
        //         );
        //       },
        //     ),
        //   )
        : Container(
            padding: EdgeInsets.all(20),
            child: Text(
              "No Poster Data Available.. click on make your poster button and Upload Poster.",
            ),
          );
  }

  Future<void> _shareScreenshot() async {
    try {
      // Capture screenshot
      final image = await _userScreenshotController.capture();
      if (image == null) return;

      // Save the image locally
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = File('${directory.path}/screenshot.png');
      await imagePath.writeAsBytes(image);

      // Share on WhatsApp
      await Share.shareXFiles([XFile(imagePath.path)],
          text: "Check out my photo!");
    } catch (e) {
      print("Error sharing screenshot: $e");
    }
  }

  String _displayedName = "Click to add a name"; // Default text
  TextEditingController _nameController = TextEditingController();

  void _editName(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Enter Name'),
        content: TextField(
          controller: _nameController,
          decoration: InputDecoration(hintText: 'Enter your name'),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close the dialog
            },
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _displayedName = _nameController.text.isEmpty
                    ? "No name provided"
                    : _nameController.text;
              });
              Navigator.of(context).pop(); // Close the dialog
            },
            child: Text('Done'),
          ),
        ],
      ),
    );
  }

  String? _selectedImagePath;

  final ImagePicker _imagePicker = ImagePicker();

  Future<void> _pickImage() async {
    // final XFile? pickedImage =
    //     await _imagePicker.pickImage(source: ImageSource.gallery);

    ThemeData themeData = Theme.of(context);
    final XFile? pickedImage = await UserImage().pickImage(themeData);

    if (pickedImage != null) {
      setState(() {
        _selectedImagePath = pickedImage.path;
      });
    }
  }

  Widget Uploaddialog({required CreatePosterViewModel parentViewModel}) {
    return ViewModelBuilder<MakePosterViewModel>.reactive(
        viewModelBuilder: () => MakePosterViewModel(parentViewModel),
        builder: (context, viewModel, child) => Column(
              children: [
                Row(
                  children: [
                    Text(
                      "Select Your Poster",
                      style:
                          TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    Spacer(),
                    IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(Icons.cancel))
                  ],
                ),
                SizedBox(height: 10),
                GestureDetector(
                  onTap: () async {
                    await viewModel.pickImage();
                  },
                  child: DottedBorder(
                    color: Colors.grey,
                    strokeWidth: 2,
                    dashPattern: [6, 6],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(12),
                    child: Container(
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * 0.3,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: viewModel.selectedImagePath != null
                          ? Image.file(
                              File(viewModel.selectedImagePath!),
                              fit: BoxFit.cover,
                            )
                          : const Center(
                              child: Icon(
                                Icons.upload_file,
                                color: Colors.grey,
                                size: 50,
                              ),
                            ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        await viewModel.pickImage();
                      },
                      child: const Text('Pick Poster'),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width * 0.03),
                    ElevatedButton(
                      onPressed: () async {
                        var success = await viewModel.uploadImage();
                        if (success) {
                          viewModel.clearSelectedImage();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Photo uploaded successfully!')),
                          );
                          Navigator.pop(context);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Failed to upload photo.')),
                          );
                        }
                      },
                      child: const Text('Done'),
                    ),
                  ],
                ),
              ],
            ));
  }
}
