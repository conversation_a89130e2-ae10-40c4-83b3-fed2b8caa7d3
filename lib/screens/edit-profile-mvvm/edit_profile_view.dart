import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/global/primary_button.dart';

import 'package:bjpnew/screens/bottombar.dart/create_poster.dart';
import 'package:bjpnew/screens/edit-profile-mvvm/edit_profile_viewmodel.dart';
import 'package:bjpnew/screens/home-mvvm/home_view.dart';
import 'package:stacked/stacked.dart';
import '../../global/CustomSecondaryButton.dart';
import '../../global/TextField.dart';

@RoutePage()
class EditProfileView extends StatefulWidget {
  final VoidCallback? onDetailsSaved;
  final bool? isHome;

  const EditProfileView({super.key, this.onDetailsSaved, this.isHome});

  @override
  State<EditProfileView> createState() => _EditProfileViewState();
}

class _EditProfileViewState extends State<EditProfileView> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<EditProfileViewModel>.reactive(
        viewModelBuilder: () => EditProfileViewModel(),
        onModelReady: (model) => model.loadStoredName(),
        builder: (context, viewModel, child) => Scaffold(
              backgroundColor: themeData.colorScheme.background,
              body: SafeArea(
                child: Scaffold(
                  appBar: AppBar(
                    iconTheme: IconThemeData(color: Colors.white),
                    backgroundColor: themeData.colorScheme.background,
                    elevation: 1,
                    title: Text(
                      'प्रोफाइल एडिट करें/Edit Your Profile',
                      style: TextStyle(
                          fontSize: 20,
                          fontFamily: 'Mukta',
                          fontWeight: FontWeight.w800,
                          color: Colors.black),
                    ),
                  ),
                  body: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        profileDetails(viewModel),
                        callToAction(viewModel),
                        SizedBox(
                          height: 20,
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  Widget profileDetails(EditProfileViewModel viewModel) {
    return Column(
      children: [
        // SizedBox(
        //   height: 10,
        // ),
        // Text(
        //   'Profile Details',
        //   textAlign: TextAlign.center,
        //   style: TextStyle(
        //       fontSize: 16, fontWeight: FontWeight.w400, color: Colors.black87),
        // ),
        SizedBox(
          height: 20,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                label: 'नाम बदलें/Change Name',
                controller: viewModel.nameController,
                onChanged: (value) {
                  viewModel.updateName(value);
                },
              ),
            ),
            _buildRoundCheckbox(true, (value) {
              // setState(() {
              //   checkbox1 = value!;
              // });
            }),
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                  label: 'पद बदलें/Change Position',
                  controller: viewModel.titleController,
                  onChanged: (value) {
                    viewModel.updateTitle(value);
                  }),
            ),
            _buildRoundCheckbox(true, (value) {
              // setState(() {
              //   checkbox2 = value!;
              // });
            }),
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                  label: 'पद का अधिक विवरण भरे/Add More Details To Position',
                  controller: viewModel.secondtitleController,
                  onChanged: (value) {
                    viewModel.updatesecond(value);
                  }),
            ),
            _buildRoundCheckbox(true, (value) {
              // setState(() {
              //   checkbox3 = value!;
              // });
            }),
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                  label: 'नंबर बदलें/Chnage Number',
                  controller: viewModel.numberController,
                  onChanged: (value) {
                    viewModel.updateNumber(value);
                  }),
            ),
            _buildRoundCheckbox(
                viewModel.checkbox4, (value) => viewModel.setCheckbox4(value!)),
          ],
        ),

        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                  label: 'फेसबुक यूज़रनाम/Facebook Username',
                  controller: viewModel.facebookController,
                  onChanged: (value) {
                    viewModel.updatefacebook(value);
                  }),
            ),
            _buildRoundCheckbox(
                viewModel.checkbox5, (value) => viewModel.setCheckbox5(value!))
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                  label: 'ट्विटर यूज़रनाम/Twitter Username',
                  controller: viewModel.twitterController,
                  onChanged: (value) {
                    viewModel.updatetwitter(value);
                  }),
            ),
            _buildRoundCheckbox(
                viewModel.checkbox6, (value) => viewModel.setCheckbox6(value!))
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.9,
              child: CustomTextField(
                  label: 'इंस्टाग्राम यूज़रनाम/Instagram Username',
                  controller: viewModel.instaController,
                  onChanged: (value) {
                    viewModel.updateinsta(value);
                  }),
            ),
            _buildRoundCheckbox(
                viewModel.checkbox7, (value) => viewModel.setCheckbox7(value!)),
          ],
        ),
      ],
    );
  }

  Widget _buildRoundCheckbox(bool value, Function(bool?) onChanged) {
    return GestureDetector(
      onTap: () {
        onChanged(!value);
      },
      child: Container(
        padding: EdgeInsets.all(2),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: value ? Colors.blue : Colors.grey, // Fill color based on state
        ),
        child: Icon(
          value ? Icons.check : null, // Show check icon if selected
          color: Colors.white,
          size: 20.0,
        ),
      ),
    );
  }

  Widget callToAction(EditProfileViewModel viewModel) {
    ThemeData themeData = Theme.of(context);
    return Column(
      children: [
        SizedBox(height: 20),
        // Spacer(),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: PrimaryButton(
              isEnabled: true,
              isLoading: false,
              onTap: () async {
                await viewModel.onSaved();
                await viewModel.saveCheckboxStates();
                widget.onDetailsSaved!.call();
                await Future.delayed(Duration(seconds: 2));

                // Navigator.pop(context);
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => widget.isHome == true
                            ? HomeView(
                                isBottomsheet: false,
                              )
                            : CreatePosterView()));
                //  Navigator.of(context).push(
                //                     MaterialPageRoute(
                //                       builder: (context) =>  HomeView(),
                //                     ),
                //                   );
              },
              color: themeData.colorScheme.primary,
              height: 50,
              label: 'Save Changes',
            )),
        SizedBox(
          height: 8,
        ),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: CustomSecondaryButton(
              leadingIcon: '',
              onPressed: () async {
                Navigator.pop(context);
              },
              buttonText: 'Cancel Changes',
              showIcon: false,
              buttonColor: Colors.transparent,
            )),
      ],
    );
  }
}
