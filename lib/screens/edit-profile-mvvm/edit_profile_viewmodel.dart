import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';

import '../../utils/Singletons/prefs_singleton.dart';
import 'package:http/http.dart' as http;
import 'package:fluttertoast/fluttertoast.dart';

class EditProfileViewModel extends BaseViewModel {
  //instances
  final Prefs _prefs = Prefs.instance;
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  //getters and setters
  final TextEditingController _nameController = TextEditingController();
  TextEditingController get nameController => _nameController;

  final TextEditingController _titleController = TextEditingController();
  TextEditingController get titleController => _titleController;
  final TextEditingController _secondtitleController = TextEditingController();
  TextEditingController get secondtitleController => _secondtitleController;
  final TextEditingController _facebookController = TextEditingController();
  TextEditingController get facebookController => _facebookController;
  final TextEditingController _twitterController = TextEditingController();
  TextEditingController get twitterController => _twitterController;

  final TextEditingController _instaController = TextEditingController();
  TextEditingController get instaController => _instaController;

  final TextEditingController _numberController = TextEditingController();
  TextEditingController get numberController => _numberController;

  //functions required for business logic
  // Future saveAndExit() async {
  //   UserDatabase(
  //       userPhone: int.parse(_numberController.text),
  //       userName: _nameController.text,
  //       userTitle: _titleController.text).createUserDatabase();
  //   await setSharedPreferences(
  //       _nameController.text,
  //       _titleController.text,
  //       int.parse(_numberController.text));
  // }
  void updateName(String value) {
    _nameController.text = value;
    notifyListeners();
  }

  void updateTitle(String value) {
    _titleController.text = value;
    notifyListeners();
  }

  void updateNumber(String value) {
    _numberController.text = value;
    notifyListeners();
  }

  void updatesecond(String value) {
    _secondtitleController.text = value;
    notifyListeners();
  }

  void updatefacebook(String value) {
    _facebookController.text = value;
    notifyListeners();
  }

  void updatetwitter(String value) {
    _twitterController.text = value;
    notifyListeners();
  }

  void updateinsta(String value) {
    _instaController.text = value;
    notifyListeners();
  }

  Future<void> loadStoredName() async {
    final prefs = await SharedPreferences.getInstance();

    _nameController.text = prefs.getString("Name") ?? '';
    _titleController.text = prefs.getString("Title") ?? '';
    _secondtitleController.text = prefs.getString("secondTitle") ?? '';
    _facebookController.text = prefs.getString("facebookId") ?? '';
    _twitterController.text = prefs.getString("twitterid") ?? '';
    _instaController.text = prefs.getString("instaid") ?? '';
    //  int? number = prefs.getInt("number") ?? 0;
    _numberController.text = prefs.getString("number") ?? "";

    _checkbox4 = prefs.getBool('checkbox4') ?? false;
    _checkbox5 = prefs.getBool('checkbox5') ?? false;
    _checkbox6 = prefs.getBool('checkbox6') ?? false;
    _checkbox7 = prefs.getBool('checkbox7') ?? false;

    notifyListeners();
  }

  void _showSelectionLimitToast() {
    Fluttertoast.showToast(
      msg: "You can only select 2 options from number, fb, insta, and twitter.",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );
  }

  Future<void> onSaved({
    bool isTestUser = false,
    BuildContext? context,
  }) async {
    _isLoading = true;
    notifyListeners();

    final url =
        Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/user');
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');

    if (token == null || userId == null) {
      // Handle case when token or userId is null
      print('Token or userId not found in SharedPreferences');
      _isLoading = false;
      notifyListeners();
      return;
    }

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token,
      'app-user-id': userId,
      "DEVICE_ID": deviceId ?? '',
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final cleanedName =
        _nameController.text.replaceAll(RegExp(r'[^\x00-\x7F]'), '');
    final cleanedNumber =
        _numberController.text.replaceAll(RegExp(r'[^\x00-\x7F]'), '');
    final cleanedDescription =
        _titleController.text.replaceAll(RegExp(r'[^\x00-\x7F]'), '');

    final body = jsonEncode(<String, dynamic>{
      'number': cleanedNumber,
      'name': cleanedName,
      'description': cleanedDescription,
    });
    print(cleanedNumber + cleanedName + cleanedDescription);

    try {
      final response = await http.put(url, headers: headers, body: body);
      if (response.statusCode == 200) {
        // AutoRouter.of(context).push(PartyListViewRoute());
        // Handle success
        print('User details updated successfully');
        await _prefs.setString('Name', nameController.text);
        await _prefs.setString('name', nameController.text);
        await _prefs.setString('Title', titleController.text);
        await _prefs.setString('number', cleanedNumber);

        if (secondtitleController.text != '') {
          await _prefs.setString('secondTitle', secondtitleController.text);
        }

        if (facebookController.text != '') {
          await _prefs.setString('facebookId', facebookController.text);
        }

        if (twitterController.text != '') {
          await _prefs.setString('twitterid', twitterController.text);
        }

        if (instaController.text != '') {
          await _prefs.setString('instaid', instaController.text);
        }
        // setSharedPreferences(
        //     cleanedName, cleanedDescription, int.parse(cleanedNumber));
        notifyListeners();
        // print(response.body);
        // print(nameController.text + cleanedNumber + cleanedDescription);
        // print(_prefs.getString('Name'));
        // print(_prefs.getString('Title'));
        // Navigate to the next screen or perform any other action
        // } else {
        //   _toastService.showToast('Error: Edit Name, Number, Position');
        // Handle failure
        // AutoRouter.of(context).push(PartyListViewRoute());
        print('Failed to update user details');
        print(response.statusCode);
      }
    } catch (e) {
      //  _toastService.showToast('Error: $e');
      // Handle exception
      print('Exception: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future setSharedPreferences(String name, String title, int number) async {}
  bool _checkbox4 = false;
  bool _checkbox5 = false;
  bool _checkbox6 = false;
  bool _checkbox7 = false;

  bool get checkbox4 => _checkbox4;
  bool get checkbox5 => _checkbox5;
  bool get checkbox6 => _checkbox6;
  bool get checkbox7 => _checkbox7;

  void setCheckbox4(bool value) {
    if (_isValidSelection(value, _checkbox5, _checkbox6, _checkbox7)) {
      _checkbox4 = value;
    } else {
      _showSelectionLimitToast();
    }
    notifyListeners();
  }

  void setCheckbox5(bool value) {
    if (_isValidSelection(_checkbox4, value, _checkbox6, _checkbox7)) {
      _checkbox5 = value;
    } else {
      _showSelectionLimitToast();
    }
    notifyListeners();
  }

  void setCheckbox6(bool value) {
    if (_isValidSelection(_checkbox4, _checkbox5, value, _checkbox7)) {
      _checkbox6 = value;
    } else {
      _showSelectionLimitToast();
    }
    notifyListeners();
  }

  void setCheckbox7(bool value) {
    if (_isValidSelection(_checkbox4, _checkbox5, _checkbox6, value)) {
      _checkbox7 = value;
    } else {
      _showSelectionLimitToast();
    }
    notifyListeners();
  }

  bool _isValidSelection(bool cb1, bool cb2, bool cb3, bool cb4) {
    int selectedCount =
        (cb1 ? 1 : 0) + (cb2 ? 1 : 0) + (cb3 ? 1 : 0) + (cb4 ? 1 : 0);
    return selectedCount <= 4;
  }

  Future<void> saveCheckboxStates() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool('checkbox4', _checkbox4);
    prefs.setBool('checkbox5', _checkbox5);
    prefs.setBool('checkbox6', _checkbox6);
    prefs.setBool('checkbox7', _checkbox7);
  }
}
