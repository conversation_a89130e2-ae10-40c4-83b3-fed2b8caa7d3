//  import 'package:bjpnew/screens/template/template_right_1.dart';
// import 'package:flutter/material.dart';

// class PaginatedListView extends StatefulWidget {
//   final List<String> allItems;
//   final bool premiumStatus ; // Make sure allItems is final and of type List<String>

//   PaginatedListView({super.key, required this.allItems , required this.premiumStatus});

//   @override
//   _PaginatedListViewState createState() => _PaginatedListViewState();
// }

// class _PaginatedListViewState extends State<PaginatedListView> {
//   List<String> displayedItems = [];
//   int itemsPerPage = 5;
//   int currentPage = 0;
//   bool isLoading = false; // Track if items are being loaded

//   @override
//   void initState() {
//     super.initState();
//     loadMoreItems(); // Load the first set of items initially
//   }

//   Future<void> loadMoreItems() async {
//     if (isLoading) return; // Prevent multiple simultaneous loads

//     setState(() {
//       isLoading = true;
//     });

//     await Future.delayed(Duration(seconds: 1)); // Simulate a delay (e.g., fetching data)

//     int nextPageEndIndex = (currentPage + 1) * itemsPerPage;
//     if (nextPageEndIndex > widget.allItems.length) {
//       nextPageEndIndex = widget.allItems.length; // Prevent overflow
//     }

//     setState(() {
//       displayedItems.addAll(
//         widget.allItems.sublist(currentPage * itemsPerPage, nextPageEndIndex),
//       );
//       currentPage++;
//       isLoading = false; // Reset loading state
//     });
//   }
//   @override
//   Widget build(BuildContext context) {
//     return  displayedItems.isEmpty && !isLoading
//           ? Center(child: CircularProgressIndicator()) // Show loading spinner initially
//           : SizedBox(
//             height:(MediaQuery.of(context).size.height*0.75 )*displayedItems.length + (isLoading ? 1 : 0),
//             child: ListView.builder(
//                 physics: const NeverScrollableScrollPhysics(),
//                 scrollDirection: Axis.vertical,
//                 shrinkWrap: true,

//                 itemCount: displayedItems.length + (isLoading ? 1 : 0),
//                 itemBuilder: (BuildContext context, int index) {
//                   if (index == displayedItems.length) {
//                   // Show a loader at the bottom of the list when more items are being fetched
//                   return Center(child: CircularProgressIndicator());
//                 }else {
//                     String imageUrlOrPath =displayedItems[index];
//                         bool isNetworkImage = imageUrlOrPath.startsWith('https://');
//                     return

//                         Template_right_1(
//                           imageUrl:
//                           displayedItems[index],
//                           premiumStatus:  widget.premiumStatus,
//                           showCTA: true,
//                           isPoster: true,
//                           isHttp:isNetworkImage ,
//                           isHOme: true,
//                           isBottomsheet: true,
//                           onImageAdded: () {
//                             setState(() {});
//                           },

//                         );

//                   }
//                 }, controller: ScrollController()..addListener(() {
//                 // Check if we're at the bottom of the list and trigger more loading if not already loading
//                 if (!isLoading && displayedItems.length < widget.allItems.length) {
//                   loadMoreItems();
//                 }
//               }
//               ) ));
//             // ListView.builder(
//             //   itemCount: displayedItems.length + (isLoading ? 1 : 0),
//             //   itemBuilder: (context, index) {
//             //     if (index == displayedItems.length) {
//             //       // Show a loader at the bottom of the list when more items are being fetched
//             //       return Center(child: CircularProgressIndicator());
//             //     }

//             //     return ListTile(
//             //       title: Text(displayedItems[index]), // Display the string item
//             //     );
//             //   },
//             //   controller: ScrollController()..addListener(() {
//             //     // Check if we're at the bottom of the list and trigger more loading if not already loading
//             //     if (!isLoading && displayedItems.length < widget.allItems.length) {
//             //       loadMoreItems();
//             //     }
//             //   }),
//             // ),

//           // );
//       }
// }
import 'package:bjpnew/screens/template/template_right_1.dart';
import 'package:flutter/material.dart';

class PaginatedListView extends StatefulWidget {
  final List<String> allItems;
  final bool premiumStatus;

  PaginatedListView(
      {super.key, required this.allItems, required this.premiumStatus});

  @override
  _PaginatedListViewState createState() => _PaginatedListViewState();
}

class _PaginatedListViewState extends State<PaginatedListView> {
  List<String> displayedItems = [];
  int itemsPerPage = 5;
  int currentPage = 0;
  bool isLoading = false;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    loadMoreItems(); // Load the first set of items initially
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> loadMoreItems() async {
    if (isLoading || displayedItems.length >= widget.allItems.length) {
      print('No more items to load or already loading');
      return;
    }

    setState(() {
      isLoading = true;
    });

    print('Loading more items...');

    // Simulate a delay for loading items
    await Future.delayed(const Duration(seconds: 1));

    // Calculate the next batch of items to load
    int nextPageEndIndex = (currentPage + 1) * itemsPerPage;

    // Make sure we don't exceed the list size
    if (nextPageEndIndex > widget.allItems.length) {
      nextPageEndIndex = widget.allItems.length;
    }

    // Add the next set of items to the displayedItems list
    setState(() {
      displayedItems.addAll(
        widget.allItems.sublist(currentPage * itemsPerPage, nextPageEndIndex),
      );
      currentPage++;
      isLoading = false;
      print('Total items loaded: ${displayedItems.length}');
    });
  }

  void _onScroll() {
    print('Current position: ${_scrollController.position.pixels}');
    print('Max scroll extent: ${_scrollController.position.maxScrollExtent}');

    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !isLoading) {
      // User has scrolled to the bottom, load the next batch of items
      print('Reached end of list, loading more items...');
      loadMoreItems();
    }
  }

  @override
  Widget build(BuildContext context) {
    return displayedItems.isEmpty && !isLoading
        ? const Center(
            child:
                CircularProgressIndicator()) // Show loading spinner initially
        : SizedBox(
            height: MediaQuery.of(context).size.height *
                1, // For debugging, set a fixed height
            child: ListView.builder(
              controller: _scrollController,
              // physics: const AlwaysScrollableScrollPhysics(),
              itemCount: displayedItems.length + (isLoading ? 1 : 0),
              itemBuilder: (BuildContext context, int index) {
                print(displayedItems.length);
                if (index == displayedItems.length) {
                  return const Center(child: CircularProgressIndicator());
                } else {
                  String imageUrlOrPath = displayedItems[index];
                  bool isNetworkImage = imageUrlOrPath.startsWith('https://');
                  return Template_right_1(
                    imageUrl: imageUrlOrPath,
                    premiumStatus: widget.premiumStatus,
                    showCTA: true,
                    isPoster: true,
                    isHttp: isNetworkImage,
                    isHOme: true,
                    isBottomsheet: true,
                    onImageAdded: () {
                      setState(() {});
                    },
                  );
                }
              },
            ),
          );
  }
}
