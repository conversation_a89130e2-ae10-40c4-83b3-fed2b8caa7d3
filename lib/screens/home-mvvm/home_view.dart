import 'dart:developer';
import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/global/CustomSecondaryButton.dart';
import 'package:bjpnew/locator/app.locator.dart';
import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombarservice.dart';
import 'package:bjpnew/screens/create-birthday-poster/create-birthday-poster.dart';
import 'package:bjpnew/screens/home-mvvm/PosterView.dart';
import 'package:bjpnew/screens/login-mvvm/login-view.dart';
import 'package:bjpnew/screens/party-list/party_list_view.dart';
import 'package:bjpnew/utils/utils.dart';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_launcher_icons/xml_templates.dart';

import 'package:flutter_svg/svg.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/home-mvvm/home_viewmodel.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/AboutUs.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/EULA.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/PrivacyPolicy.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/Refundpolicy.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/Terms.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/mandate.dart';
import 'package:bjpnew/screens/settings-mvvm/settings_view.dart';
import 'package:bjpnew/screens/template/template_right_1.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:bjpnew/services/support_email.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import 'package:bjpnew/global/custom_toast.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class HomeView extends StatefulWidget {
  final bool? isBottomsheet;
  const HomeView({super.key, this.isBottomsheet});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _pageController = PageController(initialPage: _currentPage);
    checkupdate();
    checkBirthdays();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      //showToast(context, 'Share app link with freinds.');
      _checkInstallDate();
    });
  }

  Future<void> checkInstallTime() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int? installTime = prefs.getInt('installTime');

    if (installTime == null) {
      // First app install or first launch
      int currentTime = DateTime.now().millisecondsSinceEpoch;
      prefs.setInt('installTime', currentTime);
    } else {
      // Calculate difference in hours
      int currentTime = DateTime.now().millisecondsSinceEpoch;
      int differenceInMillis = currentTime - installTime;
      int hoursDifference = differenceInMillis ~/ (1000 * 60 * 60);

      // If 24 hours have passed, show the dialog
      if (hoursDifference >= 24) {
        _showOptionsDialog(context);
      }
    }
  }

  void showPopup(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    var popMessage = await prefs.getString('popupMessage');

    if (popMessage != null) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text("Notification"),
            content: Text(popMessage),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text("Done"),
              ),
            ],
          );
        },
      );
    }
  }

  checkBirthdays() async {
    await homeview.checkBirthdays(context);
  }

  checkupdate() async {
    final prefs = await SharedPreferences.getInstance();
    bool forceupdate = prefs.getBool('forceUpdate')!;
    if (forceupdate) {
      _showForceUpdateDialog();
    }
    showPopup(context);
  }

  var name;
  var state;
  var position;
  var appuserid;
  var count = 0;
  var stateline;
  File? userImage;
  HomeViewModel homeview = HomeViewModel();
  TemplatesViewModel viewModel = TemplatesViewModel();

  Future<void> _checkInstallDate() async {
    final prefs = await SharedPreferences.getInstance();
    bool forceupdate = prefs.getBool('forceUpdate')!;
    if (forceupdate) {
      _showForceUpdateDialog();
    }

    name = await prefs.getString("Name");
    position = await prefs.getString("Title");
    state = await prefs.getString('CategorySelected');
    appuserid = await prefs.getString('appuserid');
    stateline = await prefs.getString('stateline1');

    userImage = await UserImage().returnSelectedUserImage(homeview.selectedID);
    checkInstallTime();
  }

  void _showForceUpdateDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // Block the app until update
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Update Required'),
          content: Text(
              'A new version of the app is available. Please update to continue using the app.'),
          actions: [
            ElevatedButton(
              onPressed: _launchPlayStore,
              child: Text('Update'),
            ),
          ],
        );
      },
    );
  }

  void _launchPlayStore() async {
    final packageName =
        'com.postersforb.BB'; // Replace with your app's package name.
    final url = 'market://details?id=$packageName';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      await launch(
          'https://play.google.com/store/apps/details?id=$packageName');
    }
  }

  Widget _buildUserSegment(HomeViewModel viewModel, ThemeData themeData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Poster for Bharat',
          style: TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
        ),
        if (viewModel.isLoading)
          CircularProgressIndicator() // Show a loading indicator
        else
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                viewModel.name,
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                viewModel.email,
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
            ],
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // const IconData assignment_sharp =
    //     IconData(0xe7a8, fontFamily: 'MaterialIcons', matchTextDirection: true);
    // const IconData admin_panel_settings_rounded =
    //     IconData(0xf540, fontFamily: 'MaterialIcons');
    ThemeData themeData = Theme.of(context);

    // TemplatesViewModel viewm=TemplatesViewModel();
    return DefaultTabController(
      length: 2,
      child: ViewModelBuilder<HomeViewModel>.reactive(
        viewModelBuilder: () => HomeViewModel(),
        onViewModelReady: (viewModel) =>
            viewModel.onViewModelReady(context, themeData),
        builder: (context, viewModel, child) => Scaffold(
          backgroundColor: themeData.colorScheme.background,
          drawer: Drawer(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                DrawerHeader(
                  decoration: BoxDecoration(
                    color: themeData.primaryColor,
                  ),
                  child: Column(
                    children: [
                      Center(
                        child: Row(
                          children: [
                            _buildTopSegment(viewModel, themeData),
                            _buildUserSegment(viewModel, themeData),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Text(
                        'This app is under DZINE BOX CONSULTING SOLUTION',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                ListTile(
                  title: Row(
                    children: [
                      SvgPicture.asset('Asset/Icons/PrivateIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('Premium Status',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => SettingsView()));
                  },
                ),
                SizedBox(
                  height: 10,
                ),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 30,
                      // ),
                      SvgPicture.asset('Asset/Icons/PrivateIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('ID Card/Edit Photo',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    AutoRouter.of(context)
                        .push(ProfileViewRoute(onProfileDetailsChange: () {
                      setState(() {});
                    }));
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      SvgPicture.asset('Asset/Icons/PrivateIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('Select State',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    AutoRouter.of(context).push(PartyListViewRoute());
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/PrivateIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('Privacy Policy',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => PrivacyDocument()));
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/Document.svg', height: 30),
                      const SizedBox(width: 8),
                      Text('Terms and Condition',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => TERMSDocument()));
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/Document.svg', height: 30),
                      const SizedBox(width: 8),
                      Text('Legal Agreement',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => EULADocument()));
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/Document.svg', height: 30),
                      const SizedBox(width: 8),
                      Text('Cancellation Policy',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => REFUNDDocument()));
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/Document.svg', height: 30),
                      const SizedBox(width: 8),
                      Text('Subscription Mandate',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(context,
                        MaterialPageRoute(builder: (context) => Mnadate()));
                  },
                ),
                SizedBox(height: 10),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/AboutUs.svg', height: 30),
                      const SizedBox(width: 8),
                      Text('About Us',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () {
                    Navigator.pop(context); // Close the drawer
                    Navigator.push(context,
                        MaterialPageRoute(builder: (context) => AboutUs()));
                  },
                ),
                SizedBox(
                  height: 10,
                ),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/SupportIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('Support',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () async {
                    Navigator.pop(context); // Close the drawer
                    showContactUsDialog(context);
                  },
                ),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/SupportIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('Logout',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: Colors.black))
                    ],
                  ),
                  onTap: () async {
                    final GoogleSignIn googleSignIn = GoogleSignIn();
                    await googleSignIn.signOut();
                    // await GoogleSignIn().disconnect();
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.clear();
                    Navigator.push(context,
                        MaterialPageRoute(builder: (context) => LoginView()));
                  },
                ),
                ListTile(
                  title: Row(
                    children: [
                      // Icon(
                      //   admin_panel_settings_rounded,
                      //   size: 36,
                      //   color: themeData.colorScheme.onBackground,
                      // ),
                      SvgPicture.asset('Asset/Icons/SupportIcon.svg',
                          height: 30),
                      const SizedBox(width: 8),
                      Text('Delete Your Account',
                          style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Work-Sans',
                              fontWeight: FontWeight.w600,
                              color: themeData.colorScheme.onBackground))
                    ],
                  ),
                  onTap: () async {
                    // await handleDelete(viewModel);
                    handleDeleteNew(viewModel);
//                     var success = await viewModel.deleteUser();

//                     if (success == true) {
//                       Navigator.of(context, rootNavigator: true).push(
//                         MaterialPageRoute(
//                           builder: (BuildContext context) {
//                             return LoginView();
//                           },
//                         ),
//                       );
// // Delay navigation to give the UI time to update
// //  locator<BottomBarService>().setBottomBarVisibility(false);
// //   Navigator.pushAndRemoveUntil(
// //     context,
// //     MaterialPageRoute(builder: (context) => LoginView()),
// //     (route) => true,  // Clear all previous routes
// //   );
//                     } else {
//                       showToast(context, 'Something Went Wrong!');
//                     }
                  },
                ),
              ],
            ),
          ),
          appBar: AppBar(
            backgroundColor: Colors.orange.withOpacity(0.7),
            leadingWidth: 30,
            title:
                //  Row(
                //   children: [
                Text(
              ' Poster For Bharat',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            actions: [
              GestureDetector(
                onTap: () {
                  _showOptionsDialog(context);
                },
                child: Container(
                  padding: EdgeInsets.all(4),
                  margin: EdgeInsets.only(right: 12),
                  // height: MediaQuery.of(context).size.height * 0.05,
                  // width: MediaQuery.of(context).size.width * 0.3,
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.black),
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.white),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        'Asset/Icons/Whatsapp-Icon.svg',
                        color: Colors.green,
                        height: MediaQuery.of(context).size.height * 0.03,
                      ),
                      Text(
                        'Share App',
                        style: TextStyle(
                            fontSize:
                                MediaQuery.of(context).size.height * 0.02),
                      ),
                    ],
                  ),
                ),
              )
            ],
            // bottom: PreferredSize(
            //   preferredSize: Size.fromHeight(48.0),
            //   child: Container(
            //     color: Colors.white,
            //     child: TabBar(
            //       tabs: [
            //         Tab(text: 'Posters'),
            //         // Tab(text: 'Videos'),
            //       ],
            //     ),
            //   ),
            // ),
          ),
          body: SafeArea(
            child:
                //  TabBarView(
                //   children: [

                _buildPostersView(viewModel, themeData, true),
            // _buildPostersView(viewModel, themeData, false),
            // _buildVideosView(viewModel, themeData),
            // ],
          ),
        ),

        // ),
      ),
    );
  }

  Future<void> handleDelete(HomeViewModel viewModel) async {
    showConfirmationDialog(
      context: context,
      title: "Confirm Account Deletion",
      content:
          "Are you sure you want to delete your account? If you delete, all your photos and premium subscription (if applicable) will also expire.",
      yesText: "Yes",
      onYesClicked: () {
        showConfirmationDialog(
          context: context,
          title: "Final Confirmation",
          content:
              "This action is irreversible. Are you sure you want to delete your account permanently?",
          yesText: "Yes, Delete",
          onYesClicked: () async {
            var success = await viewModel.deleteUser();
            if (success) {
              // Navigator.of(context, rootNavigator: true).pushReplacement(
              //   MaterialPageRoute(builder: (context) => LoginView()),
              // );
              Navigator.of(context, rootNavigator: true).pushReplacement(
                MaterialPageRoute(
                  builder: (BuildContext context) {
                    return LoginView();
                  },
                ),
              );
            } else {
              showToast(context, 'Something Went Wrong!');
            }
          },
        );
      },
    );
  }

  Future<void> handleDeleteNew(HomeViewModel viewModel) async {
    ThemeData themeData = Theme.of(context);
    final prefs = await SharedPreferences.getInstance();
    var subscriptionStatus = prefs.getString('subscriptionStatus');
    int? remainingDays = viewModel.getRemainingDays();
    if (remainingDays < 0) {
      remainingDays = 0;
    }
    print(remainingDays);
    await showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            contentPadding: EdgeInsets.all(16),
            backgroundColor: Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                LottieBuilder.asset(
                  'Asset/Lottie/alert.json',
                  height: 84,
                ),
                SizedBox(
                  height: 24,
                ),
                Text(
                  'Are you sure you want to delete your account?',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 24,
                      fontFamily: 'Mukta',
                      height: 1.2,
                      color: Colors.black87),
                ),
                SizedBox(
                  height: 12,
                ),
                Text(
                  subscriptionStatus == 'CONDIRMED'
                      ? 'If you delete, all your photos and premium subscription $remainingDays Day\'s will also expire.'
                      : 'If you delete, all your photos will also Delete & premium subscription $remainingDays Day\'s will also expire.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 20,
                      fontFamily: 'Mukta',
                      height: 1.2,
                      color: Colors.black54),
                ),
                SizedBox(
                  height: 24,
                )
              ],
            ),
            actions: [
              PrimaryButton(
                onTap: () => Navigator.pop(context),
                label: 'No, don\'t delete',
                isEnabled: true,
                height: 48,
                isLoading: false,
                color: themeData.colorScheme.primary,
              ),
              SizedBox(
                height: 8,
              ),
              CustomSecondaryButton(
                showIcon: false,
                leadingIcon: '',
                buttonColor: Colors.transparent,
                onPressed: () async {
                  Navigator.pop(context);
                  await showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          contentPadding: EdgeInsets.all(16),
                          backgroundColor: Colors.white,
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              LottieBuilder.asset(
                                'Asset/Lottie/alert.json',
                                height: 84,
                              ),
                              SizedBox(
                                height: 24,
                              ),
                              // Text(
                              //   'Are you sure you want to delete your account?',
                              //   textAlign: TextAlign.center,
                              //   style: TextStyle(
                              //       fontWeight: FontWeight.w600,
                              //       fontSize: 24,
                              //       fontFamily: 'Mukta',
                              //       height: 1.2,
                              //       color: Colors.black87),
                              // ),
                              SizedBox(
                                height: 12,
                              ),
                              Text(
                                "This action is irreversible. Are you sure you want to delete your account permanently?",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 20,
                                    fontFamily: 'Mukta',
                                    height: 1.2,
                                    color: Colors.black54),
                              ),
                              SizedBox(
                                height: 24,
                              )
                            ],
                          ),
                          actions: [
                            PrimaryButton(
                              onTap: () => Navigator.pop(context),
                              label: 'No, don\'t delete',
                              isEnabled: true,
                              height: 48,
                              isLoading: false,
                              color: themeData.colorScheme.primary,
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            CustomSecondaryButton(
                              showIcon: false,
                              leadingIcon: '',
                              buttonColor: Colors.transparent,
                              onPressed: () async {
                                Utils.showLoaderDialog(context);
                                // viewModel.isLoader = true;
                                viewModel.notifyListeners();

                                await GoogleSignIn().disconnect();
                                var sucess = await viewModel.deleteUser();
                                print(sucess);
                                if (sucess == true) {
                                  await viewModel.clearSharedPreferences();
                                  await viewModel.clearCache();
                                  await viewModel.clearAppData();

// Navigator.pushReplacement(
//   context,
//   MaterialPageRoute(builder: (context) => LoginView()),
// );
                                  SchedulerBinding.instance!
                                      .addPostFrameCallback((_) {
                                    if (mounted) {
                                      Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => LoginView()),
                                      );
                                    }
                                  });
                                } else {
                                  showToast(context, 'Something went wrong!');
                                }
                                viewModel.isLoader = true;
                                Utils.hideLoaderDialog(context);
                                viewModel.notifyListeners();
                              },
                              buttonText: viewModel.isLoader
                                  ? 'Processing...'
                                  : 'Yes, Delete',
                            )
                          ],
                        );
                      });
                  //                await GoogleSignIn().disconnect();
                  //  var sucess =  await viewModel.deleteUser();
                  //  print(sucess);
                  //  if(sucess==true){
                  //   final prefs = await SharedPreferences.getInstance();
                  //     await prefs.clear();
                  //     Navigator.push(context,
                  //         MaterialPageRoute(builder: (context) => LoginView()));
                  //  }else {
                  //    showToast(context, 'Something went wrong!');
                  //  }
                },
                buttonText: 'Yes, Delete',
              )
            ],
          );
        });
  }

  void showConfirmationDialog({
    required BuildContext context,
    required String title,
    required String content,
    String yesText = "Yes",
    String noText = "Cancel", // Default value
    required VoidCallback onYesClicked,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context), // Close dialog
              child: Text(noText),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog before action
                onYesClicked();
              },
              child: Text(yesText, style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void showContactUsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Contact Us',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              ElevatedButton.icon(
                icon: Icon(Icons.chat),
                label: Text('Through WhatsApp'),
                onPressed: () async {
                  Navigator.pop(context); // Close the drawer
                  LaunchWhatsApp launchEmail = LaunchWhatsApp();
                  await launchEmail.openWhatsApp();
                },
              ),
              ElevatedButton.icon(
                icon: Icon(Icons.email),
                label: Text('Email Us'),
                onPressed: () async {
                  Navigator.pop(context); // Close the drawer
                  LaunchEmail launchEmail = LaunchEmail();
                  await launchEmail.launchEmail();
                  // Implement email functionality here
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPostersView(
      HomeViewModel viewModel, ThemeData themeData, bool isPoster) {
    return Scaffold(
      backgroundColor: themeData.colorScheme.background,
      body: SingleChildScrollView(
        controller: viewModel.scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
        child: Column(
          children: [
            // GestureDetector(
            //   onTap: () async {
            //     await shareAssetImage();

            //     // await    DownloadShareImage().nonPremiumShare(imageUrl: imageUrl);
            //     // await DownloadShareImage(controller: _controllerr).shareIDCard( );
            //   },
            //   child: Container(
            //     padding: EdgeInsets.all(5),
            //     margin: EdgeInsets.all(5),
            //     // height: MediaQuery.of(context) .size.height*0.05,
            //     // width: MediaQuery.of(context).size.width*0.3,
            //     decoration: BoxDecoration(
            //         borderRadius: BorderRadius.circular(10),
            //         color: Colors.green),
            //     child: Row(
            //       children: [
            //         SvgPicture.asset(
            //           'Asset/Icons/Whatsapp-Icon.svg',
            //           color: Colors.white,
            //           height: MediaQuery.of(context).size.height * 0.05,
            //         ),
            //         SizedBox(
            //           width: 10,
            //         ),
            //         Text(
            //           // 'Share and promote the app\n with party supporters',
            //           'Share and promote the app',
            //           style: TextStyle(
            //               fontSize: MediaQuery.of(context).size.height * 0.025,
            //               color: Colors.white,
            //               fontWeight: FontWeight.bold),
            //         ),
            //       ],
            //     ),
            //   ),
            // ),
            SizedBox(height: 7),
            _buildCategorySegment(context, viewModel, themeData),
            SizedBox(height: 7),
            _buildLazyLoadedView(viewModel, themeData, isPosters: isPoster),
            // SizedBox(height: 240),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  int length = 0;

  HomeViewModel viewMode = HomeViewModel();
  PageController _pageController = PageController();
  int _currentPage = 0;

  Widget _buildCategoryItem(
      category, viewModel, selectedCategoryId, themeData) {
    print(category['name']);
    final categoryId = category['categoryId'] as String?;
    final categoryName = category['name'] as String?;
    final isSelected = categoryId == selectedCategoryId;
    return SizedBox(
      height: 30,
      child: ElevatedButton(
        onPressed: () async {
          if (categoryId != null && categoryName != null) {
            viewModel.onCategorySelected(categoryId);
            // viewModel.saveSelection(categoryId, false);
          }
        },
        style: ElevatedButton.styleFrom(
          foregroundColor: isSelected ? Colors.white : Colors.black,
          backgroundColor: isSelected
              ? Colors.red
              : themeData.colorScheme.primary.withOpacity(0.1),
          elevation: 0,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Text(
          categoryName!,
          style: TextStyle(
            fontSize: 13,
            fontFamily: 'Work-Sans',
          ),
        ),
        // child: Text(
        //   categoryName != null
        //       ? utf8.decode(categoryName.codeUnits)
        //       : 'Unknown',
        //   style: TextStyle(
        //     fontSize: 10,
        //     fontFamily: 'Work-Sans',
        //   ),
        // ),
      ),
    );
  }

  Widget _buildCategorySegment(
      BuildContext context, HomeViewModel viewModel, ThemeData themeData) {
    final categories = viewModel.categories;
    const String defaultCategoryId = '9af1a16a-7852-4a64-8d67-fd6e3987c9de';
    String selectedCategoryId = viewModel.selectedCategorypolitics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 3.0,
          runSpacing: 3.0,
          children: [
            ...categories.take(3).map((category) {
              return _buildCategoryItem(
                  category, viewModel, selectedCategoryId, themeData);
            }).toList(),
          ],
        ),
        SizedBox(
          height: 6,
        ),
        Wrap(
          spacing: 3.0,
          runSpacing: 3.0,
          children: [
            ...categories.skip(3).map((category) {
              return _buildCategoryItem(
                  category, viewModel, selectedCategoryId, themeData);
            }).toList(),
            SizedBox(
              height: 30,
              child: ElevatedButton(
                onPressed: () {
                  AutoRouter.of(context).push(CreatePosterViewRoute());
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Colors.blue, // Customize as needed
                  elevation: 2,
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(
                  "Create Your Poster",
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'Work-Sans',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            //birthday button
            SizedBox(
              height: 30,
              child: ElevatedButton(
                onPressed: () {
                  AutoRouter.of(context).push(BirthdayPosterViewRoute());
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Colors.blue, // Customize as needed
                  elevation: 2,
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(
                  "Birthday",
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'Work-Sans',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLazyLoadedView(HomeViewModel viewModel, ThemeData themeData,
      {required bool isPosters}) {
    print(viewModel.premiumStatus);
    print(viewModel.loadedItems);
    log("CATEGORY Wise ITEMS loadedItems " + viewModel.loadedItems.toString());
    log("CATEGORY Wise ITEMS " + viewModel.posters.toString());

    //  return isPosters == true
    //     ? viewModel.loadedItems.isEmpty ? CircularProgressIndicator():

    //     PaginatedListView(allItems: viewModel.loadedItems,premiumStatus:  viewModel.premiumStatus)

    return isPosters == true
        ? viewModel.posters.isEmpty
            ? Center(child: CircularProgressIndicator())
            : SizedBox(
                child: ListView.builder(
                  // controller: _scrollController,
                  physics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  itemCount: viewModel.posters.length,
                  itemBuilder: (BuildContext context, int index) {
                    print(viewModel.posters.length);
                    if (index == viewModel.posters.length) {
                      return const Center(child: CircularProgressIndicator());
                    } else {
                      String imageUrlOrPath = viewModel.posters[index];
                      bool isNetworkImage =
                          imageUrlOrPath.startsWith('https://');
                      log("IMage CHECKM: " + imageUrlOrPath);

                      // bool showLeader = viewModel.selectedCategory ==
                      //     '9af1a16a-7852-4a64-8d67-fd6e3987c9de';
                      return Template_right_1(
                        imageUrl: imageUrlOrPath,
                        premiumStatus: viewModel.premiumStatus,
                        showCTA: true,
                        isPoster: true,
                        isHttp: isNetworkImage,
                        showLeaderPhotos: true,
                        isHOme: true,
                        isBottomsheet: true,
                        onImageAdded: () {
                          setState(() {});
                        },
                      );
                    }
                  },
                ),
              )

        // return isPosters == true ?
        //     ListView.builder(
        //         physics: const NeverScrollableScrollPhysics(),
        //         scrollDirection: Axis.vertical,
        //         shrinkWrap: true,
        //         itemCount: viewModel.loadedItems.length +
        //             (viewModel.showLoadingIndicator ? 1 : 0),
        //         itemBuilder: (BuildContext context, int index) {
        //           if (viewModel.showLoadingIndicator &&
        //               index == viewModel.loadedItems.length) {
        //              return _buildLoadingIndicator(themeData);
        //           } else {
        //             String imageUrlOrPath =viewModel.loadedItems[index];
        //                 bool isNetworkImage = imageUrlOrPath.startsWith('https://');
        //             return

        //                 Template_right_1(
        //                   imageUrl:
        //                     viewModel.loadedItems[index],
        //                   premiumStatus: viewModel.premiumStatus,
        //                   showCTA: true,
        //                   isPoster: true,
        //                   isHttp:isNetworkImage ,
        //                   isHOme: true,
        //                   isBottomsheet: widget.isBottomsheet,
        //                   onImageAdded: () {
        //                     setState(() {});
        //                   },

        //                 );
        //           }
        //         },
        //       )

        : Container(
            height: MediaQuery.of(context).size.height * 0.7,
            // color: Colors.red,
            child: Center(
                child: Text(
              "Coming soon....",
              style: TextStyle(fontSize: 20),
            ))
//             _showLoadingIndicator
//                 ? Center(child: CircularProgressIndicator())
//                 : PageView.builder(
//                     controller: _pageController,
//                     scrollDirection: Axis.vertical,
//               onPageChanged: (index) {
//                 setState(() {
//                   _currentPage = index;
//                 });
//               },
//                itemCount:   viewModel.loadedItems.length +
//                            (viewModel.showLoadingIndicator ? 1 : 0),
//                     itemBuilder: (BuildContext context, int index) {

//                       length=viewModel.loadedItems.length +
//                         (viewModel.showLoadingIndicator ? 1 : 0);
//                         if(viewModel.loadedItems[index].endsWith('.mp4') ){
//                          count++;
//                         }
//                       print("object");
//                       print(count);
//                       print(viewModel.loadedItems[index].endsWith('.mp4'));

//                       if (viewModel.showLoadingIndicator && index == viewModel.loadedItems.length) {
//                         return _buildLoadingIndicator(themeData);
//                       } else {
//                         print(isPosters);
//                         return
//                              // Padding(
//                             //  padding: const EdgeInsets.symmetric(vertical: 16),
//                            // child:
//                             Stack(
//                           children: [
//                             Template_right_1(
//                               imageUrl: viewModel.loadedItems[index],
//                               premiumStatus: viewModel.premiumStatus,
//                               showCTA: true,
//                               isPoster: false,
//                               onImageAdded: () {
//                                 setState(() {});
//                               },
//                               // onLeaderImageAdded: () {
//                               //   setState(() {  });
//                               // },
//                               // ),
//                             ),
//                             Positioned( top: MediaQuery.of(context).size.height*0.75,
//                             left: MediaQuery.of(context).size.width*0.7,
//                               child:ElevatedButton(
//   onPressed: _nextPage ,
//   style: ElevatedButton.styleFrom(
//     primary: Colors.black, // background color
//     onPrimary: Colors.white, // text color
//     shape: RoundedRectangleBorder(
//       borderRadius: BorderRadius.circular(5), // border radius
//     ),
//   ),
//   child: Text("Next"),
// ))
//                           ],
//                         );
//                       }
//                     },
//                   ),
            );
  }

  Future<Widget> _userImage(HomeViewModel viewModel) async {
    if (viewModel.selectedID != null) {
      var userImage =
          await UserImage().returnSelectedUserImage(viewModel.selectedID);

      try {
        viewModel.saveImageUrl((userImage as File).path);
      } catch (e) {
        print("ERROR _userImage save" + e.toString());
      }

      return Image.file(
        userImage,
        fit: BoxFit.contain,
        width: 260,
        height: 260,
        alignment: Alignment.bottomCenter,
      );
    } else {
      return Container(
          child: SvgPicture.asset(
        'Asset/SVG/ImagePlaceholder.svg',
        fit: BoxFit.contain,
      ));
    }
  }

  Widget _buildLoadingIndicator(ThemeData themeData) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: CircularProgressIndicator(
              color: themeData.colorScheme.outline,
              strokeWidth: 2,
            ),
          ),
          SizedBox(width: 12),
          Text(
            'Loading..',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }

  void _showOptionsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height / 2,
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.04,
              ),
              IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(Icons.cancel)),
              // BottomSheetIdCard(appuserId: appuserid , name: name,state: state,title: position,userImage: userImage!,viewModel: viewModel,isSelect: false,),
              SizedBox(
                height: 10,
              ),

              //  Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   mainAxisAlignment: MainAxisAlignment.center,
              //    children: [
              //      Container(height: 2,width: 150, color: Colors.grey,),
              //       Text("  or / या  " , style: TextStyle(color: Colors.black , fontWeight: FontWeight.bold,backgroundColor: Colors.white),),
              //      Container(height: 2,width: 150,color: Colors.grey,)
              //    ],
              //  ),
              SizedBox(
                height: 20,
              ),
              Center(
                child: Text(
                  'बीजेपी App शेयर करे और अपने लोगो को जोड़े।',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.white,
                    backgroundImage: AssetImage(
                      'Asset/Images/Onboarding-Asset-01.jpeg',
                    ),
                  ),
                  SizedBox(
                    width: 30,
                  ),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.5,
                    child: PrimaryButton(
                      isEnabled: true,
                      height: 48,
                      isLoading: false,
                      onTap: () async {
                        await shareAssetImage();
                        // await    DownloadShareImage().nonPremiumShare(imageUrl: imageUrl);
                        // await DownloadShareImage(controller: _controllerr).shareIDCard( );
                      },
                      label: 'Share APP Link',
                      color: Colors.orange,
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      },
    );

    //  AlertDialog(
    //   title: Text('Choose an Option'),
    //   actions: [
    //     TextButton(
    //       onPressed: () {
    //         Navigator.of(context).pop();
    //         _shareAppLink(context);
    //       },
    //       child: Text('Share App Link'),
    //     ),
    //     TextButton(
    //       onPressed: () async{
    //         Navigator.of(context).pop();
    //         showBottomSheetIdCard(context, viewModel);
    //       },
    //       child: Text('Share ID Card'),
    //     ),
    //   ],
    // );
    // },
    // );
  }

  Future<void> shareAssetImage() async {
    // Step 1: Load the asset image into memory
    final byteData = await rootBundle.load('Asset/Images/share.jpeg');

    // Step 2: Write the image to a temporary file
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/share_image.jpeg');
    await file.writeAsBytes(byteData.buffer.asUint8List());

    // Step 3: Get the share link from SharedPreferences
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var textlist = prefs.getString('shareLink');

    // Step 4: Share the file
    await Share.shareXFiles(
      [XFile(file.path)], // Share the image file
      text:
          'अपने फोटो के साथ अपनी पार्टी का दैनिक पोस्टर पायें अभी ऐप डाउनलोड करें: $textlist', // Add the text
    );
  }

  void _shareAppLink(BuildContext context) {
    // Your logic to share the app link with ad creative
    print('Sharing app link with ad creative');
  }

  Widget _buildTopSegment(HomeViewModel viewModel, ThemeData themeData) {
    return FutureBuilder(
      future: _userImage(viewModel),
      builder: (BuildContext context, AsyncSnapshot<Widget> snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          return Padding(
            padding: const EdgeInsets.only(left: 6, top: 14, right: 8),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 36,
                  backgroundColor: themeData.colorScheme.primary,
                  child: CircleAvatar(
                    radius: 32,
                    backgroundColor: themeData.colorScheme.background,
                    child: GestureDetector(
                      onTap: () {
                        AutoRouter.of(context)
                            .push(ProfileViewRoute(onProfileDetailsChange: () {
                          setState(() {});
                        }));
                      },
                      child: CircleAvatar(
                        radius: 28,
                        backgroundColor: Colors.white,
                        child: ClipRRect(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(28)),
                          child: snapshot.data,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          return CircularProgressIndicator();
        }
      },
    );
  }
}
