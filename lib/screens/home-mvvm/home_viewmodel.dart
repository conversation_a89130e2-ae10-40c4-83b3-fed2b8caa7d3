import 'dart:convert';
import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/services/birthday_service.dart';
import 'package:bjpnew/services/photo_background_removal.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:lottie/lottie.dart';
import 'package:path/path.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/locator/app.locator.dart';
import 'package:bjpnew/services/download_share_image.dart';
import 'package:bjpnew/services/imagesizeservice.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../route/route.gr.dart';
import '../../global/CustomSecondaryButton.dart';
import '../../services/fetch_content.dart';
import '../../services/user_db_operations.dart';
import '../../utils/Singletons/prefs_singleton.dart';
import 'package:http/http.dart' as http;

class HomeViewModel extends BaseViewModel implements Initialisable {
  String _name = "No Name";
  String _email = "No Email";
  bool _isLoading = true;

  String get name => _name;
  String get email => _email;
  bool get isLoading => _isLoading;

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    _name = prefs.getString('name') ?? "No Name";
    _email = prefs.getString('email') ?? "No Email";
    _isLoading = false;
    notifyListeners(); // Notify UI to rebuild
  }

  Future<void> saveImageUrl(String localPath) async {
    String? str = _prefs.getString('UserImage');
    if (str == null || str.trim().isEmpty) {
      String? imageUrl =
          await PhotoBackgroundRemoval().imageUploadWithPath(localPath);
      _prefs.setString('UserImage', imageUrl ?? '');
    }
  }

  //instance variables:
  int _startingDate = DateTime.now().day;
  int _startingMonth = DateTime.now().month;
  int _startingYear = DateTime.now().year;
  int _emptyCount = 0;

  int _currentPage = 0;
  final int _pageSize = 5;
  //getters and setters
  final Prefs _prefs = Prefs.instance;
  Prefs get prefs => _prefs;

  //new .......
  String defaultCategoryId = '9af1a16a-7852-4a64-8d67-fd6e3987c9de';
  String selectedCategorypolitics = '9af1a16a-7852-4a64-8d67-fd6e3987c9de';
  String selectedCategory = '9af1a16a-7852-4a64-8d67-fd6e3987c9de';
  bool _hasMadeSelection = false;
  List<Map<String, String?>> _categories = []; // Store category details
  List<Map<String, String?>> get categories => _categories;
  List<String> _posters = [];
  List<String> get posters => _posters;
  String? _selectedCategoryId;
  String? get selectedCategoryId => _selectedCategoryId;

  void onCategorySelected(String categoryId) {
    // Find the selected category by ID
    final selectedCategoryData = _categories.firstWhere(
      (cat) => cat['categoryId'] == categoryId,
      orElse: () => {}, // Return null if not found
    );

    if (selectedCategoryData != null) {
      // Update the selected category ID in the ViewModel
      selectedCategory = categoryId;
      selectedCategorypolitics = categoryId; // Store category ID
      _hasMadeSelection = true; // for first time select on political tab

      print('Selected category ID: $categoryId');

      // Save selected category by ID
      saveSelection(categoryId, true);

      // Fetch posters for the selected category
      fetchPosters(categoryId);
    }
  }

  Future<void> saveSelection(String categoryId, bool hasVisited) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selected_category_id', categoryId);
    await prefs.setBool('has_visited_party_list', hasVisited);
  }

  Future<void> fetchPosters(String categoryId) async {
    setBusy(true);
    try {
      final urls = await fetchPostersFromApi(categoryId); // Call the API method
      _posters = urls; // Correctly assign the returned value
      _selectedCategoryId = categoryId;
      notifyListeners();
    } catch (e) {
      print('Error fetching posters: $e');
      _posters = [];
    } finally {
      setBusy(false);
    }
  }

  Future<List<String>> fetchPostersFromApi(String categoryId) async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');
    final String? party = prefs.getString('selectedParty') ?? 'BJP';
    String? categorySelection = _prefs.getString('CategorySelected');
    log("CATEGORY Language : " + categorySelection.toString());

    if (token == null || userId == null) {
      print('Token or userId not found in SharedPreferences');
      return [];
    }

    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/post/v1/post/category?categoryId=$categoryId&party=$party&state=$categorySelection');
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'token': token,
      'app-user-id': userId,
      'device-id': deviceId ?? '',
      'CLIENT_VERSION': '1',
      'CLIENT_TYPE': 'ANDROID',
      'CLIENT_VERSION_CODE': '1',
      'package-name': 'com.postersforb.BB'
    };

    try {
      final response = await http.get(url, headers: headers);
      if (response.statusCode == 200) {
        print("categoryid:$categoryId");
        final jsonResponse = jsonDecode(response.body);
        print("response: $jsonResponse");

        final List<String> urls = (jsonResponse['posts'] as List<dynamic>)
            .map((post) => post['url'] as String?)
            .where((url) => url != null)
            .cast<String>()
            .toList();

        log("CATEGORY Wise ITEMS URLS " + urls.toString());
        return urls;
      } else {
        print('Error fetching posters: ${response.statusCode}');
        throw Exception('Failed to load posters');
      }
    } catch (e) {
      print('Error: $e');
      return [];
    }
  }

  Future<void> fetchCategories() async {
    setBusy(true);
    try {
      final fetchContent = FetchContent();
      final categoriesData = await fetchContent.fetchCategories();
      _categories = categoriesData
          .map((item) => {
                'categoryId': item['categoryId'],
                'name': item['name'],
              })
          .toList();
      notifyListeners();
    } catch (e) {
      print('Error fetching categories: $e');
    } finally {
      setBusy(false);
    }
  }

  //--------------------------------------------------//

  final ScrollController _scrollController = ScrollController();
  ScrollController get scrollController => _scrollController;

  int? get selectedID => _prefs.getInt('SelectedID');

  bool get isSubscribedUser => _prefs.getBool('isSubscribedUser') ?? false;

  bool _showRatingWidget = false;
  bool get showRatingWidget => _showRatingWidget;
  set showRatingWidget(value) {
    _showRatingWidget = value;
    notifyListeners();
  }

  bool _premiumStatus = false;
  bool get premiumStatus => _premiumStatus;
  set premiumStatus(value) {
    _premiumStatus = value;
    notifyListeners();
  }

  List<String> _loadedItems = [];
  List<String> get loadedItems => _loadedItems;

  bool _showLoadingIndicator = false;
  bool get showLoadingIndicator => _showLoadingIndicator;

  Future initialise() async {
    await checkForUpdate();
    await getItems();
    await checkPremiumStatus();

    //categories
    await fetchCategories();
    var catId = defaultCategoryId;
    selectedCategory = catId;
    selectedCategorypolitics = catId;
    await fetchPosters(catId);

    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (_emptyCount <= 5 && !_showLoadingIndicator) {
          getItems();
        }
      }
    });
  }

  //functions for business logic:

  Future checkPremiumStatus() async {
    _premiumStatus = await UserDatabase().checkPremiumStatus() ?? false;
    print(_premiumStatus);
    print("premium user");
    notifyListeners();
  }

  Future onViewModelReady(context, themeData) async {
    isSubscribedUser
        ? await subscribedUserWarningDialog(context)
        : await nonSubscribedUserWarningDialog(context, themeData);
    await checkIfRatingAsked(context, themeData);
    _loadUserData();
  }

  // Future shareOnWhatsapp() async {
  //   FirebaseFirestore _firestore = await FirebaseFirestore.instance;
  //   DocumentSnapshot documentSnapshot =
  //       await _firestore.collection('Share_Image').doc('ShareImage').get();
  //   String? shareImage = documentSnapshot.get('1');
  //   await DownloadShareImage().nonPremiumShare(imageUrl: shareImage!);
  // }
  onrefresh() {
    notifyListeners();
  }

  Future<void> shareOnWhatsapp() async {
    String imageUrl = "Asset/Logo/Poster-App-Logo.png";

    // Call the nonPremiumShare method with the image URL
    await DownloadShareImage().nonPremiumShare(imageUrl: imageUrl);
  }

  Future<bool> deleteUser() async {
    final prefs = await SharedPreferences.getInstance();
    final String token = prefs.getString('token') ?? '';
    final String userId = prefs.getString('userId') ?? '';
    final String? email = prefs.getString('email');
    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/user/v1/user?emailId=$email');

    // Headers as specified in the curl command
    final headers = {
      'TOKEN': token, // Replace with actual token value
      'app-user-id': userId, // Replace with actual user ID value
    };
    print(headers);
    try {
      final response = await http.delete(url, headers: headers);

      print(response.body);
      if (response.statusCode == 200) {
        print('User deleted successfully');
        await prefs.remove('token');
        await prefs.remove('userId');
        await prefs.remove('isOnboarded');

        return true;
      } else {
        print('Failed to delete user: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('Error: $e');
      return false;
    }
  }

  void setDate() {
    if (_startingDate == 1 && _startingMonth != 1) {
      if (_startingMonth == 3 ||
          _startingMonth == 5 ||
          _startingMonth == 7 ||
          _startingMonth == 8 ||
          _startingMonth == 10 ||
          _startingMonth == 12) {
        _startingDate = 30;
        _startingMonth--;
      } else {
        _startingDate = 31;
        _startingMonth--;
      }
    } else if (_startingDate == 1 && _startingMonth == 1) {
      _startingDate = 31;
      _startingMonth = 12;
      _startingYear--;
    } else {
      _startingDate--;
    }
  }

  Future<void> getItems() async {
    if (_showLoadingIndicator) return;

    _showLoadingIndicator = true;
    notifyListeners();

    String? categorySelection = _prefs.getString('CategorySelected');
    List<String> newItems = [];
    List<String> allItems = [];

    bool foundData = false;

    while (!foundData && _emptyCount <= 5) {
      allItems = await FetchContent(
              // date: _startingDate,
              // month: _startingMonth,
              // year: _startingYear,
              selectedCategory: categorySelection)
          .returnContent();
      print(allItems.length);
      if (allItems.isNotEmpty) {
        foundData = true;
        newItems =
            allItems.where((item) => !loadedItems.contains(item)).toList();
        loadedItems.addAll(newItems);
        setDate();
        _emptyCount = 0;
      } else {
        foundData = false;
        _emptyCount++;
        setDate();
      }
    }
    _showLoadingIndicator = false;
    notifyListeners();
  }
  // Future<void> getItems() async {
  //   if (_showLoadingIndicator) return; // Prevent multiple fetches
  //   _showLoadingIndicator = true;
  //   notifyListeners(); // Update the UI

  //   String? categorySelection = _prefs.getString('CategorySelected');
  //   List<String> newItems = [];
  //   List<String> allItems = [];

  //   bool foundData = false;

  //   while (!foundData && _emptyCount <= 5) {
  //     // Fetch the content for the current date or category
  //     allItems = await FetchContent(
  //             // You can pass additional parameters here if needed
  //             selectedCategory: categorySelection)
  //         .returnContent();

  //     if (allItems.isNotEmpty) {
  //       // Filter the items that haven't been loaded yet
  //       newItems =
  //           allItems.where((item) => !loadedItems.contains(item)).toList();

  //       // Add new items to the already loaded list
  //       if (newItems.isNotEmpty) {
  //         foundData = true; // Stop loop when new items are found
  //         loadedItems.addAll(newItems);
  //         print(newItems.length); // Update the loaded items list
  //         print(loadedItems.length);
  //         print(_emptyCount);
  //         print(foundData);
  //         setDate(); // Update the date or parameters for the next load
  //         _emptyCount = 0; // Reset empty count since data was found
  //       } else {
  //         // All fetched items are already loaded, continue fetching more
  //         foundData = false;
  //         _emptyCount++;
  //         setDate(); // Update date for next fetch
  //       }
  //     } else {
  //       // No items were found, try the next set
  //       foundData = false;
  //       _emptyCount++;
  //       setDate();
  //     }
  //   }

  //   _showLoadingIndicator = false; // Hide the loading indicator
  //   notifyListeners(); // Notify the UI to update
  // }

  String? formatBirthdayNames(List<Birthday> birthdays) {
    var names = birthdays.map((b) => b.name).toList();
    return names.isEmpty
        ? ''
        : names.length > 1
            ? '${names.sublist(0, names.length - 1).join(', ')} and ${names.last}'
            : names.first;
  }

  Future showBirthdayDialog(context) async {
    ThemeData themeData = Theme.of(context);
    String birthdayNames = formatBirthdayNames(birthday) ?? '';
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20)),
              content: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Stack(
                    children: [
                      LottieBuilder.asset(
                        'Asset/Lottie/birthdayAnim.json',
                        height: 250,
                      ),
                      LottieBuilder.asset(
                        'Asset/Lottie/confetti.json',
                        height: 250,
                      ),
                    ],
                  ),
                  Text(
                    "Today's Birthday.",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w800,
                        color: Colors.red,
                        fontFamily: 'Mukta'),
                  ),
                  SizedBox(
                    height: 12,
                  ),
                  Text(
                    "$birthdayNames\'s birthday posters are ready, share it now!",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 20,
                        height: 1.4,
                        fontWeight: FontWeight.w400,
                        color: Colors.blueGrey,
                        fontFamily: 'Mukta'),
                  ),
                  SizedBox(
                    height: 36,
                  ),
                  PrimaryButton(
                    isLoading: false,
                    height: 48,
                    onTap: () async {
                      Navigator.pop(context);
                      // AutoRouter.of(context).push(BirthdayPosterViewRoute());
                      AutoRouter.of(context)
                          .push(BirthdayPosterViewRoute(pageIndex: 0));
                    },
                    isEnabled: true,
                    label: 'Share Birthday Poster.',
                    color: themeData.colorScheme.primary,
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  CustomSecondaryButton(
                      leadingIcon: '',
                      buttonColor: Colors.transparent,
                      showIcon: false,
                      onPressed: () async {
                        Navigator.pop(context);
                      },
                      buttonText: 'Close')
                ],
              ));
        });
  }

  Future<void> clearAppData() async {
    final appDir = await getApplicationSupportDirectory();
    if (appDir.existsSync()) {
      appDir.deleteSync(recursive: true); // Deletes app storage files
    }
  }

  Future<void> clearCache() async {
    final cacheDir = await getTemporaryDirectory();
    if (cacheDir.existsSync()) {
      cacheDir.deleteSync(recursive: true); // Deletes all cache files
    }
  }

  Future<void> clearSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear(); // Clears all stored preferences
  }

  bool isLoader = false;
  int getRemainingDays() {
    int? premiumTill = _prefs.getInt('premiumTill');
    if (premiumTill == null) return 0;

    DateTime now = DateTime.now();
    DateTime premiumTillDate = DateTime.fromMillisecondsSinceEpoch(premiumTill);

    // Calculate the difference in days
    int daysRemaining = premiumTillDate.difference(now).inDays;

    return daysRemaining;
  }

  Future nonSubscribedUserWarningDialog(context, ThemeData themeData) async {
    int? remainingDays = _prefs.getInt("daysLeft");
    if (remainingDays == null) {
      //do nothing
      // this is when first time the value is null
      // second time remaining days will not be null
      // also the shared preference will remain null unless the user is a premium user
    } else if (remainingDays <= 3) {
      return showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    LottieBuilder.asset(
                      'Asset/Lottie/alert.json',
                      height: 84,
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    Text(
                      remainingDays <= 0
                          ? 'आपका प्रीमियम ख़तम हो गया है, पोस्टर शेयर करते रहने के लिए वापस खरीदें'
                          : remainingDays <= 3
                              ? 'आपका प्रीमियम ${remainingDays} दिन में ख़तम होने वाला है'
                              : '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.w800,
                          height: 1.4,
                          color: Colors.red,
                          fontFamily: 'Mukta'),
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    Text(
                      remainingDays <= 0
                          ? ''
                          : remainingDays <= 3
                              ? "इसके बाद आप फोटो के साथ पोस्टर शेयर नहीं कर पायेंगे"
                              : '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 24,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                          fontFamily: 'Mukta'),
                    ),
                    SizedBox(
                      height: 36,
                    ),
                    PrimaryButton(
                      isLoading: false,
                      height: 48,
                      onTap: () async {
                        Navigator.pop(context);
                        AutoRouter.of(context).push(PremiumViewRoute(
                            imageUrl: loadedItems[0], isPoster: false));
                      },
                      isEnabled: true,
                      label: 'प्रीमियम खरीदें',
                      color: themeData.colorScheme.primary,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    CustomSecondaryButton(
                        leadingIcon: '',
                        buttonColor: Colors.transparent,
                        showIcon: false,
                        onPressed: () async {
                          Navigator.pop(context);
                        },
                        buttonText: 'बंद करें')
                  ],
                ));
          });
    }
  }

  Future subscribedUserWarningDialog(context) async {
    int? remainingDays = _prefs.getInt("daysLeft");
    bool? isWarningShown = _prefs.getBool('isWarningShown') ?? false;

    if (remainingDays == null) {
      //do nothing
      // this is when first time the value is null
      // second time remaining days will not be null
      // also the shared preference will remain null unless the user is a premium user
    } else if (remainingDays <= 1 && isWarningShown == false) {
      return showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'प्रीमियम अगले महीने के लिए अपने आप एक्टिवेट हो जाएगा',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w800,
                          height: 1.4,
                          color: Colors.black87,
                          fontFamily: 'Mukta'),
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    Text(
                      'आपका ऑटो-पे एक्टिव है, प्रीमियम राशी अपने आप कट जाएगी',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 20,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87.withAlpha(150),
                          fontFamily: 'Mukta'),
                    ),
                    SizedBox(
                      height: 36,
                    ),
                    PrimaryButton(
                      isLoading: false,
                      onTap: () async {
                        Navigator.pop(context);
                        _prefs.setBool('isWarningShown', true);
                      },
                      isEnabled: true,
                      label: 'ठीक है',
                      color: Colors.grey,
                    ),
                  ],
                ));
          });
    }
  }

  Future checkIfRatingAsked(context, themeData) async {
    String? registerDateString = _prefs.getString('registerDate');
    bool? ratingsAsked = _prefs.getBool('userRatingAsked');
    DateTime registerDate = DateTime.now();

    if (registerDateString == null) {
      _prefs.setString('registerDate', registerDate.toString());
    } else {
      registerDate = DateTime.parse(registerDateString);
      if ((DateTime.now().difference(registerDate).inDays + 1) % 7 == 0 &&
          ratingsAsked == null) {
        await userRatingDialog(context, themeData);
      }
    }
  }

  Future userRatingDialog(context, ThemeData themeData) async {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.topRight,
                    width: MediaQuery.of(context).size.width,
                    child: SvgPicture.asset(
                      'Asset/Icons/cross.svg',
                      height: 36,
                    ),
                  ),
                ),
                LottieBuilder.asset(
                  'Asset/Lottie/PlayStore-rating.json',
                  height: 64,
                  repeat: false,
                ),
                SizedBox(
                  height: 24,
                ),
                Text(
                  'अगर आपको हमारा एप्प पसंद आया तो हमें प्ले स्टोर पर 5 स्टार रेटिंग दें',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Colors.black87,
                      fontSize: 24,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  height: 24,
                ),
                PrimaryButton(
                  isLoading: false,
                  height: 48,
                  onTap: () async {
                    Navigator.pop(context);
                    final Uri url = Uri.parse(
                        'https://play.google.com/store/apps/details?id=com.postersforb.BB');
                    if (!await launchUrl(url,
                        mode: LaunchMode.externalApplication)) {
                      throw Exception('Could not launch $url');
                    }
                    _prefs.setBool('userRatingAsked', true);
                  },
                  label: 'रेटिंग दें',
                  isEnabled: true,
                  color: themeData.colorScheme.primary,
                )
              ],
            ),
          );
        });
  }

  Future<void> checkForUpdate() async {
    InAppUpdate.checkForUpdate().then((updateInfo) {
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        if (updateInfo.immediateUpdateAllowed) {
          // Perform immediate update
          InAppUpdate.performImmediateUpdate().then((appUpdateResult) {
            if (appUpdateResult == AppUpdateResult.success) {
              //App Update successful
            }
          });
        } else if (updateInfo.flexibleUpdateAllowed) {
          //Perform flexible update
          InAppUpdate.startFlexibleUpdate().then((appUpdateResult) {
            if (appUpdateResult == AppUpdateResult.success) {
              //App Update successful
              InAppUpdate.completeFlexibleUpdate();
            }
          });
        }
      }
    });
  }

  List<Birthday> birthday = [];
  BirthdayService bday = BirthdayService();
  Future<void> checkBirthdays(context) async {
    var allBirthdays = await bday.fetchBirthdays();
    final today = DateTime.now();
    var todayBirthdays = allBirthdays.where((birthday) {
      return birthday.dob?.day == today.day &&
          birthday.dob?.month == today.month;
    }).toList();
    birthday = todayBirthdays;

    if (todayBirthdays.isNotEmpty) {
      showBirthdayDialog(context);
    }
    notifyListeners();
  }

  Map<String, String> getStateMessages(String state) {
    switch (state) {
      case 'UTTAR_PRADESH':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'BIHAR':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'CHHATTISGARH':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'HARYANA':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'MADHYA_PRADESH':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'RAJASTHAN':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'UTTARAKHAND':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'HIMACHAL_PRADESH':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'DELHI':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'JHARKHAND':
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
      case 'TELANGANA':
        return {
          'firstse':
              'మీ త్రివర్ణ ID కార్డు పంచుకోండి మరియు మీ సహకారులను చేర్చండి.',
          'secondse':
              'మీ Poster For Bharat ప్రతి ఇంటా త్రివర్ణ ID కార్డు వెంటనే పొందండి. Poster For Bharat యాప్‌ను డౌన్‌లోడ్ చేసుకోండి మరియు ID కార్డు‌తో రోజువారీ Poster For Bharat పోస్టర్‌లను పొందండి.'
        };
      case 'ANDHRA_PRADESH':
        return {
          'firstse':
              'మీ త్రివర్ణ ID కార్డు పంచుకోండి మరియు మీ సహకారులను చేర్చండి.',
          'secondse':
              'మీ Poster For Bharat ప్రతి ఇంటా త్రివర్ణ ID కార్డు వెంటనే పొందండి. Poster For Bharat యాప్‌ను డౌన్‌లోడ్ చేసుకోండి మరియు ID కార్డు‌తో రోజువారీ Poster For Bharat పోస్టర్‌లను పొందండి.'
        };
      case 'GUJARAT':
        return {
          'firstse': 'તમારું તિરંગા ID કાર્ડ શેર કરો અને તમારા લોકોને જોડો.',
          'secondse':
              'તમારું Poster For Bharat ઘર ઘર તિરંગા ID કાર્ડ તરત મેળવો. Poster For Bharat એપ ડાઉનલોડ કરો અને ID કાર્ડ સાથે દૈનિક Poster For Bharat પોસ્ટર્સ મેળવો.'
        };
      case 'KARNATAKA':
        return {
          'firstse':
              'ನಿಮ್ಮ ತ್ರಿವರ್ಣ ID ಕಾರ್ಡ್ ಹಂಚಿಕೊಳ್ಳಿ ಮತ್ತು ನಿಮ್ಮ ತಂಡವನ್ನು ಸೇರಿಸಿ.',
          'secondse':
              'ನಿಮ್ಮ Poster For Bharat ಪ್ರತಿಯೊಂದು ಮನೆಯ ತ್ರಿವರ್ಣ ID ಕಾರ್ಡ್ ತಕ್ಷಣ ಪಡೆಯಿರಿ. Poster For Bharat ಆಪ್ ಅನ್ನು ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ ಮತ್ತು ID ಕಾರ್ಡ್‌ನೊಂದಿಗೆ ದೈನಂದಿನ Poster For Bharat ಪೋಸ್ಟರ್‌ಗಳನ್ನು ಪಡೆಯಿರಿ.'
        };
      case 'ODISHA':
        return {
          'firstse':
              'ଆପଣଙ୍କର ତିରଙ୍ଗା ID କାର୍ଡ ସେୟାର କରନ୍ତୁ ଏବଂ ଆପଣଙ୍କର ସହକାରୀମାନେ ଯୋଡନ୍ତୁ।',
          'secondse':
              'ଆପଣଙ୍କର Poster For Bharat ପ୍ରତ୍ୟେକ ଘର ତିରଙ୍ଗା ID କାର୍ଡ ସତ୍ରଖଣା ପାଆନ୍ତୁ। Poster For Bharat ଆପ୍‌କୁ ଡାଉନ୍‌ଲୋଡ୍ କରନ୍ତୁ ଏବଂ ID କାର୍ଡ ସହିତ ପ୍ରତିଦିନ Poster For Bharat ପୋଷ୍ଟର ମିଳାନ୍ତୁ।'
        };
      case 'TAMIL_NADU':
        return {
          'firstse':
              'உங்கள் திருவிழா ID கார்டைப் பகிருங்கள் மற்றும் உங்கள் குழுவை இணைக்கவும்.',
          'secondse':
              'உங்கள் Poster For Bharat ஒவ்வொரு வீட்டிலும் திருவிழா ID கார்டைப் பெறுங்கள். Poster For Bharat ஆப்பை பதிவிறக்கம் செய்யவும் மற்றும் ID கார்டுடன் தினசரி Poster For Bharat போஸ்டர்களைப் பெறுங்கள்.'
        };
      case 'WEST_BENGAL':
        return {
          'firstse':
              'আপনার তিরঙ্গা ID কার্ড শেয়ার করুন এবং আপনার দলের সদস্যদের যুক্ত করুন।',
          'secondse':
              'আপনার Poster For Bharat প্রত্যেক বাড়িতে ত্রিবর্ণ ID কার্ড তৎক্ষণাৎ পান। Poster For Bharat অ্যাপ ডাউনলোড করুন এবং ID কার্ডের সাথে দৈনিক Poster For Bharat পোস্টার পান।'
        };
      case 'ASSAM':
        return {
          'firstse':
              'আপোনাৰ তিৰংগা ID কাৰ্ড শ্বেয়াৰ কৰক আৰু আপোনাৰ দলোৰ লগৰ দিয়া।',
          'secondse':
              'আপোনাৰ Poster For Bharat প্ৰতিটো ঘৰ তিৰংগা ID কাৰ্ড ততালিকে পাব। Poster For Bharat এপ ডাউনলোড কৰক আৰু ID কাৰ্ডৰ সৈতে দৈনিক Poster For Bharat প\'ষ্টাৰো পাব।'
        };
      case 'KERALA':
        return {
          'firstse':
              'നിങ്ങളുടെ ത്രിവർണ ID കാർഡ് പങ്കിടുക, നിങ്ങളുടെ കൂട്ടാക്കൾക്ക് ചേർക്കുക.',
          'secondse':
              'നിങ്ങളുടെ Poster For Bharat ഓരോ വീടിനും ത്രിവർണ ID കാർഡ് ഉടന്‍ ലഭിക്കൂ. Poster For Bharat ആപ്പ് ഡൗണ്‍ലോഡ് ചെയ്യുക, ID കാർഡിനൊപ്പം പ്രതിദിന Poster For Bharat പോസ്റ്ററുകളും ലഭിക്കൂ.'
        };
      case 'PUNJAB':
        return {
          'firstse': 'ਆਪਣਾ ਤਿਰੰਗਾ ID ਕਾਰਡ ਸਾਂਝਾ ਕਰੋ ਅਤੇ ਆਪਣੇ ਸਾਥੀਆਂ ਨੂੰ ਜੋੜੋ।',
          'secondse':
              'ਆਪਣਾ Poster For Bharat ਹਰ ਘਰ ਤਿਰੰਗਾ ID ਕਾਰਡ ਤੁਰੰਤ ਪਾਓ। Poster For Bharat ਐਪ ਡਾਊਨਲੋਡ ਕਰੋ ਅਤੇ ID ਕਾਰਡ ਦੇ ਨਾਲ ਦੈਨਿਕ Poster For Bharat ਪੋਸਟਰ ਵੀ ਪਾਓ।'
        };
      case 'MAHARASTRA':
        return {
          'firstse':
              'आपला तिरंगा ID कार्ड शेअर करा आणि आपल्या टीमला जोडण्याचे करा.',
          'secondse':
              'आपला Poster For Bharat प्रत्येक घर तिरंगा ID कार्ड त्वरित मिळवा. Poster For Bharat अ‍ॅप डाउनलोड करा आणि ID कार्डसह दैनिक Poster For Bharat पोस्टर मिळवा.'
        };
      case 'GOA':
        return {
          'firstse': 'तुमचो तिरंगा ID कार्ड शेअर करात आनी तुमच्या टीमला जोडात.',
          'secondse':
              'तुमचो Poster For Bharat हर घर तिरंगा ID कार्ड ताबडतो मिळय. Poster For Bharat ऐप डाउनलोड करात आणि ID कार्ड संग डेली Poster For Bharat पोस्टर पावचो.'
        };
      default:
        return {
          'firstse': 'हर घर तिरंगा ID कार्ड शेयर करे और अपने लोगो को जोड़े।',
          'secondse':
              'अपना पोस्टर फॉर भारत हर घर तिरंगा ID कार्ड तुरंत पाए। Poster For Bharat ऐप डाउनलोड करे और डेली पोस्टर फॉर भारत पोस्टर भी पाए ID कार्ड के साथ।'
        };
    }
  }
}
