import 'package:stacked/stacked.dart';

class PosterViewModel extends BaseViewModel {
  List<String> loadedItems = [];

  PosterViewModel(List<String> initialData) {
    loadedItems = initialData;
  }
  bool isLoadingMore = false;
  final int itemsPerPage = 5;

  MyViewModel() {
    // Load initial items
    loadInitialItems();
  }

  Future<void> loadInitialItems() async {
    setBusy(true);
    loadedItems = await _fetchItems();
    setBusy(false);
  }

  Future<void> loadMoreItems() async {
    if (!isLoadingMore) {
      isLoadingMore = true;
      notifyListeners();

      List<String> newItems = await _fetchItems();
      loadedItems.addAll(newItems);

      isLoadingMore = false;
      notifyListeners();
    }
  }

  Future<List<String>> _fetchItems() async {
    await Future.delayed(const Duration(seconds: 2)); // Simulate network delay
    return List<String>.generate(
        itemsPerPage, (index) => 'Image URL ${loadedItems.length + index}');
  }
}
