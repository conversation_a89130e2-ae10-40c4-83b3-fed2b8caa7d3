// import 'package:auto_route/auto_route.dart';
// import 'package:auto_size_text/auto_size_text.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:bjpnew/route/route.gr.dart';
// import 'package:bjpnew/screens/home-mvvm/home_view.dart';
// import 'package:bjpnew/screens/home-mvvm/home_viewmodel.dart';
// import 'package:bjpnew/screens/template/template_right_1.dart';
// import 'package:stacked/stacked.dart';
// import 'package:bjpnew/services/user_image_operations.dart';

// @RoutePage()
// class HomeTabView extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return DefaultTabController(
//       length: 2,
//       child: Scaffold(
//         appBar: AppBar(
//           title: Text('Home View'),
//           bottom: TabBar(
//             tabs: [
//               Tab(text: 'Posters'),
//               Tab(text: 'Videos'),
//             ],
//           ),
//         ),
//         body: TabBarView(
//           children: [
//             HomeView(),
//             HomeView(),
//           ],
//         ),
//       ),
//     );
//   }
// }
