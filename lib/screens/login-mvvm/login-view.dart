// // import 'package:auto_route/annotations.dart';
// // import 'package:flutter/material.dart';
// // import 'package:bjpnew/global/primary_button.dart';
// // import 'package:bjpnew/screens/login-mvvm/login-viewmodel.dart';
// // import 'package:bjpnew/screens/login-mvvm/testview.dart';
// // import 'package:stacked/stacked.dart';

// // @RoutePage()
// // class LoginView extends StatefulWidget {
// //   const LoginView({super.key});

// //   @override
// //   State<LoginView> createState() => _LoginViewState();
// // }

// // class _LoginViewState extends State<LoginView> {
// //   int count = 0;
// //   @override
// //   Widget build(BuildContext context) {
// //     ThemeData themeData = Theme.of(context);
// //     return ViewModelBuilder<LoginViewModel>.reactive(
// //         viewModelBuilder: () => LoginViewModel(),
// //         builder: (context, model, child) => Scaffold(
// //               backgroundColor: Colors.white,
// //               body: Padding(
// //                 padding: const EdgeInsets.symmetric(horizontal: 16),
// //                 child: Column(
// //                   mainAxisAlignment: MainAxisAlignment.center,
// //                   crossAxisAlignment: CrossAxisAlignment.center,
// //                   children: [
// //                     const Spacer(),
// //                     GestureDetector(
// //                         onTap: () => setState(() {
// //                               if (count < 10) {
// //                                 count++;
// //                               } else {
// //                                 Navigator.push(
// //                                   context,
// //                                   MaterialPageRoute(
// //                                       builder: (context) => TestLogin()),
// //                                 );
// //                               }
// //                               //TODO: test login settings here:
// //                             }),
// //                         //TODO: test login settings here:

// //                         child: Image.asset(
// //                           "Asset/Images/Onboarding-Asset-01.jpeg",
// //                           height: 244,
// //                           // width: 544,
// //                         )),
// //                     const SizedBox(
// //                       height: 32.0,
// //                     ),
// //                     Text(' Posters for B',
// //                         style: TextStyle(
// //                             fontWeight: FontWeight.w800,
// //                             fontSize: 32,
// //                             fontFamily: 'Work-Sans',
// //                             color: themeData.colorScheme.onBackground)),
// //                     const SizedBox(height: 4),
// //                     Text(
// //                       'एक क्लिक में पायें पोस्टर अपनी फोटो के साथ',
// //                       style: TextStyle(
// //                           fontSize: 20,
// //                           fontFamily: 'Mukta',
// //                           fontWeight: FontWeight.w500,
// //                           color: Colors.black87),
// //                     ),
// //                     const SizedBox(
// //                       height: 44.0,
// //                     ),
// //                     PrimaryButton(
// //                       isLoading: model.isLoading,
// //                       isEnabled: true,
// //                       onTap: () async {
// //                         model.isLoading = true;
// //                         await model.authentication(context);
// //                         model.isLoading = false;
// //                       },
// //                       height: 56,
// //                       label: 'रजिस्टर करें',
// //                       iconPath: 'Asset/Icons/Google.svg',
// //                       color: themeData.colorScheme.primary,
// //                     ),
// //                     const Spacer(),
// //                     Text(
// //                       'By signing up you agree to our\n“Terms of Service” and “Privacy Policy',
// //                       style: Theme.of(context).textTheme.bodyMedium?.copyWith(),
// //                       textAlign: TextAlign.center,
// //                     ),
// //                     const SizedBox(
// //                       height: 32.0,
// //                     ),
// //                   ],
// //                 ),
// //               ),
// //             ));
// //   }
// // }

// import 'dart:js_interop';

import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/main.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/global/primary_button.dart';

import 'package:bjpnew/screens/login-mvvm/login-viewmodel.dart';
import 'package:bjpnew/screens/login-mvvm/testview.dart';
import 'package:bjpnew/utils/authentication/authentication.dart';

import 'package:stacked/stacked.dart';

@RoutePage()
class LoginView extends StatefulWidget {
  const LoginView({super.key});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  int count = 0;
  final Authentication _authentication = Authentication();

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);

    return ViewModelBuilder<LoginViewModel>.reactive(
        viewModelBuilder: () => LoginViewModel(),
        builder: (context, model, child) => Scaffold(
              backgroundColor: Colors.white,
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Spacer(),
                    Text(' Poster for Bharat',
                        style: TextStyle(
                            fontWeight: FontWeight.w800,
                            fontSize: 32,
                            fontFamily: 'Work-Sans',
                            color: themeData.colorScheme.onBackground)),
                    const SizedBox(height: 4),
                    Text(
                      'दैनिक पोस्टर फोटो और नाम के साथ',

                      // 'एक क्लिक में पायें पोस्टर अपनी फोटो के साथ',
                      style: TextStyle(
                          fontSize: 20,
                          fontFamily: 'Mukta',
                          fontWeight: FontWeight.w500,
                          color: Colors.black87),
                    ),
                    GestureDetector(
                        onTap: () => setState(() {
                              if (count < 10) {
                                count++;
                              } else {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => TestLogin()),
                                );
                              }
                              //TODO: test login settings here:
                            }),
                        child: Image.asset(
                          "Asset/Images/Onboarding-Asset-01.jpeg",
                          height: 244,
                        )),
                    const SizedBox(
                      height: 32.0,
                    ),
                    const SizedBox(
                      height: 44.0,
                    ),
                    PrimaryButton(
                      // isLoading: model.isLoading,
                      isEnabled: true,
                      onTap: () async {
                        // model.isLoading = true;
                        await _authentication.signInWithGoogle(context);
                        // model.isLoading = false;here is the day w
                      },
                      height: 56,
                      label: 'Register',
                      iconPath: 'Asset/Icons/Google.svg',
                      color: themeData.colorScheme.primary,
                    ),
                    const Spacer(),
                    Text(
                      'By signing up you agree to our\n“Terms of Service” and “Privacy Policy',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(
                      height: 32.0,
                    ),
                  ],
                ),
              ),
            ));
  }
}

// import 'dart:convert';
// import 'package:flutter/material.dart';
// import 'package:google_sign_in/google_sign_in.dart';
// import 'package:http/http.dart' as http;

// class Authentication {
//   static Future<void> signInWithGoogle(BuildContext context) async {
//     final GoogleSignIn googleSignIn = GoogleSignIn();

//     final GoogleSignInAccount? googleSignInAccount =
//         await googleSignIn.signIn();

//     if (googleSignInAccount != null) {
//       final GoogleSignInAuthentication googleSignInAuthentication =
//           await googleSignInAccount.authentication;

//       final idToken = googleSignInAuthentication.idToken;

//       print('ID Token: $idToken');

//       // Call the login endpoint with the token
//       final loginSuccess = await loginUserWithGoogle(idToken!);

//       if (loginSuccess) {
//         // Navigate to the next screen or perform any other action
//       } else {
//         // Handle login failure
//       }
//     }
//   }

//   static Future<bool> loginUserWithGoogle(String idToken) async {
//     final url = Uri.parse(
//         'https://backend.designboxconsuting.com/poster/user/v1/user/login?token=$idToken');
//     final headers = <String, String>{
//       'Content-Type': 'application/json',
//     };

//     try {
//       final response = await http.post(url, headers: headers);
//       if (response.statusCode == 200) {
//         // Login successful
//         print('Login successful: ${response.body}');
//         return true;
//       } else {
//         // Login failed
//         print('Login failed: ${response.statusCode}');
//         print('Response body: ${response.body}');
//         return false;
//       }
//     } catch (e) {
//       print('Error: $e');
//       return false;
//     }
//   }
// }

// class LoginScreen extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Login Screen'),
//       ),
//       body: Center(
//         child: ElevatedButton(
//           onPressed: () {
//             Authentication.signInWithGoogle(context);
//           },
//           child: Text('Sign in with Google'),
//         ),
//       ),
//     );
//   }
// }
// import 'package:flutter/material.dart';
// import 'package:bjpnew/utils/authentication/authentication.dart';

// class LoginView extends StatelessWidget {
//   final Authentication _authentication = Authentication();

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Sign In with Google'),
//       ),
//       body: Center(
//         child: ElevatedButton(
//           onPressed: () {
//             _authentication.signInWithGoogle(context);
//           },
//           child: Text('Sign in with Google'),
//         ),
//       ),
//     );
//   }
// }
