import 'dart:developer';
import 'package:bjpnew/screens/nameplate/thirdnameplate.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:flutter/material.dart';

class FifthNamePlate extends StatefulWidget {
  final TemplatesViewModel viewModel;
  final String logo;
  final Color color;
  final String? fontfamily;

  const FifthNamePlate({
    super.key,
    required this.viewModel,
    required this.logo,
    required this.color,
    required this.fontfamily,
  });

  @override
  State<FifthNamePlate> createState() => _FifthNamePlateState();
}

class _FifthNamePlateState extends State<FifthNamePlate> {
  final GlobalKey _wrapKey = GlobalKey();
  double _lastWrapHeight = 0;

  void _printLineCount() {
    final renderBox = _wrapKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final wrapHeight = renderBox.size.height;

      //Saves height of _wrapKey widget..
      if ((wrapHeight - _lastWrapHeight).abs() > 1) {
        _lastWrapHeight = wrapHeight;
        final lineHeight = 24.0; // Adjust this based on your actual line height
        final numberOfLines = (wrapHeight / lineHeight).ceil();
        log('Wrap is taking $numberOfLines line(s) with height $wrapHeight');
        widget.viewModel.setWrapHeight(wrapHeight);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = widget.viewModel;
    final colorContainer = viewModel.containerColor;
    final mutedColor = viewModel.mutedColor.withAlpha(128);

    // Schedule the line count after build
    WidgetsBinding.instance.addPostFrameCallback((_) => _printLineCount());

    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(color: widget.color),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Logo and name row
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 8, 8, 12),
            child: Row(
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    /// Gradient container behind the logo
                    Padding(
                      padding: EdgeInsets.only(
                        top: 2,
                        left: MediaQuery.of(context).size.height * 0.06,
                      ),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.09,
                        width: MediaQuery.of(context).size.width * 0.41,
                        padding: const EdgeInsets.fromLTRB(40, 4, 6, 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [colorContainer, mutedColor],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(100.0),
                            bottomRight: Radius.circular(100.0),
                          ),
                        ),
                        child: GestureDetector(
                          onTap: () {
                            DialogHelper.showTextSizeDialog(
                              context,
                              viewModel,
                              'text1',
                            );
                          },
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                viewModel.userDetails[0],
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                                softWrap: true,
                                style: TextStyle(
                                  fontFamily: widget.fontfamily,
                                  color: Colors.white,
                                  height: 1.1,
                                  fontSize:
                                      viewModel.getTextSize('text1') ?? 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    /// Logo on top
                    Positioned(
                      left: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 3.0,
                          ),
                        ),
                        child: CircleAvatar(
                          radius: MediaQuery.of(context).size.height * 0.045,
                          backgroundColor: Colors.white,
                          // backgroundImage: NetworkImage(widget.logo),
                          backgroundImage: AssetImage(viewModel.isBjp
                              ? "Asset/Political Logos/BJP-Logo1.png"
                              : "Asset/Political Logos/Congress-Logo.png"),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          Column(
            key: _wrapKey,
            children: [
              /// Designation
              // if (viewModel.checkbox2 == true &&
              if (viewModel.userDetails[1].isNotEmpty) ...[
                Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                  decoration: BoxDecoration(
                    color: viewModel.mutedColor.withAlpha(128), // half-opacity
                  ),
                  child: Text(
                    viewModel.userDetails[1],
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                    maxLines: 1,
                    style: TextStyle(
                      fontFamily: widget.fontfamily,
                      color: Colors.white,
                      fontSize: viewModel.getTextSize('text2'),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Divider(
                  color: Colors.white.withAlpha(128),
                  thickness: 1,
                  height: 1,
                ),
              ],

              /// Contact Info
              viewModel.getSocialCount() > 0 && viewModel.checkAnyTrue()
                  ? Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 6.0, horizontal: 4.0),
                      child: Container(
                        width: double.infinity,
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            // This will be called whenever constraints change
                            WidgetsBinding.instance
                                .addPostFrameCallback((_) => _printLineCount());
                            return Wrap(
                              spacing: 12,
                              alignment: WrapAlignment.spaceAround,
                              runAlignment: WrapAlignment.spaceAround,
                              children: [
                                if (viewModel.number.toString().isNotEmpty
                                     && viewModel.checkbox4 == true
                                    )
                                  _iconText(
                                      Icons.phone, viewModel.number.toString()),
                                if (viewModel.address.toString().isNotEmpty
                                     && viewModel.checkbox4 == true
                                    )
                                  _iconText(Icons.location_on,
                                      viewModel.address.toString()),
                                if (viewModel.facebookId.isNotEmpty
                                    && viewModel.checkbox5 == true
                                    )
                                  _iconTextAsset('Asset/Icons/fb.png',
                                      viewModel.facebookId),
                                if (viewModel.instaId.isNotEmpty
                                    && viewModel.checkbox7 == true
                                    )
                                  _iconTextAsset(
                                      'Asset/Icons/in.png', viewModel.instaId),
                                if (viewModel.twitterId.isNotEmpty
                                    && viewModel.checkbox6 == true
                                    )
                                  _iconTextAsset('Asset/Icons/Twitter X.png',
                                      viewModel.twitterId),
                              ],
                            );
                          },
                        ),
                      ),
                    )
                  : SizedBox()
            ],
          ),
        ],
      ),
    );
  }

  Widget _iconText(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.white),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: const TextStyle(color: Colors.white),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _iconTextAsset(String asset, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            asset,
            height: 16,
            width: 16,
            color: Colors.white,
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: const TextStyle(color: Colors.white),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
