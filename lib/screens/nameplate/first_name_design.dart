import 'package:flutter/material.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplate.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplateviewmodel.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:stacked/stacked.dart';

class FirstNamePlate extends StatelessWidget {
  TemplatesViewModel viewModel;
  String? fontfamily;
  FirstNamePlate({super.key, required this.viewModel, this.fontfamily});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      // padding: EdgeInsets.only(
      //     left: 16,
      //     right:
      //         MediaQuery.of(context).size.width / 2.2,
      //     top: 12,
      //     bottom: 8),
      color: Colors.green,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                // height:
                //  viewModel.userDetails[0].length < 17 ?  MediaQuery.of(context)
                //             .size
                //             .height *
                //         0.09:   MediaQuery.of(context)
                //             .size
                //             .height,
                margin: EdgeInsets.only(
                    top: 2, left: MediaQuery.of(context).size.height * 0.034),
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.13),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    //  Container(
                    //    width: MediaQuery.of(context).size.width*0.18,
                    //   child: FittedBox(
                    //     fit: BoxFit.fitWidth,
                    GestureDetector(
                      onTap: () => DialogHelper.showTextSizeDialog(
                          context, viewModel, 'text1'),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.4,
                        //  height:MediaQuery.of(context).size.width*0.09 ,
                        child: Text(
                          viewModel.userDetails[0],
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                fontFamily: fontfamily,
                                color: Color.fromARGB(255, 198, 63, 1),
                                fontSize: viewModel.getTextSize('text1'),
                                //  MediaQuery.of(context).size.height*0.027 ,
                                fontWeight: FontWeight.w800,
                              ),
                        ),
                      ),
                    ),

                    //   ),
                    // ),

                    SizedBox(
                      height: MediaQuery.of(context).size.width * 0.002,
                    ),
                    GestureDetector(
                      onTap: () => DialogHelper.showTextSizeDialog(
                          context, viewModel, 'text2'),
                      child: Text(
                        viewModel.userDetails[1],
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontFamily: fontfamily,
                              color: Colors.black,
                              fontSize: viewModel.getTextSize('text2'),
                              // MediaQuery.of(context).size.height*0.023,
                              fontWeight: FontWeight.w800,
                            ),
                      ),
                    ),
                    // AutoSizeText(

                    //   viewModel
                    //       .userDetails[1],
                    //   maxFontSize: 14,
                    //   textAlign:
                    //       TextAlign.left,
                    //   style: Theme.of(
                    //           context)
                    //       .textTheme
                    //       .displayMedium
                    //       ?.copyWith(
                    //           color: Colors
                    //               .black,
                    //           fontFamily:
                    //               'Mukta',
                    //           height:
                    //               1.4),
                    //   maxLines: 1,
                    // ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.002,
                    ),
                    viewModel.secondLine != ""
                        ? GestureDetector(
                            onTap: () => DialogHelper.showTextSizeDialog(
                                context, viewModel, 'text2'),
                            child: Text(
                              viewModel.secondLine,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    fontFamily: fontfamily,
                                    color: Colors.black,
                                    fontSize: viewModel.getTextSize('text2'),
                                    //  MediaQuery.of(context).size.height*0.023,
                                    fontWeight: FontWeight.w800,
                                  ),
                            ),
                          )
                        : SizedBox(
                            child: Text(""),
                          ),
                    viewModel.userDetails[0].length < 16
                        ? SizedBox(
                            height: 10,
                          )
                        : SizedBox(),
                  ],
                ),
              ),
              CircleAvatar(
                radius: MediaQuery.of(context).size.height * 0.046,
                backgroundColor: Colors.white,
                backgroundImage: AssetImage(viewModel.isBjp
                    ? "Asset/Political Logos/BJP-Logo.png"
                    : "Asset/Political Logos/Congress-Logo.png"),
              ),
            ],
          ),
          SizedBox(
            height: 1,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 10,
              ),
              viewModel.number.toString() != "" && viewModel.checkbox4 == true
                  ? SizedBox(
                      child: Container(
                          child: Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 15,
                            color: Colors.white,
                          ),
                          Text(
                            viewModel.number.toString(),
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      )),
                    )
                  : SizedBox(),
              SizedBox(
                width: 10,
              ),
              viewModel.facebookId != "" && viewModel.checkbox5 == true
                  ? SizedBox(
                      child: Row(
                      children: [
                        Image.asset(
                          'Asset/Icons/fb.png',
                          height: 15,
                          width: 15,
                        ),
                        Text(
                          viewModel.facebookId ?? '',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ))
                  : SizedBox(
                      child: Text(""),
                    ),
              SizedBox(
                width: 10,
              ),
              viewModel.instaId != "" && viewModel.checkbox7 == true
                  ? SizedBox(
                      child: Row(
                      children: [
                        Image.asset(
                          'Asset/Icons/in.png',
                          height: 15,
                          width: 15,
                        ),
                        Text(
                          viewModel.instaId ?? '',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ))
                  : SizedBox(),
              SizedBox(
                width: 10,
              ),
              viewModel.twitterId != "" && viewModel.checkbox6 == true
                  ? SizedBox(
                      child: Row(
                      children: [
                        Image.asset(
                          'Asset/Icons/Twitter X.png',
                          height: 15,
                          width: 15,
                        ),
                        Text(
                          viewModel.twitterId ?? '',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ))
                  : SizedBox(),
            ],
          )
        ],
      ),
    );
  }
}
