import 'package:flutter/material.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplate.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplateviewmodel.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:stacked/stacked.dart';

class FourthNamePlate extends StatelessWidget {
  TemplatesViewModel viewModel;
  bool isfourth;
  String? fontfamily;
  FourthNamePlate(
      {super.key,
      required this.viewModel,
      required this.isfourth,
      this.fontfamily});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      // padding: EdgeInsets.only(
      //     left: 16,
      //     right:
      //         MediaQuery.of(context).size.width / 2.2,
      //     top: 12,
      //     bottom: 8),
      color: Colors.green,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                // height:
                //     MediaQuery.of(context)
                //             .size
                //             .height *
                //         0.06,
                margin: EdgeInsets.only(
                    top: 2, left: MediaQuery.of(context).size.height * 0.034),
                padding: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.07),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () => DialogHelper.showTextSizeDialog(
                          context, viewModel, 'text1'),
                      child: Container(
                        width: isfourth == true
                            ? MediaQuery.of(context).size.width
                            : MediaQuery.of(context).size.width * 0.47,
                        //   // height: MediaQuery.of(context).size.height*0.01 ,
                        //   child: FittedBox(
                        //     fit: BoxFit.fitWidth,
                        //     child:
                        child: Text(
                          // "pragya aditya aditya",
                          viewModel.userDetails[0],
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                fontFamily: fontfamily,
                                color: Color.fromARGB(255, 198, 63, 1),
                                fontSize: viewModel.getTextSize('text1'),
                                // / MediaQuery.of(context).size.height*0.033,
                                fontWeight: FontWeight.w800,
                              ),
                        ),
                      ),
                    ),
                    // ),

                    SizedBox(
                      height: MediaQuery.of(context).size.width * 0.002,
                    ),
                    GestureDetector(
                      onTap: () => DialogHelper.showTextSizeDialog(
                          context, viewModel, 'text2'),
                      child: Text(
                        viewModel.userDetails[1],
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontFamily: fontfamily,
                              color: Colors.black,
                              fontSize: viewModel.getTextSize('text2'),
                              //  MediaQuery.of(context).size.height*0.026,

                              fontWeight: FontWeight.w800,
                            ),
                      ),
                    ),
                    // AutoSizeText(

                    //   viewModel
                    //       .userDetails[1],
                    //   maxFontSize: 14,
                    //   textAlign:
                    //       TextAlign.left,
                    //   style: Theme.of(
                    //           context)
                    //       .textTheme
                    //       .displayMedium
                    //       ?.copyWith(
                    //           color: Colors
                    //               .black,
                    //           fontFamily:
                    //               'Mukta',
                    //           height:
                    //               1.4),
                    //   maxLines: 1,
                    // ),
                    // SizedBox(
                    //   height: MediaQuery.of(context).size.height*0.002,
                    // ),
                    viewModel.secondLine != ""
                        ? Text(
                            viewModel.secondLine,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(
                                  fontFamily: 'Arya',
                                  color: Colors.black,
                                  fontSize: MediaQuery.of(context).size.height *
                                      0.017,
                                  fontWeight: FontWeight.w800,
                                ),
                          )
                        : SizedBox(),
                  ],
                ),
              ),
              CircleAvatar(
                radius: MediaQuery.of(context).size.height * 0.031,
                backgroundColor: Colors.white,
                backgroundImage: AssetImage(viewModel.isBjp
                    ? "Asset/Political Logos/BJP-Logo.png"
                    : "Asset/Political Logos/Congress-Logo.png"),
              ),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.002,
          ),
        ],
      ),
    );
  }
}
