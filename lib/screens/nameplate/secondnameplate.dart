import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplate.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplateviewmodel.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:stacked/stacked.dart';

class Secondnameplate extends StatelessWidget {
  TemplatesViewModel viewModel;
  String? fontfamily;
  Secondnameplate({super.key, required this.viewModel, this.fontfamily});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: MediaQuery.of(context).size.width,
          // padding: EdgeInsets.only(
          //   left: 16,
          //   right: MediaQuery.of(context)
          //           .size
          //           .width /
          //       2.2,
          //   top: 12,
          // ),
          color: viewModel.containerColor,
          child: Row(
            children: [
              SizedBox(
                width: 10,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 10,
                  ),
                  GestureDetector(
                    onTap: () => DialogHelper.showTextSizeDialog(
                        context, viewModel, 'text1'),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width * 0.45,
                      child: Text(
                        viewModel.userDetails[0],
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontFamily: fontfamily,
                              color: Colors.white,
                              fontSize: viewModel.getTextSize('text1'),
                              //  26,
                              // MediaQuery.of(context).size.height*0.035,
                              fontWeight: FontWeight.w800,
                            ),
                      ),
                    ),
                  ),
                  //   ),
                  // ),

                  SizedBox(
                    height: MediaQuery.of(context).size.width * 0.002,
                  ),
                  GestureDetector(
                    onTap: () => DialogHelper.showTextSizeDialog(
                        context, viewModel, 'text2'),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.height * 0.3,
                      child: Text(
                        viewModel.userDetails[1],
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontFamily: fontfamily,
                              color: Colors.black,
                              fontSize: viewModel.getTextSize('text2'),
                              // MediaQuery.of(context).size.height*0.027,
                              fontWeight: FontWeight.w800,
                            ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 4,
                  )
                ],
              ),
            ],
          ),
        ),
        Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height * 0.023,
          color: Colors.green,
        )
      ],
    );
  }
}
