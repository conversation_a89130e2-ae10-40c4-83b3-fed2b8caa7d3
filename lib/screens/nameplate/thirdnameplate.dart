import 'package:flutter/material.dart';
import 'package:flutter_launcher_icons/xml_templates.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplateviewmodel.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:stacked/stacked.dart';

class ThirdNamePlate extends StatelessWidget {
  TemplatesViewModel viewModel;
  Color? color;
  String? fontfamily;
  ThirdNamePlate(
      {super.key, required this.viewModel, this.color, this.fontfamily});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      // padding: EdgeInsets.only(
      //     left: 16,
      //     right:
      //         MediaQuery.of(context).size.width / 2.2,
      //     top: 12,
      //     bottom: 8),
      color: color,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            children: [
              Column(
                children: [
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height * 0.07,
                    margin: EdgeInsets.only(
                        left: MediaQuery.of(context).size.height * 0.02),
                    padding: EdgeInsets.only(
                        left: MediaQuery.of(context).size.width * 0.084),
                    // color: Colors.white,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () => DialogHelper.showTextSizeDialog(
                              context, viewModel, 'text1'),
                          child: Container(
                            // color: Colors.yellow,
                            width: MediaQuery.of(context).size.width * 0.47,

                            child: Text(
                              viewModel.userDetails[0],
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    fontFamily: fontfamily,
                                    color: Color.fromARGB(255, 198, 63, 1),
                                    fontSize: viewModel.getTextSize('text1'),
                                    //  MediaQuery.of(context).size.height*0.035,

                                    //  MediaQuery.of(context).size.height*0.033,
                                    fontWeight: FontWeight.w800,
                                  ),
                            ),
                          ),
                        ),
                        //   ),
                        // ),

                        // SizedBox(
                        //   height:  MediaQuery.of(context).size.width*0.002,
                        // ),
                        //    Text(
                        //        viewModel
                        //       .userDetails[1]  ,
                        //       style: Theme.of(
                        //           context)
                        //       .textTheme
                        //       .headlineMedium
                        //       ?.copyWith(
                        //         fontFamily:
                        //             'Arya',
                        //         color: Colors.black,
                        //         fontSize:20,
                        //         // MediaQuery.of(context).size.height*0.019,
                        //         fontWeight:
                        //             FontWeight
                        //                 .w800,
                        //       ),),
                        // SizedBox(
                        //   height: MediaQuery.of(context).size.height*0.002,
                        // ),
                        // viewModel.secondLine !=
                        //         ""
                        //     ?  Text(  viewModel
                        //       .userDetails[1],style: Theme.of(
                        //           context)
                        //       .textTheme
                        //       .headlineMedium
                        //       ?.copyWith(
                        //         fontFamily:
                        //             'Arya',
                        //         color: Colors.black,
                        //         fontSize: 20,
                        //         //  MediaQuery.of(context).size.height*0.017,
                        //         fontWeight:
                        //             FontWeight
                        //                 .w800,
                        //       ),)
                        // : SizedBox(),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => DialogHelper.showTextSizeDialog(
                        context, viewModel, 'text2'),
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      // height:
                      //     MediaQuery.of(context)
                      //             .size
                      //             .height *
                      //         0.09,
                      decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(10)),
                      margin: EdgeInsets.only(
                          left: MediaQuery.of(context).size.height * 0.02),
                      padding: EdgeInsets.only(
                          top: 5,
                          bottom: 5,
                          left: MediaQuery.of(context).size.width * 0.084),

                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            viewModel.userDetails[1],
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(
                                  fontFamily: fontfamily,
                                  color: Colors.black,
                                  fontSize: viewModel.getTextSize('text2'),
                                  //  MediaQuery.of(context).size.height*0.027,
                                  // 20,
                                  // MediaQuery.of(context).size.height*0.019,
                                  fontWeight: FontWeight.w800,
                                ),
                          ),
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.002,
                          ),
                          viewModel.secondLine != ""
                              ? Text(
                                  viewModel.secondLine,
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineMedium
                                      ?.copyWith(
                                        fontFamily: fontfamily,
                                        color: Colors.black,
                                        fontSize:
                                            viewModel.getTextSize('text2'),
                                        //  MediaQuery.of(context).size.height*0.027,
                                        fontWeight: FontWeight.w800,
                                      ),
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Positioned(
                top: MediaQuery.of(context).size.height * 0.012,
                child: CircleAvatar(
                  radius: MediaQuery.of(context).size.height * 0.027,
                  backgroundColor: Colors.white,
                  backgroundImage: AssetImage(viewModel.isBjp
                      ? "Asset/Political Logos/BJP-Logo.png"
                      : "Asset/Political Logos/Congress-Logo.png"),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 1,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 10,
              ),
              viewModel.number.toString() != "" && viewModel.checkbox4 == true
                  ? Container(
                      color: Colors.black,
                      child: Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 15,
                            color: Colors.white,
                          ),
                          Text(
                            viewModel.number.toString(),
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    )
                  : SizedBox(),
              SizedBox(
                width: 10,
              ),
              viewModel.facebookId != "" && viewModel.checkbox5 == true
                  ? Container(
                      color: Colors.black,
                      child: Row(
                        children: [
                          Image.asset(
                            'Asset/Icons/fb.png',
                            height: 15,
                            width: 15,
                          ),
                          Text(
                            viewModel.facebookId ?? '',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    )
                  : SizedBox(),
              SizedBox(
                width: 10,
              ),
              viewModel.instaId != "" && viewModel.checkbox7 == true
                  ? Container(
                      color: Colors.black,
                      child: Row(
                        children: [
                          Image.asset(
                            'Asset/Icons/in.png',
                            height: 15,
                            width: 15,
                          ),
                          Text(
                            viewModel.instaId ?? '',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    )
                  : SizedBox(),
              SizedBox(
                width: 10,
              ),
              viewModel.twitterId != "" && viewModel.checkbox6 == true
                  ? Container(
                      color: Colors.black,
                      child: Row(
                        children: [
                          Image.asset(
                            'Asset/Icons/Twitter X.png',
                            height: 15,
                            width: 15,
                          ),
                          Text(
                            viewModel.twitterId ?? '',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    )
                  : SizedBox(),
            ],
          )
        ],
      ),
    );
  }
}

class DialogHelper {
  // Static method to show the bottom sheet
  static void showTextSizeDialog(
      BuildContext context, TemplatesViewModel view, String textKey) {
    // Get initial slider value for the specific text
    double initialSliderValue =
        ((view.getTextSize(textKey) - (textKey == 'text1' ? 20.0 : 18.0)) /
                (textKey == 'text1' ? 20.0 : 18.0)) *
            100;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Container(
              padding: EdgeInsets.all(16),
              height: 200,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Adjust Text Size', style: TextStyle(fontSize: 18)),
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('-30%', style: TextStyle(fontSize: 16)),
                      Expanded(
                        child: Slider(
                          value: initialSliderValue,
                          min: -30,
                          max: 30,
                          divisions: 12, // 10% increments
                          label: '${initialSliderValue.round()}%',
                          onChanged: (newValue) {
                            setState(() {
                              initialSliderValue = newValue;
                            });
                          },
                          onChangeEnd: (newValue) {
                            // When the user stops sliding, change text size
                            view.changeTextSize(textKey, newValue);
                          },
                        ),
                      ),
                      Text('+30%', style: TextStyle(fontSize: 16)),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
