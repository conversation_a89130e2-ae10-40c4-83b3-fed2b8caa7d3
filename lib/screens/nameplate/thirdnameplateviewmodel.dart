// import 'package:stacked/stacked.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// class ThirdnameplateViewModel extends BaseViewModel {
//     // Different default sizes for text1 and text2
//   Map<String, double> _textSizes = {
//     'text1': 20.0, // Default size for text1 is 20
//     'text2': 18.0, // Default size for text2 is 18
//   };

//   bool _isLoading = false;
//   bool get isLoading => _isLoading;

//   static const String textSizeKeyPrefix = 'textSizeKey_';

//   // Constructor to load text sizes when the ViewModel is created
//   ThirdnameplateViewModel() {
//     _loadTextSizes();
//   }

//   // Method to get the text size for a particular key
//   double getTextSize(String key) => _textSizes[key] ?? 16.0;

//   // Method to update the text size for a specific key
//   Future<void> changeTextSize(String key, double sliderValue) async {
//     _isLoading = true;
//     notifyListeners();

//     // Change the text size by the slider value percentage from the default
//     _textSizes[key] = (key == 'text1' ? 20.0 : 18.0) + ((key == 'text1' ? 20.0 : 18.0) * sliderValue / 100);
//     _isLoading = false;
//     notifyListeners();

//     // Save the updated text size
//     await _saveTextSize(key);
//   }

//   // Load the text sizes for all keys
//   Future<void> _loadTextSizes() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     _textSizes['text1'] = prefs.getDouble(textSizeKeyPrefix + 'text1') ?? 20.0;
//     _textSizes['text2'] = prefs.getDouble(textSizeKeyPrefix + 'text2') ?? 18.0;
//     notifyListeners(); // Trigger UI update after loading
//   }

//   // Save the text size for a specific key
//   Future<void> _saveTextSize(String key) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.setDouble(textSizeKeyPrefix + key, _textSizes[key]!);
//   }

// }
