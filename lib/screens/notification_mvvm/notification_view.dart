import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/screens/notification_mvvm/notification_viewmodel.dart';
import 'package:stacked/stacked.dart';

@RoutePage()
class NotificationView extends StatefulWidget {
  const NotificationView({super.key});

  @override
  State<NotificationView> createState() => _NotificationViewState();
}

class _NotificationViewState extends State<NotificationView> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<NotificationViewModel>.reactive(
        viewModelBuilder: () => NotificationViewModel(),
        builder: (context, model, child) => Scaffold(
              body: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LottieBuilder.asset(
                    'Asset/Lottie/notificationBell.json',
                    height: 160,
                  ),
                  SizedBox(
                    height: 56,
                  ),
                  Text('Allow permissions for best\ndaily designs',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontFamily: 'Work-Sans',
                          fontSize: 24,
                          fontWeight: FontWeight.w800,
                          color: themeData.colorScheme.onBackground)),
                  SizedBox(
                    height: 12,
                  ),
                  Text('Press allow to give us proper permissions',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontFamily: 'Work-Sans',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: themeData.colorScheme.onBackground)),
                  SizedBox(
                    height: 24,
                  ),
                  Container(
                      padding: EdgeInsets.all(16),
                      child: PrimaryButton(
                        label: 'Allow permissions',
                        height: 56,
                        isLoading: false,
                        isEnabled: true,
                        color: themeData.colorScheme.primary,
                        onTap: () async {
                          await model.askNotificationPermission(context);
                        },
                      )),
                  SizedBox(
                    height: 32,
                  )
                ],
              ),
            ));
  }
}
