import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/onboarding-mvvm/onboarding_view.dart';
import 'package:stacked/stacked.dart';
import '../../services/permissions_service.dart';

class NotificationViewModel extends BaseViewModel {
  Future<void> askNotificationPermission(context) async {
    await PermissionAccess().requestNotificationsPermissions();
    await PermissionAccess().requestStoragePermission();
    //   Navigator.of(context).push(MaterialPageRoute(
    // builder: (context) => OnboardingDetailsView(token: '',),
// ));

    AutoRouter.of(context).replace(OnboardingDetailsViewRoute());
  }
}
