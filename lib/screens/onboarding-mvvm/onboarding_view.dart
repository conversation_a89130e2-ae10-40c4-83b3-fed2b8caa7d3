// OnboardingDetailsView.dart
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:bjpnew/global/TextField.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/screens/onboarding-mvvm/onboarding_viewmodel.dart';
import 'package:bjpnew/screens/onboarding-mvvm/upload_image.dart';

import 'package:stacked/stacked.dart';

@RoutePage()
class OnboardingDetailsView extends StatefulWidget {
  const OnboardingDetailsView({
    Key? key,
  }) : super(key: key);

  @override
  State<OnboardingDetailsView> createState() => _OnboardingDetailsState();
}

class _OnboardingDetailsState extends State<OnboardingDetailsView> {
  final OnboardingDetailsViewModel _onboardingDetailsViewModel =
      OnboardingDetailsViewModel();

  @override
  void initState() {
    super.initState();
    _onboardingDetailsViewModel.getConfigData();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<OnboardingDetailsViewModel>.reactive(
      viewModelBuilder: () => _onboardingDetailsViewModel,
      onViewModelReady: (model) async => await model.getUserDetails(),
      builder: (context, viewModel, child) => Scaffold(
        backgroundColor: themeData.colorScheme.background,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'Add your details for the poster',
              style: TextStyle(
                fontSize: 24,
                fontFamily: 'Mukta',
                fontWeight: FontWeight.w700,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            CustomTextField(
              label: 'Enter Number',
              // hint: "eg 9999999999",
              controller: viewModel.numberController,
              maxLength:10,
              textInputType: TextInputType.number,
              onChanged: (value) {
                viewModel.numberController.text = value;
                viewModel.clickEnabled();
              },
            ),
            const SizedBox(height: 20),
            CustomTextField(
              label: 'Enter Name',
              hint: 'eg. Ramesh Sharma',
              controller: viewModel.nameController,
              onChanged: (value) {
                viewModel.nameController.text = value;
                viewModel.clickEnabled();
              },
            ),
            const SizedBox(height: 20),
            CustomTextField(
              label: 'Position',
              controller: viewModel.titleController,
              hint: 'eg. Jila Parishad',
              onChanged: (value) {
                viewModel.titleController.text = value;
              },
            ),
            SizedBox(height: 56),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: PrimaryButton(
                color: themeData.colorScheme.primary,
                label: 'Save and Continue',
                height: 52,
                isLoading: false,
                isEnabled: viewModel.isEnabled,
                onTap: () async {
                  if (viewModel.numberController.text.length != 10) {
                    warningSnackbar(viewModel);
                  } else if (viewModel.numberController.text != '' &&
                      viewModel.nameController.text != '' &&
                      viewModel.numberController.text.length == 10) {
                    viewModel.onSaved(isTestUser: false, context: context);
                  } else {
                    successSnackbar(viewModel);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  ScaffoldFeatureController<SnackBar, SnackBarClosedReason> warningSnackbar(
      OnboardingDetailsViewModel viewModel) {
    return ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Container(
        child: Row(
          children: [
            LottieBuilder.asset(
              'Asset/Lottie/warning1.json',
              height: 36,
            ),
            SizedBox(width: 12),
            Text(
              'Enter a valid phone number..',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
      backgroundColor: Colors.red,
    ));
  }

  ScaffoldFeatureController<SnackBar, SnackBarClosedReason> successSnackbar(
      OnboardingDetailsViewModel viewModel) {
    return ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Container(
        child: Row(
          children: [
            LottieBuilder.asset(
              'Asset/Lottie/warning1.json',
              height: 36,
            ),
            SizedBox(width: 12),
            Text(
              'Fill mandatory fields..',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
      backgroundColor: Colors.red,
    ));
  }
}
