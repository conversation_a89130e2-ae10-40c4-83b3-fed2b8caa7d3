import 'dart:convert';
import 'dart:math';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:bjpnew/global/403code.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/onboarding-mvvm/upload_image.dart';
import 'package:bjpnew/utils/Singletons/prefs_singleton.dart';
import 'package:lottie/lottie.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class OnboardingDetailsViewModel extends ChangeNotifier {
  final Prefs prefs = Prefs.instance;
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  final TextEditingController _nameController = TextEditingController();
  TextEditingController get nameController => _nameController;

  final TextEditingController _numberController = TextEditingController();
  TextEditingController get numberController => _numberController;

  final TextEditingController _titleController = TextEditingController();
  TextEditingController get titleController => _titleController;

  bool _isEnabled = false;
  bool get isEnabled => _isEnabled;

  // Future<void> generateAndSaveDeviceId() async {
  //   final prefs = await SharedPreferences.getInstance();
  //   String deviceId = Uuid().v4();
  //   prefs.setString('deviceId', deviceId);
  //   print("deviceId: $deviceId");
  // }
  String generateRandomDeviceId() {
    Random random = Random();
    int randomNumber = random.nextInt(999999999); // Adjust the range as needed
    return randomNumber.toString();
  }

  void clickEnabled() {
    if (numberController.text != '' && nameController.text != '') {
      _isEnabled = true;
      notifyListeners();
    } else {
      _isEnabled = false;
      notifyListeners();
    }
  }

  Future getConfigData() async {
    final prefs = await SharedPreferences.getInstance();
    final String state = prefs.getString('CategorySelected') ?? '';
    final configUrl = Uri.parse(
        'https://backend.designboxconsuting.com/poster/config/v1/config?state=${state}');
    try {
      final response = await http.get(configUrl);
      if (response.statusCode == 200) {
        await Prefs.instance.setString('config', response.body);
        print("config");
        print(response.body);
        var res = jsonDecode(response.body);
        final prefs = await SharedPreferences.getInstance();
        final String? token = prefs.getString('token');
        final String? userId = prefs.getString('userId');

        await prefs.setString('shareLink', res["shareLink"]);
        await prefs.setBool('forceUpdate', res["forceUpdate"]);

        print(res["forceUpdate"]);
        if (res["popupMessage"] != null) {
          await prefs.setBool('popupMessage', true);
        }
        print(userId);
        print(token);
      } else {
        // Handle error
        print('Failed to load config: ${response.statusCode}');
      }
    } catch (e) {
      // Handle network error
      print('Failed to load config: $e');
    }
  }

  Future<String> generateRandomDeviceid() async {
    final prefs = await SharedPreferences.getInstance();
    String deviceId = Uuid().v4();
    prefs.setString('deviceId', deviceId);
    print("Generated deviceId: $deviceId");
    return deviceId;
  }

  Future<void> onSaved({
    required bool isTestUser,
    required BuildContext context,
  }) async {
    _isLoading = true;
    notifyListeners();
    final prefs = await SharedPreferences.getInstance();

    String deviceId = prefs.getString('deviceId') ?? generateRandomDeviceId();
    prefs.setString('deviceId', deviceId);
    print("Retrieved deviceId: $deviceId");

    final url =
        Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/user');
    // final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');

    if (token == null || userId == null) {
      // Handle case when token or userId is null
      print('Token or userId not found in SharedPreferences');
      _isLoading = false;
      notifyListeners();
      return;
    }

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token,
      'app-user-id': userId,
      "DEVICE_ID": deviceId,
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final cleanedName =
        _nameController.text.trim().replaceAll(RegExp(r'[^\x00-\x7F]'), '');
    final cleanedNumber =
        _numberController.text.trim().replaceAll(RegExp(r'[^\x00-\x7F]'), '');
    final cleanedDescription =
        _titleController.text.trim().replaceAll(RegExp(r'[^\x00-\x7F]'), '');

    final body = jsonEncode(<String, dynamic>{
      'number': _numberController.text,
      'name': cleanedName,
      'description': cleanedDescription,
    });

    try {
      final response = await http.put(url, headers: headers, body: body);
      if (response.statusCode == 200) {
         final userData = jsonDecode(response.body);
         final success = userData['success'].toString();
        final message = userData['message'];
        if (success == 'false') {
          successSnackbar(context, 'Error : ${message}');
          return;
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UploadImage(),
          ),
        );

        // AutoRouter.of(context).push(PartyListViewRoute());
        // Handle success
        print('User details updated successfully');
        setPreferences();
        print(response.body);
        // Navigate to the next screen or perform any other action
        // } else if (response.statusCode == 403) {
        // DialogBox.showSessionTimeoutDialog(context);
      } else {
        // Handle failure
        // AutoRouter.of(context).push(PartyListViewRoute());
        print('Failed to update user details');
        print(response.statusCode);
      }
    } catch (e) {
      // Handle exception
      print('Exception: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  ScaffoldFeatureController<SnackBar, SnackBarClosedReason> successSnackbar(
      BuildContext context, String text) {
    return ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Row(
        children: [
          LottieBuilder.asset(
            'Asset/Lottie/warning1.json',
            height: 36,
          ),
          SizedBox(
            width: 12,
          ),
          Text(
            text,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ],
      ),
      backgroundColor: Colors.red,
    ));
  }


  Future<void> getUserDetails() async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String deviceId = prefs.getString('deviceId') ?? '';
    print("device");
    print(deviceId.toString());
    ;

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      "DEVICE_ID": deviceId,
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final url =
        Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/user');
    try {
      final response = await http.get(url, headers: headers);
      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        print(userData);
        final name = userData['name'];
        final title = userData['description'];
        final number = userData['number'];
        final email = userData['email'];
        print('Number from API: $number');
        await prefs.setString('appuserid', userData['appUserId'].toString());

        _nameController.text = name ?? '';
        _titleController.text = title ?? '';
        _numberController.text = number ?? '';
        print("Name: $name, Title: $title, Number: $number, Email: $email");
        print("fetched details");
        //  setPreferences();
        prefs.setString('email', email);
        prefs.setString('number', number);
        prefs.setString('name', name);
        clickEnabled();
        notifyListeners();
      } else {
        print('Failed to fetch user details: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching user details: $e');
    }
  }

  void setPreferences() {
    prefs.setBool('isOnboarded', true);
    prefs.setString('Name', _nameController.text);
    prefs.setString('Title', _titleController.text);
    prefs.setString('number', _numberController.text);
    prefs.setString('registerDate', DateTime.now().toString());
  }
}
