import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombar.dart';
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'onboarding_viewmodel.dart';

@RoutePage()
class SelectPartyView extends StatefulWidget {
  const SelectPartyView({Key? key}) : super(key: key);

  @override
  State<SelectPartyView> createState() => _SelectPartyViewState();
}

class _SelectPartyViewState extends State<SelectPartyView> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<OnboardingDetailsViewModel>.reactive(
      viewModelBuilder: () => OnboardingDetailsViewModel(),
      builder: (context, viewModel, child) => Scaffold(
        backgroundColor: themeData.colorScheme.background,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'Select Your Party',
              style: TextStyle(
                fontSize: 24,
                fontFamily: 'Mukta',
                fontWeight: FontWeight.w700,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPartyButton('BJP', Colors.orange, 'BJP',
                    'Asset/Political Logos/BJP-Logo.png', viewModel),
                const SizedBox(height: 20),
                _buildPartyButton('Congress', Colors.blue, 'CONGRESS',
                    'Asset/Political Logos/Congress-Logo.png', viewModel),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildPartyButton(String title, Color color, String value,
      String image, OnboardingDetailsViewModel viewModel) {
    final themeData = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      // child: ElevatedButton(
      //   style: ElevatedButton.styleFrom(
      //     backgroundColor: color,
      //     minimumSize: Size(double.infinity, 52),
      //   ),
      //   onPressed: () async {
      //     await _savePartySelection(value);
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       SnackBar(
      //         content: Text('$title selected successfully!'),
      //         backgroundColor: Colors.green,
      //       ),
      //     );
      //   },
      //   child: Text(
      //     title,
      //     style: TextStyle(color: Colors.white, fontSize: 18),
      //   ),
      // ),

      child: GestureDetector(
        onTap: () async {
          await _savePartySelection(value);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$title selected successfully!'),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => MyBottomBarView(),
            ),
          );
        },
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: themeData.colorScheme.shadow.withAlpha(30),
                blurRadius: 12,
              )
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                image, // Change the path based on your image
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
              SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Work-Sans',
                  fontSize: 18, // Adjust font size
                  fontWeight: FontWeight.w500,
                  color: themeData.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _savePartySelection(String party) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selectedParty', party);
  }
}
