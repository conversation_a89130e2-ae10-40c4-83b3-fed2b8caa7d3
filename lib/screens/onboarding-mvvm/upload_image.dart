import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bjpnew/global/custom_toast.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/route/route.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/party-list/party_list_view.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:bjpnew/services/photo_background_removal.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UploadImage extends StatefulWidget {
  const UploadImage({super.key});

  @override
  State<UploadImage> createState() => _UploadImageState();
}

class _UploadImageState extends State<UploadImage> {
  TemplatesViewModel viewModel = TemplatesViewModel();
  var isload = false;
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Upload Profile Image',
            style: TextStyle(
              fontSize: 24,
              fontFamily: 'Mukta',
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          GestureDetector(
            onTap: () async {
              var changedImage = await viewModel.userImageChange(context);
              if (changedImage != null) {
                await removeBackground(changedImage, false);
                setState(() {});
              }
            },
            child: FutureBuilder(
                future: viewModel.leaderImage(),
                builder: (context, snapshot) {
                  // print(snapshot.data);
                  return Container(
                    alignment: Alignment.bottomRight,
                    height: 180,
                    width: 180,
                    child: snapshot.data,
                  );
                }),
          ),
          SizedBox(height: 56),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: PrimaryButton(
              color: themeData.colorScheme.primary,
              label: 'Save and Continue',
              height: 52,
              isLoading: false,
              isEnabled: true,
              onTap: () async {
                int? selectedID = await SharedPreferences.getInstance()
                    .then((prefs) => prefs.getInt('SelectedID'));

                if (selectedID == null) {
                  showToast(context, "Upload Your Pic");
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PartyListView(),
                    ),
                  );
                }

                // AutoRouter.of(context).push(PartyListViewRoute());
              },
            ),
          ),
        ],
      ),
    );
  }

  Future removeBackground(XFile? image, bool? leader) {
    ThemeData themeData = Theme.of(context);
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return StreamBuilder(
              stream: PhotoBackgroundRemoval().executeEverything(image),
              builder: (context, snapshot) {
                return AlertDialog(
                  backgroundColor: Colors.white,
                  title: Text(
                    'Removing background, please wait..',
                    textAlign: TextAlign.center,
                  ),
                  content: SingleChildScrollView(
                    child: ListBody(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Container(width: 300, child: snapshot.data),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    snapshot.connectionState == ConnectionState.done
                        ? PrimaryButton(
                            isEnabled: true,
                            isLoading: false,
                            onTap: () {
                              Navigator.pop(context);
                            },
                            label: 'Add Image',
                            color: themeData.colorScheme.primary,
                          )
                        : Center(child: CircularProgressIndicator()),
                  ],
                );
              });
        });
  }
}
