import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/screens/onboarding-mvvm/onboarding_viewmodel.dart';
import 'package:bjpnew/screens/onboarding-mvvm/select_party_view.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombar.dart';

import 'package:bjpnew/screens/home-mvvm/home_view.dart';
import 'package:bjpnew/screens/party-list/party_list_viewModel.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import '../../route/route.gr.dart';

@RoutePage()
class PartyListView extends StatefulWidget {
  const PartyListView({Key? key});

  @override
  State<PartyListView> createState() => _PartyListViewState();
}

class _PartyListViewState extends State<PartyListView> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<PartyListViewModel>.reactive(
      viewModelBuilder: () => PartyListViewModel(),
      builder: (context, viewModel, child) => Scaffold(
        backgroundColor: themeData.colorScheme.background,
        body: SingleChildScrollView(
          child: FutureBuilder<List<Map<String, String>>>(
            future: viewModel.fetchStateInfos(),
            builder: (BuildContext context,
                AsyncSnapshot<List<Map<String, String>>> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: CircularProgressIndicator(
                    color: Colors.black54,
                  ),
                );
              } else if (snapshot.hasError) {
                return Center(
                  child: Text('Error: ${snapshot.error}'),
                );
              } else {
                List<Map<String, String>> stateInfos = snapshot.data ?? [];
                return Column(
                  children: [
                    SizedBox(height: 64),
                    Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: Center(
                        child: Text(
                          'Select Your State',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 28,
                            fontFamily: 'Work-Sans',
                            color: themeData.colorScheme.onBackground,
                          ),
                        ),
                      ),
                    ),
                    ListView.builder(
                      itemCount: stateInfos.length,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (BuildContext context, int index) {
                        // print(stateInfos[index]);
                        return Padding(
                          padding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: GestureDetector(
                            onTap: () async {
                              viewModel
                                  .setSharedPrefs(stateInfos[index]['state']!);
                              // Navigator.of(context).push(
                              //   MaterialPageRoute(
                              //     builder: (context) => HomeView(),
                              //   ),
                              // );

                              // Navigator.of(context).push(
                              //   MaterialPageRoute(
                              //     builder: (context) => MyBottomBarView(),
                              //   ),
                              // );
                              await OnboardingDetailsViewModel()
                                  .getConfigData();
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => SelectPartyView(),
                                ),
                              );
                            },
                            child: Container(
                              width: 300,
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(15),
                                boxShadow: [
                                  BoxShadow(
                                    color: themeData.colorScheme.shadow
                                        .withAlpha(30),
                                    blurRadius: 12,
                                  )
                                ],
                              ),
                              child: Text(
                                "${stateInfos[index]['name']} - ${stateInfos[index]['regionalName']}",
                                style: TextStyle(
                                  fontFamily: 'Work-Sans',
                                  fontSize: 20,
                                  fontWeight: FontWeight.w500,
                                  color: themeData.colorScheme.onSurface,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                );
              }
            },
          ),
        ),
      ),
    );
  }
}
