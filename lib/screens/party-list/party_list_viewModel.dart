import 'dart:convert';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:bjpnew/utils/Singletons/prefs_singleton.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';

class PartyListViewModel extends BaseViewModel {
  final Prefs _prefs = Prefs.instance;

  // Future<List<String>> fetchStateInfos() async {
  //   String? configJson = _prefs.getString('config');
  //   if (configJson != null) {
  //     Map<String, dynamic> config =
  //         jsonDecode(utf8.decode(configJson.codeUnits));
  //     List<dynamic> stateInfos = config['stateInfos'];
  //     List<String> stateInfoList = stateInfos.map((info) {
  //       return "${info['name']} - ${info['regionalName']}";
  //     }).toList();
  //     return stateInfoList;
  //   } else {
  //     return [];
  //   }
  // }
  Future<List<Map<String, String>>> fetchStateInfos() async {
    String? configJson = _prefs.getString('config');
    if (configJson != null) {
      Map<String, dynamic> config =
          jsonDecode(utf8.decode(configJson.codeUnits));
      List<dynamic> stateInfos = config['stateInfos'];
      List<Map<String, String>> stateInfoList = stateInfos.map((info) {
        return {
          'state': info['state'] as String,
          'name': info['name'] as String,
          'regionalName': info['regionalName'] as String
        };
      }).toList();

      //  String jsonString = jsonEncode(selectedStateInfo["leadersImgUrls"]);
      // print(selectedStateInfo["leadersImgUrls"]);
      // await prefs.setString('LeaderPhoto' ,jsonString );

      return stateInfoList;
    } else {
      return [];
    }
  }

  Future<void> setSharedPrefs(String selectedCategory) async {
    _prefs.setString('CategorySelected', selectedCategory);
    _prefs.setStringList('my_string_list', []);
    print(selectedCategory);
  }

//   void config( ) async {
//     final prefs = await SharedPreferences.getInstance();
//  String? configJson = prefs.getString('config');
//     if (configJson != null) {
//       Map<String, dynamic> config =
//           jsonDecode(utf8.decode(configJson.codeUnits));
//       List<dynamic> stateInfos = config['stateInfos'];
//       var stateName = prefs.getString('CategorySelected');

//       final selectedStateInfo = stateInfos.firstWhere(
//         (info) => info["state"] == stateName,
//         orElse: () => null,
//       );
//       print(stateName);
//       String jsonString = jsonEncode(selectedStateInfo["leadersImgUrls"]);
//       print(selectedStateInfo["leadersImgUrls"]);
//       await prefs.setString('LeaderPhoto' ,jsonString );
//       }
//   }
}
