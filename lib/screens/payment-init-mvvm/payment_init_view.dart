import 'dart:async';
import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/screens/payment-init-mvvm/payment_init_viewmodel.dart';
import 'package:stacked/stacked.dart';
import '../../global/CustomSecondaryButton.dart';

@RoutePage()
class PaymentInitView extends StatefulWidget {
  // final int amount;
  final String packageId;
  final String? targetApp;
  // final bool isSubscriptionUser;
  const PaymentInitView({
    super.key,
    // required this.amount,
    required this.packageId,
    required this.targetApp,
    // required this.isSubscriptionUser
  });

  @override
  State<PaymentInitView> createState() => _PaymentInitViewState();
}

class _PaymentInitViewState extends State<PaymentInitView> {
  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<PaymentInitViewModel>.reactive(
        viewModelBuilder: () => PaymentInitViewModel(),
        onViewModelReady: (viewModel) async {
          //initialize variables in viewModel with data provided by premium screen:
          // viewModel.amount = widget.amount;
          // viewModel.duration = widget.duration;
          viewModel.packageId = widget.packageId;
          viewModel.targetApp = widget.targetApp;

          // viewModel.isSubscriptionUser = widget.isSubscriptionUser;
          //initialize payment and long polling:
          await viewModel.paymentInitiation(context);
          Future.delayed(Duration(seconds: 1), () async {
            // await viewModel.paymentStatusPolling(context);
          });
        },
        builder: (context, viewModel, child) => Scaffold(
              backgroundColor: themeData.colorScheme.background,
              body: WillPopScope(
                onWillPop: () async {
                  return await warningPopUp();
                },
                child: Scaffold(
                  body: Container(
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          height: 44,
                          width: 44,
                          child: CircularProgressIndicator(
                            color: Colors.red,
                            strokeWidth: 8,
                            backgroundColor: Colors.red.withAlpha(100),
                            strokeCap: StrokeCap.round,
                          ),
                        ),
                        const SizedBox(
                          height: 24,
                        ),
                        Text(
                          'आपका पेमेंट प्रोसेस हो रहा है',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: Colors.black87,
                              fontSize: 24,
                              fontWeight: FontWeight.w600),
                        ),
                        const SizedBox(
                          height: 4,
                        ),
                        Text(
                          'कृपया एप्प बंद या बैक बटन ना दबायें',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: Colors.black87,
                              fontSize: 20,
                              fontWeight: FontWeight.w300),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  Future warningPopUp() async {
    ThemeData themeData = Theme.of(context);
    bool shouldPop = await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
            backgroundColor: Colors.white,
            content: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Lottie.asset('Asset/Lottie/error-lottie.json',
                        height: 44, fit: BoxFit.fill),
                    SizedBox(
                      height: 32,
                    ),
                    Text(
                      'क्या आप पेमेंट कैंसिल करना चाहते हैं ?',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w800,
                          fontFamily: 'Mukta',
                          height: 1.5),
                    ),
                    SizedBox(
                      height: 12,
                    ),
                    Text(
                      'अगर आपने पेमेंट कर दिया है तो ऐसा करने से एप्प चालू नहीं होगा',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87.withAlpha(150),
                          fontFamily: 'Mukta'),
                    )
                  ],
                ),
              ),
            ),
            actions: <Widget>[
              PrimaryButton(
                isEnabled: true,
                height: 48,
                onTap: () {
                  Navigator.pop(context, false);
                },
                label: 'नहीं, पेमेंट कैंसिल नहीं करें',
                isLoading: false,
                color: themeData.colorScheme.primary,
              ),
              SizedBox(
                height: 8,
              ),
              CustomSecondaryButton(
                showIcon: false,
                leadingIcon: '',
                onPressed: () {
                  Navigator.pop(context, true);
                },
                buttonText: 'हाँ, पेमेंट कैंसिल करें',
                buttonColor: Colors.transparent,
              )
            ],
          );
        });
    return shouldPop;
  }
}
