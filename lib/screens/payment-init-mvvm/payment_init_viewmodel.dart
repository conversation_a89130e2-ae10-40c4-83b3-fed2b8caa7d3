// import 'dart:async';
// import 'package:auto_route/auto_route.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:facebook_app_events/facebook_app_events.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:lottie/lottie.dart';
// import 'package:bjpnew/route/route.gr.dart';
// import 'package:stacked/stacked.dart';
// import 'package:url_launcher/url_launcher.dart';
// import '../../global/primary_button.dart';
// import '../../services/phonePe.dart';
// import '../../utils/Singletons/prefs_singleton.dart';

// class PaymentInitViewModel extends BaseViewModel {

//   //instances
//   final Prefs _prefs = Prefs.instance;
//   late Timer? _paymentStatusTimer;

//   //getters and setters
//   // int _amount = 0;
//   // int get amount => _amount;
//   // set amount(value){
//   //   _amount = value;
//   //   notifyListeners();
//   // }

//   String _paymentStatus = 'PENDING';
//   String get paymentStatus => _paymentStatus;
//   set paymentStatus(value){
//     _paymentStatus = value;
//     notifyListeners();
//   }

//   bool _currentPaymentStatus = false;
//   bool get currentPaymentStatus => _currentPaymentStatus;
//   set currentPaymentStatus(value){
//     _currentPaymentStatus = value;
//     notifyListeners();
//   }

//   Future paymentInitiation() async {
//     // FirebaseAuth firebase = FirebaseAuth.instance;
//     // String? userEmail = firebase.currentUser?.email;
//     // var body = await PhonePe(
//     //     userID: userEmail!,
//     //     amount: _amount,
//     //     duration: _duration,
//     //     targetApp: _targetApp,
//     //     isSubscriptionUser: _isSubscriptionUser).getResponse();
//     // var uri = Uri.parse(body);
//     // if(await canLaunchUrl(uri)){
//     //   await launchUrl(uri);
//     } else {
//       print('there was an error');
//     }
//   }

//   // Future paymentStatusPolling(context) async{
//   //   String checkFirebaseStatus = await _premiumStatusFirebase();
//   //   String? userEmail = FirebaseAuth.instance.currentUser?.email;

//   //   _paymentStatusTimer = Timer.periodic(Duration(seconds: 3), (timer) async{
//   //     //conditions to be checked for firebase status:
//   //     if(checkFirebaseStatus == 'SUCCESS'){
//   //       _prefs.setString('paymentStatus', checkFirebaseStatus);
//   //       _paymentStatus = 'SUCCESS';
//   //       notifyListeners();
//   //       timer.cancel();
//   //       await _handleSuccessPayment(context);
//   //     } else if(paymentStatus == 'FAILED'){
//   //       _prefs.setString('paymentStatus', checkFirebaseStatus);
//   //       _paymentStatus = 'FAILED';
//   //       notifyListeners();
//   //       timer.cancel();
//   //       await _handlePaymentError(context);
//   //     } else if(paymentStatus == 'PENDING'){
//   //       FirebaseFirestore _firebase = FirebaseFirestore.instance;
//   //       var documentSnapshot = await _firebase.collection('User_Database').doc(userEmail).get();
//   //       checkFirebaseStatus = documentSnapshot.get('paymentStatus');
//   //       _prefs.setString('paymentStatus', checkFirebaseStatus);
//   //       _rateLimit++;
//   //       if(_rateLimit > 40){
//   //         timer.cancel();
//   //         _handlePaymentError(context);
//   //       }
//   //       //let the timer running
//   //     }
//   //   });
//   // }

//   Future _handleSuccessPayment(context) async{

//     if(context.mounted){
//       await _successMessage(context);
//       AutoRouter.of(context).replaceAll([HomeViewRoute()]);
//     }
//   }

//   Future _handlePaymentError(context) async {
//     Future.delayed(Duration(seconds: 2));
//     if(context.mounted){
//       await _errorMessage(context);
//       AutoRouter.of(context).pop();
//     }
//   }

//   Future<String> _premiumStatusFirebase() async {
//     FirebaseAuth firebaseAuth = FirebaseAuth.instance;
//     String? userEmail = firebaseAuth.currentUser?.email;
//     FirebaseFirestore _firebase = FirebaseFirestore.instance;
//     DocumentSnapshot documentSnapshot = await _firebase.collection('User_Database').doc(userEmail).get();
//     String? firebasePaymentStatus = documentSnapshot.get('paymentStatus');
//     return firebasePaymentStatus ?? '';
//   }

//   Future<void> _successMessage(BuildContext context) async{
//     ThemeData themeData = Theme.of(context);
//     return showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (BuildContext context){
//           return AlertDialog(
//           shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(15)
//           ),
//           backgroundColor: Colors.white,
//           content: Padding(
//             padding: EdgeInsets.symmetric(horizontal: 16),
//             child: SingleChildScrollView(
//               child: Column(
//                 children: [
//                   SizedBox(
//                     width: 150,
//                     height: 150,
//                     child: Lottie.asset('Asset/Lottie/success-lottie.json'),
//                   ),
//                   Text(
//                     'The payment was successful',
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                       fontWeight: FontWeight.w600,
//                       fontFamily: 'Work-Sans',
//                       fontSize: 20,
//                       color: themeData.colorScheme.onSurface,
//                     ),)
//                 ],
//               ),
//             ),
//           ),
//           actions: <Widget>[
//             PrimaryButton(
//               isEnabled: true,
//               isLoading: false,
//               onTap: (){Navigator.pop(context);},
//               label: 'Okay',
//               color: themeData.colorScheme.primary,
//             )
//           ],
//           );
//         }
//     );
//   }

//   Future<void> _errorMessage(BuildContext context) async{
//     ThemeData themeData = Theme.of(context);
//     return showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (BuildContext context){
//           return AlertDialog(
//           shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(15)
//           ),
//           backgroundColor: Colors.white,
//           content: Padding(
//             padding: EdgeInsets.symmetric(horizontal: 16),
//             child: SingleChildScrollView(
//               child: Column(
//                 children: [
//                   SizedBox(
//                     width: 100,
//                     height: 100,
//                     child: Lottie.asset('Asset/Lottie/error-lottie.json'),
//                   ),
//                   Text('किसी कारणवश आपका पेमेंट पूरा नहीं हो पाया है',
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                         fontSize: 24,
//                         fontWeight: FontWeight.w800,
//                         fontFamily: 'Mukta',
//                         height: 1.5
//                     ),
//                   ),
//                   SizedBox(height: 12,),
//                   Text('अगर आपके पैसे कट गए हैं तो हमसे सेटिंग्स में जाके सपोर्ट पर संपर्क करें',
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                         fontSize: 20,
//                         fontWeight: FontWeight.w500,
//                         color: Colors.black87.withAlpha(150),
//                         fontFamily: 'Mukta'
//                     ),
//                   )
//                 ],
//               ),
//             ),
//           ),
//           actions: <Widget>[
//             PrimaryButton(
//               isEnabled: true,
//               isLoading: false,
//               onTap: (){Navigator.pop(context);},
//               label: 'Okay',
//               color: themeData.colorScheme.primary,
//             )
//           ],
//                     );
//         }
//     );
//   }

//   @override
//   void dispose() {
//     // TODO: implement dispose
//     _paymentStatusTimer?.cancel();
//     super.dispose();
//   }

// }

import 'dart:async';
import 'dart:convert';
import 'package:auto_route/auto_route.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/services/user_db_operations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../global/primary_button.dart';
import '../../services/phonePe.dart';
import '../../utils/Singletons/prefs_singleton.dart';
import 'package:http/http.dart' as http;

class PaymentInitViewModel extends BaseViewModel {
  // Instances
  final Prefs _prefs = Prefs.instance;
  late Timer? _paymentStatusTimer;

  // Getters and setters
  int _amount = 0;
  int get amount => _amount;
  set amount(value) {
    _amount = value;
    notifyListeners();
  }

  String _duration = '';
  String get duration => _duration;
  set duration(value) {
    _duration = value;
    notifyListeners();
  }

  String _packageId = '';
  String get packageId => _packageId;
  set packageId(value) {
    _packageId = value;
    notifyListeners();
  }

  String _targetApp = '';
  String get targetApp => _targetApp;
  set targetApp(value) {
    _targetApp = value;
    notifyListeners();
  }

  int _rateLimit = 0;
  int get rateLimit => _rateLimit;
  set rateLimit(value) {
    _rateLimit = value;
    notifyListeners();
  }

  bool _isSubscriptionUser = false;
  bool get isSubscriptionUser => _isSubscriptionUser;
  set isSubscriptionUser(value) {
    _isSubscriptionUser = value;
    notifyListeners();
  }

  String _paymentStatus = 'PENDING';
  String get paymentStatus => _paymentStatus;
  set paymentStatus(value) {
    _paymentStatus = value;
    notifyListeners();
  }

  bool _currentPaymentStatus = false;
  bool get currentPaymentStatus => _currentPaymentStatus;
  set currentPaymentStatus(value) {
    _currentPaymentStatus = value;
    notifyListeners();
  }

  Future<void> paymentInitiation(BuildContext context) async {
    try {
      print('12345678');
      var body = await PhonePe(
        targetApp: _targetApp,
        packageId: _packageId,
        paymentFlow: 'UPI_INTENT',
      ).getResponse();
      print('0000000');
      var uri = Uri.parse(body);
      print('12345678');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        print(launchUrl);

        // Start polling after launching the payment URL
        paymentStatusPolling(context);
      } else {
        print('There was an error launching the URL');
        await _handlePaymentError(context);
      }
    } catch (e) {
      print('000000098');
      print('Error: $e');
      await _handlePaymentError(context);
    }
  }

  Future<void> paymentStatusPolling(BuildContext context) async {
    _paymentStatusTimer = Timer.periodic(Duration(seconds: 5), (timer) async {
      try {
        bool? premiumStatus = await _checkPremiumStatusFromServer();

        if (premiumStatus == true) {
          notifyListeners();
          timer.cancel();
          await _handleSuccessPayment(context);
        } else if (_rateLimit > 40) {
          // If polling exceeds a certain limit, handle it as an error
          timer.cancel();
          await _handlePaymentError(context);
        }
        _rateLimit++;
      } catch (e) {
        print('Polling error: $e');
        timer.cancel();
        await _handlePaymentError(context);
      }
    });
  }

  Future<bool?> _checkPremiumStatusFromServer() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    bool? _premiumStatus = sharedPreferences.getBool('isPremium');
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');
    print("deviceid for premiuma absbd");

    try {
      final response = await http.get(
        Uri.parse(
            'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions'),
        headers: {
          "Content-Type": "application/json",
          'TOKEN': token ?? "",
          'app-user-id': userId ?? "",
          "DEVICE_ID": deviceId ?? "",
          "CLIENT_VERSION": "39",
          "CLIENT_TYPE": "ANDROID",
          "CLIENT_VERSION_CODE": "94"
        },
      );

      if (response.statusCode == 200) {
        print("isnisde print");
        final jsonResponse = jsonDecode(response.body);
        final premiumUser = jsonResponse['premiumUser'] ?? false;
        final premiumTill = jsonResponse['premiumTill'] ?? 0;
        print(",premium$premiumUser");

        // Check if the user is a premium user based on the response
        // and update the local storage accordingly
        if (premiumUser) {
          sharedPreferences.setBool('isPremium', true);
          sharedPreferences.setInt('premiumTill', premiumTill);
        } else {
          sharedPreferences.setBool('isPremium', false);
          sharedPreferences.setInt('premiumTill', 0);
        }
        return premiumUser;
      } else {
        throw Exception('Failed to load premium status');
      }
    } catch (e) {
      print('Error checking premium status: $e');
      return _premiumStatus;
    }
  }

  Future<void> _handleSuccessPayment(BuildContext context) async {
    // DateTime premiumDate = DateTime.now();
    // // Only needed if user is a subscribed user
    // DateTime dueDate =
    //     DateTime.now().add(Duration(days: _duration == "monthly" ? 31 : 365));
    // // Setting the shared preferences to serve future needs
    // _prefs.setBool('isPremium', true);
    // _prefs.setString('date', premiumDate.toString());
    // _prefs.setString('duration', _duration);
    // _prefs.setInt('amount', _amount);
    // // Required shared prefs if user is a subscribedUser
    // _isSubscriptionUser
    //     ? _prefs.setBool('isSubscribedUser', true)
    //     : _prefs.setBool('isSubscribedUser', false);
    // _isSubscriptionUser ? _prefs.setString('dueDate', dueDate.toString()) : {};
    // Setting event on Facebook and sending user to main quotes screen:
    await FacebookAppEvents()
        .logPurchase(amount: amount.toDouble(), currency: 'INR');
    if (context.mounted) {
      await _successMessage(context);
      // AutoRouter.of(context).replaceAll([HomeViewRoute()]);
    }
  }

  Future<void> _handlePaymentError(BuildContext context) async {
    Future.delayed(Duration(seconds: 2));
    if (context.mounted) {
      await _errorMessage(context);
      AutoRouter.of(context).pop();
    }
  }

  Future<void> _successMessage(BuildContext context) async {
    ThemeData themeData = Theme.of(context);
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          backgroundColor: Colors.white,
          content: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(
                    width: 150,
                    height: 150,
                    child: Lottie.asset('Asset/Lottie/success-lottie.json'),
                  ),
                  Text(
                    'The payment was successful',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Work-Sans',
                      fontSize: 20,
                      color: themeData.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: <Widget>[
            PrimaryButton(
              isEnabled: true,
              isLoading: false,
              onTap: () {
                Navigator.pop(context);
              },
              label: 'Okay',
              color: themeData.colorScheme.primary,
            ),
          ],
        );
      },
    );
  }

  Future<void> _errorMessage(BuildContext context) async {
    ThemeData themeData = Theme.of(context);
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          backgroundColor: Colors.white,
          content: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(
                    width: 100,
                    height: 100,
                    child: Lottie.asset('Asset/Lottie/error-lottie.json'),
                  ),
                  Text(
                    'किसी कारणवश आपका पेमेंट पूरा नहीं हो पाया है',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w800,
                      fontFamily: 'Mukta',
                      height: 1.5,
                    ),
                  ),
                  SizedBox(
                    height: 12,
                  ),
                  Text(
                    'अगर आपके पैसे कट गए हैं तो हमसे सेटिंग्स में जाके सपोर्ट पर संपर्क करें',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87.withAlpha(150),
                      fontFamily: 'Mukta',
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: <Widget>[
            PrimaryButton(
              isEnabled: true,
              isLoading: false,
              onTap: () {
                Navigator.pop(context);
              },
              label: 'Okay',
              color: themeData.colorScheme.primary,
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _paymentStatusTimer?.cancel();
    super.dispose();
  }
}
