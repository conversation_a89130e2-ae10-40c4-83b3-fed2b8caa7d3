import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombar.dart';

import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class RazorpayPayment {
  final BuildContext context;

  String subscriptionId;
  String amount;
  final String apiKey;
  RazorpayPayment({
    required this.subscriptionId,
    required this.amount,
    required this.context,
    required this.apiKey,
  });

  late Razorpay _razorpay;

  void initRazorpay() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  void dispose() {
    _razorpay.clear();
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    processingloader(context);
    log("Payment Success : [Payment id] " + response.paymentId.toString());
    log("Payment Success : [Signature] " + response.signature.toString());
    await makePayment(
        response.paymentId.toString(), response.signature.toString());

    await FacebookAppEvents()
        .logPurchase(amount: double.parse(amount), currency: 'INR');

    Navigator.of(context).pop();

    if (context.mounted) {
      await successMessage(context);
      // AutoRouter.of(context).replaceAll([HomeViewRoute()]);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MyBottomBarView(),
        ),
      );
    }

    dispose();
  }

  void _handlePaymentError(PaymentFailureResponse response) async {
    Map<dynamic, dynamic>? v = response.error;

    print("object  $response ");

    // errorMessage(context);
    // Handle payment failure
    dispose();
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Handle external wallet

    print(' ecternal error  $response');
    dispose();
  }

  void openCheckout() async {
    final prefs = await SharedPreferences.getInstance();
    final String? email = prefs.getString('email');
    final String? number = prefs.getString('number');
    var options = {
      'key': apiKey,
      'name': 'Poster for Bharat',
      'description': 'Premium',
      'subscription_id': subscriptionId,
      'recurring': true,
      'prefill': {'contact': number, 'email': email},
      'external': {
        'wallets': ['paytm', 'phonepe', 'upi'],
      }
    };
    try {
      print("subsid,$subscriptionId");
      print("object ,$options");
      _razorpay.open(options);
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  processingloader(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible:
          false, // Prevents dismissing the dialog by tapping outside
      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text("Payment Processing..."),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> successMessage(BuildContext context) async {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => false,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15)),
              backgroundColor: Colors.white,
              content: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // SizedBox(
                      //   width: 150,
                      //   height: 150,
                      //   // child: Lottie.asset('assets/animations/success.json',
                      //       repeat: false, fit: BoxFit.fill),
                      // ),
                      const Text('The payment was successful',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w800,
                              fontFamily: 'Montserrat',
                              height: 1.5,
                              color: Colors.black87))
                    ],
                  ),
                ),
              ),
              actions: <Widget>[
                ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text("Okay"))
              ],
            ),
          );
        });
  }

  Future<void> errorMessage(BuildContext context) async {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => false,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15)),
              backgroundColor: Colors.white,
              content: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const Text(
                        'Error processing your payment',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w800,
                            fontFamily: 'Montserrat',
                            color: Colors.black87),
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      Text(
                        'Contact support if your amount has been debited',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87.withAlpha(150),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              actions: <Widget>[
                ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text("Okay"))
              ],
            ),
          );
        });
  }

  Future<void> makePayment(String rpPaymentId, String signature) async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = await prefs.getString('token');
    final String? userId = await prefs.getString('userId');
    final String? rporderId = await prefs.getString('rpOrderId');

    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/subscription/v1/$rporderId/payment');
    print(url);
    final headers = {
      'app-user-id': userId ?? "",
      'Content-Type': 'application/json',
      'TOKEN': token ?? "",
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final body = jsonEncode({
      'rpPaymentId': rpPaymentId,
      'recurring': true,
      'success': true,
      'signature': signature,
    });
    print(headers);
    print(body);
    try {
      final response = await http.post(url, headers: headers, body: body);
      if (response.statusCode == 200) {
        print('Payment successful');
        // Handle the response as needed
      } else {
        print('Failed to make payment: ${response.statusCode}');
        // Handle the error as needed
      }
    } catch (e) {
      print('Error making payment: $e');
      // Handle the exception as needed
    }
  }
}
