// import 'package:stacked/stacked.dart';

// import '../../utils/Singletons/prefs_singleton.dart';

// class PremiumViewModel extends BaseViewModel{

//   //instances
//   final Prefs _prefs = Prefs.instance;

//   //getters and setters
//   Prefs get prefs => _prefs;
//   bool get premiumStatus => _prefs.getBool('isPremium') ?? false;
//   int get monthlyAmount => 99;
//   int get annualAmount => 499;

//   bool _isAnnualSelected = true;
//   bool get isAnnualSelected => _isAnnualSelected;
//   set isAnnualSelected(value){
//     _isAnnualSelected = value;
//     notifyListeners();
//   }

// }
import 'package:stacked/stacked.dart';

import '../../utils/Singletons/prefs_singleton.dart';

class PremiumViewModel extends BaseViewModel {
  // Instances
  final Prefs _prefs = Prefs.instance;

  // Getters and Setters
  Prefs get prefs => _prefs;
  bool get premiumStatus => _prefs.getBool('isPremium') ?? false;

  int get monthlyAmount => calculateTotalAmount(99);
  int get annualAmount => calculateTotalAmount(499);

  bool _isAnnualSelected = true;
  bool get isAnnualSelected => _isAnnualSelected;
  set isAnnualSelected(value) {
    _isAnnualSelected = value;
    notifyListeners();
  }

  // Method to calculate the total amount including GST
  int calculateTotalAmount(int planAmount) {
    // Assuming GST percentage is fixed at 18%
    return (planAmount + (planAmount * 18 / 100)).round();
  }
}
