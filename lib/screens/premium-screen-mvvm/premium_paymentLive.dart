import 'dart:convert';
import 'dart:ffi';

import 'package:auto_route/auto_route.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/services/phonePe.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
// Import your PhonePe service

class RazorPayService {
  String subscriptionId;
  String amount;
  // String targetApp;
  RazorPayService({
    required this.subscriptionId,
    required this.amount,
    // required this.targetApp
  });

  late Razorpay _razorpay;
  void initiateService() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  void dispose() {
    _razorpay.clear(); // Clear listeners
    // _razorpay.close(); // Close Razorpay instance
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    // Handle payment success
    print('Payment successful: ${response.paymentId}');
    print(response);
    // await FacebookAppEvents()
    // .logPurchase(amount: double.parse(amount), currency: 'INR');

    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      "DEVICE_ID": deviceId ?? "",
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final body = {
      "rpPaymentId": response.paymentId,
      "recurring": true,
      "success": true,
      "signature": response.signature
    };

    // Make API call with the payment ID and headers
    final String apiUrl =
        'https://backend.designboxconsuting.com/subscription/v1/${subscriptionId}/payment';

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: headers,
        body: jsonEncode(body),
      );
      if (response.statusCode == 200) {
        print('Payment API call successful');
      } else {
        print('Failed to make Payment API call: ${response.statusCode}');
      }
    } catch (e) {
      print('Error making Payment API call: $e');
    }
    //  if (context.mounted) {
    // await successMessage(context);
    // AutoRouter.of(context).replaceAll([HomeViewRoute()]);
    // }
    // Navigate to success screen or perform necessary actions
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    // Handle payment failure
    print('Payment failed: ${response.code} - ${response.message}');
    // Navigate to error screen or perform necessary actions
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Handle external wallet
    print('External wallet selected: ${response.walletName}');
    // Perform necessary actions
  }

  void startPayment() async {
    // Pass the subscription ID to Razorpay for payment
    print("subscriptioid");
    // print("amount: $amount");

    // print(subscriptionId);
    final prefs = await SharedPreferences.getInstance();
    final String? email = prefs.getString('email');
    // final String? number = prefs.getString('number');

    // final String number = prefs.getString('number') ?? '';
    // print(number);
    var options = {
      // 'key': "rzp_live_2XvG45whKJ5t2s",
      'key': 'rzp_live_JgHvAxfkjZixmG',
      // 'order_id': subscriptionId,
      'subscription_id': subscriptionId,

      // 'order_id': "",
      'name': 'Poster for Bharat',
      'description': ' Poster for Bharat premium',
      'autocapture': true,
      'prefill': {
        'email': email,
        // "number": number
      },
      'method': {
        'external': {
          'wallets': ['paytm', 'phonepe', 'upi'],
        },
        'upi': {
          'flow': 'collect',
          'apps': [
            'com.google.android.apps.nbu.paisa.user',
            'net.one97.paytm',
            'com.phonepe.app'
          ] // Add UPI app package names here
        }
      }
      // 'external': {
      //   'wallets': ['paytm', 'phonepe', 'upi'],
      // },
    };

    try {
      print("reached open razor pay");
      print("subs, $subscriptionId");
      print("Opening Razorpay with options: $options");
      _razorpay.open(options);
    } catch (e) {
      debugPrint('there is some error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Razorpay Payment'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => startPayment(),
          child: Text('Initiate Payment'),
        ),
      ),
    );
  }
}

// class RazorpayPaymentScreen extends StatefulWidget {
//   final String packageId;

//   RazorpayPaymentScreen({required this.packageId});

//   @override
//   _RazorpayPaymentScreenState createState() => _RazorpayPaymentScreenState();
// }

// class _RazorpayPaymentScreenState extends State<RazorpayPaymentScreen> {
//   late Razorpay _razorpay;

//   @override
//   void initState() {
//     super.initState();
//     _razorpay = Razorpay();
//     _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
//     _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
//     _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
//   }

//   @override
//   void dispose() {
//     _razorpay.clear(); // Clear listeners
//     // _razorpay.close(); // Close Razorpay instance
//     super.dispose();
//   }

// void _handlePaymentSuccess(PaymentSuccessResponse response) {
//   // Handle payment success
//   print('Payment successful: ${response.paymentId}');
//   // Navigate to success screen or perform necessary actions
// }

// void _handlePaymentError(PaymentFailureResponse response) {
//   // Handle payment failure
//   print('Payment failed: ${response.code} - ${response.message}');
//   // Navigate to error screen or perform necessary actions
// }

// void _handleExternalWallet(ExternalWalletResponse response) {
//   // Handle external wallet
//   print('External wallet selected: ${response.walletName}');
//   // Perform necessary actions
// }

// void _startPayment(String subscriptionId) async {
//   try {
//     final phonePe = PhonePe(packageId: widget.packageId);
//     print("object");
//     print(phonePe);
//     final response = await phonePe.getResponse();
//     final subscriptionId = jsonDecode(response)['message'];
//     // Pass the subscription ID to Razorpay for payment
//     _razorpay.open({
//       'key': 'rzp_live_iRa27vt623Fx0c',
//       'amount':
//           100, // Amount in the smallest currency unit (e.g., cents for USD)
//       'currency': 'INR',
//       'name': 'Your Company Name',
//       'description': 'Subscription Payment',
//       'order_id': subscriptionId, // Get the order ID from your backend
//       'prefill': {
//         'contact': '9403012499',
//         'email': '<EMAIL>',
//       },
//       'external': {
//         'wallets': ['paytm'],
//       }
//     });
//   } catch (e) {
//     // Handle exception
//     print('Error initiating payment: $e');
//   }
// }
// void startPayment() async {
//   final phonePe = PhonePe(
//     packageId: widget.packageId,
//     paymentFlow: "UPI_INTENT",
//     targetApp: "com.phonepe.app",
//   );
//   print("object");
//   print(phonePe);
//   final response = await phonePe.getResponse();
//   final subscriptionId = jsonDecode(response)['data']['phonepe'];
//   print(subscriptionId);
//   // Pass the subscription ID to Razorpay for payment

//   var options = {
//     'key': 'rzp_live_JgHvAxfkjZixmG',
//     // 'key': "rzp_live_iRa27vt623Fx0c",
//     'subscription_id': subscriptionId,
//     // 'order_id': "",
//     'name': ' Posters for B',
//     'description': ' Posters for B premium',
//     'prefill': {'email': " email"}
//   };

//   try {
//     _razorpay.open(options);
//   } catch (e) {
//     debugPrint('there is some error: ${e.toString()}');
//   }
// }

// @override
// Widget build(BuildContext context) {
//   // return Scaffold(
//   //   appBar: AppBar(
//   //     title: Text('Razorpay Payment'),
//   //   ),
//   //   body: Center(
//   //     child: ElevatedButton(
//   //       onPressed: () => startPayment(),
//   //       child: Text('Initiate Payment'),
//   //     ),
//   //   ),
//   // );
// }
// }
