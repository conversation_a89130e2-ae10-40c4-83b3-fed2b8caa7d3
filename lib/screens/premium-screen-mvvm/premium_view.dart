// // import 'package:auto_route/auto_route.dart';
// // import 'package:flutter/material.dart';
// // import 'package:flutter_svg/svg.dart';
// // import 'package:bjpnew/route/route.gr.dart';
// // import 'package:bjpnew/screens/template/template_right_1.dart';
// // import 'package:stacked/stacked.dart';
// // import '../../global/PurchasePlans.dart';
// // import 'package:bjpnew/services/user_image_operations.dart';
// // import 'premium_viewModel.dart';

// // @RoutePage()
// // class PremiumView extends StatefulWidget {
// //   final String imageUrl;
// //   final bool isTestUser;
// //   const PremiumView({
// //     super.key,
// //     required this.imageUrl,
// //     this.isTestUser = false,
// //   });

// //   @override
// //   State<PremiumView> createState() => _PremiumViewState();
// // }

// // class _PremiumViewState extends State<PremiumView> {
// //   @override
// //   Widget build(BuildContext context) {
// //     ThemeData themeData = Theme.of(context);
// //     return ViewModelBuilder<PremiumViewModel>.reactive(
// //         viewModelBuilder: () => PremiumViewModel(),
// //         builder: (context, viewModel, child) => SafeArea(
// //                 child: Scaffold(
// //               backgroundColor: themeData.colorScheme.background,
// //               body: ListView(
// //                 children: [
// //                   shareImage(viewModel),
// //                   amountCards(viewModel),
// //                   SizedBox(height: 24),
// //                   const SizedBox(height: 24)
// //                 ],
// //               ),
// //               bottomNavigationBar: InkWell(
// //                   onTap: () {
// //                     AutoRouter.of(context).push(UPIViewRoute(
// //                       amount: viewModel.isAnnualSelected
// //                           ? viewModel.annualAmount
// //                           : viewModel.monthlyAmount,
// //                       duration:
// //                           viewModel.isAnnualSelected ? 'annual' : 'monthly',
// //                     ));
// //                   },
// //                   child: bottomNavigationBar(viewModel)),
// //             )));
// //   }

// //   Widget shareImage(PremiumViewModel viewModel) {
// //     return FutureBuilder(
// //       future: userImage(viewModel),
// //       builder: (BuildContext context, AsyncSnapshot snapshot) {
// //         if (snapshot.data != null) {
// //           return Column(
// //             crossAxisAlignment: CrossAxisAlignment.center,
// //             children: [
// //               Template_right_1(
// //                   imageUrl: widget.imageUrl,
// //                   showCTA: false,
// //                   premiumStatus: viewModel.premiumStatus)
// //             ],
// //           );
// //         } else {
// //           return CircularProgressIndicator();
// //         }
// //       },
// //     );
// //   }

// //   Widget amountCards(PremiumViewModel viewModel) {
// //     return Column(
// //       children: [
// //         const SizedBox(height: 24),
// //         Text(
// //           'अनलिमिटेड पोस्टर अपनी फोटो के साथ शेयर करें',
// //           style: TextStyle(
// //               fontWeight: FontWeight.w700,
// //               fontSize: 20,
// //               fontFamily: 'Mukta',
// //               color: Colors.black87.withAlpha(200)),
// //           textAlign: TextAlign.center,
// //         ),
// //         const SizedBox(height: 40),
// //         Padding(
// //             padding: EdgeInsets.symmetric(horizontal: 16),
// //             child: GestureDetector(
// //               onTap: () {
// //                 viewModel.isAnnualSelected = true;
// //               },
// //               child: PurchasePlans(
// //                 planDuration: '12 months',
// //                 planAmount: 499,
// //                 isSelected: viewModel.isAnnualSelected ? true : false,
// //                 showDiscount: true,
// //               ),
// //             )),
// //         const SizedBox(height: 12),
// //         Padding(
// //             padding: const EdgeInsets.symmetric(horizontal: 16),
// //             child: GestureDetector(
// //               onTap: () async {
// //                 viewModel.isAnnualSelected = false;
// //               },
// //               child: PurchasePlans(
// //                 planAmount: 99,
// //                 isSelected: viewModel.isAnnualSelected ? false : true,
// //                 planDuration: '/month',
// //                 showDiscount: false,
// //               ),
// //             )),
// //       ],
// //     );
// //   }

// //   Widget bottomNavigationBar(PremiumViewModel viewModel) {
// //     return Container(
// //       color: Colors.red,
// //       child: Container(
// //         padding: EdgeInsets.all(12),
// //         child: Row(
// //           children: [
// //             Column(
// //               mainAxisSize: MainAxisSize.min,
// //               children: [
// //                 Text(
// //                   '₹ ${viewModel.isAnnualSelected ? viewModel.annualAmount.toString() : viewModel.monthlyAmount.toString()}',
// //                   style: TextStyle(
// //                       fontSize: 24,
// //                       fontFamily: 'Work-Sans',
// //                       fontWeight: FontWeight.bold,
// //                       color: Colors.white),
// //                 ),
// //                 viewModel.isAnnualSelected
// //                     ? Text(
// //                         "/year",
// //                         style: TextStyle(
// //                             fontSize: 16,
// //                             fontFamily: 'Work-Sans',
// //                             fontWeight: FontWeight.normal,
// //                             color: Colors.white),
// //                       )
// //                     : Text(
// //                         "/month",
// //                         style: TextStyle(
// //                             fontSize: 16,
// //                             fontFamily: 'Work-Sans',
// //                             fontWeight: FontWeight.normal,
// //                             color: Colors.white),
// //                       ),
// //               ],
// //             ),
// //             SizedBox(
// //               width: 12,
// //             ),
// //             CircleAvatar(
// //               backgroundColor: Colors.white,
// //               radius: 2,
// //             ),
// //             SizedBox(
// //               width: 12,
// //             ),
// //             Text(
// //               'PURCHASE',
// //               style: TextStyle(
// //                 fontSize: 20,
// //                 fontFamily: 'Montserrat',
// //                 fontWeight: FontWeight.bold,
// //                 color: Colors.white,
// //               ),
// //             ),
// //             Spacer(),
// //             Icon(
// //               Icons.arrow_forward_rounded,
// //               size: 36,
// //               color: Colors.white,
// //             )
// //           ],
// //         ),
// //       ),
// //     );
// //   }

// //   Future<Widget> userImage(PremiumViewModel viewModel) async {
// //     int? selectedID = viewModel.prefs.getInt('SelectedID');

// //     if (selectedID != null) {
// //       var userImage = await UserImage().returnSelectedUserImage(selectedID);
// //       return Image.file(userImage,
// //           fit: BoxFit.contain,
// //           width: 260,
// //           height: 260,
// //           alignment: Alignment.bottomCenter);
// //     } else {
// //       return Container(
// //           child: SvgPicture.asset('Asset/SVG/ImagePlaceholder.svg'));
// //     }
// //   }
// // }

// import 'dart:convert';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:auto_route/auto_route.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:bjpnew/route/route.gr.dart';
// import 'package:bjpnew/screens/premium-screen-mvvm/payment.dart';
// import 'package:bjpnew/screens/template/template_right_1.dart';
// import 'package:stacked/stacked.dart';
// import 'package:url_launcher/url_launcher.dart';
// import '../../global/PurchasePlans.dart';
// import 'package:bjpnew/services/user_image_operations.dart';
// import 'premium_viewModel.dart';
// import 'package:http/http.dart' as http;
// // import 'package:intl/intl.dart';

// @RoutePage()
// class PremiumView extends StatefulWidget {
//   final String imageUrl;
//   final bool isTestUser;
//   const PremiumView({
//     super.key,
//     required this.imageUrl,
//     this.isTestUser = false,
//   });

//   @override
//   State<PremiumView> createState() => _PremiumViewState();
// }

// class _PremiumViewState extends State<PremiumView> {
//   @override
//   Widget build(BuildContext context) {
//     ThemeData themeData = Theme.of(context);
//     return ViewModelBuilder<PremiumViewModel>.reactive(
//         viewModelBuilder: () => PremiumViewModel(),
//         onModelReady: (viewModel) async =>
//             await viewModel.fetchSubscriptionDetails(),
//         builder: (context, viewModel, child) => SafeArea(
//                 child: Scaffold(
//               backgroundColor: themeData.colorScheme.background,
//               body: ListView(
//                 children: [
//                   shareImage(viewModel),
//                   amountCards(viewModel),
//                   // SizedBox(height: 24),
//                   // const SizedBox(height: 24)
//                 ],
//               ),
//               bottomNavigationBar: InkWell(
//                   onTap: () async {
//                     await viewModel.initiatePayment();
//                   },
//                   child: bottomNavigationBar(viewModel)),
//             )));
//   }

//   Widget shareImage(PremiumViewModel viewModel) {
//     return FutureBuilder(
//       future: userImage(viewModel),
//       builder: (BuildContext context, AsyncSnapshot snapshot) {
//         if (snapshot.data != null) {
//           return Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               Padding(
//                 padding: const EdgeInsets.all(18.0),
//                 child: Template_right_1(
//                     imageUrl: widget.imageUrl,
//                     showCTA: false,
//                     premiumStatus: false),
//               )
//             ],
//           );
//         } else {
//           return CircularProgressIndicator();
//         }
//       },
//     );
//   }

//   Widget amountCards(PremiumViewModel viewModel) {
//     return Column(
//       children: [
//         const SizedBox(height: 0),
//         Text(
//           'Share Unlimited Posters with Your Photo',
//           style: TextStyle(
//             fontWeight: FontWeight.w700,
//             fontSize: 20,
//             fontFamily: 'Mukta',
//             color: Colors.black87.withAlpha(200),
//           ),
//           textAlign: TextAlign.center,
//         ),
//         const SizedBox(height: 14),
//         Padding(
//           padding: EdgeInsets.symmetric(horizontal: 16),
//           child: GestureDetector(
//             onTap: () {
//               viewModel.isAnnualSelected = true;
//             },
//             child: PurchasePlans(
//               planDuration: '12 months',
//               planAmount: 499,
//               gstPercentage: 18,
//               isSelected: viewModel.isAnnualSelected ? true : false,
//               showDiscount: true,
//               planId: viewModel.yearlyPlanId,
//             ),
//           ),
//         ),
//         const SizedBox(height: 5),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16),
//           child: GestureDetector(
//             onTap: () async {
//               viewModel.isAnnualSelected = false;
//             },
//             child: PurchasePlans(
//               planAmount: 99,
//               gstPercentage: 18,
//               isSelected: viewModel.isAnnualSelected ? false : true,
//               planDuration: '/month',
//               showDiscount: false,
//               planId: viewModel.monthPlanId,
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget bottomNavigationBar(PremiumViewModel viewModel) {
//     return Container(
//       color: Colors.red,
//       child: Container(
//         padding: EdgeInsets.all(12),
//         child: Row(
//           children: [
//             Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Text(
//                   '₹ ${viewModel.isAnnualSelected ? viewModel.priceAfterTaxAnnual.toString() : viewModel.priceAfterTax.toString()}',
//                   style: TextStyle(
//                       fontSize: 24,
//                       fontFamily: 'Work-Sans',
//                       fontWeight: FontWeight.bold,
//                       color: Colors.white),
//                 ),
//                 viewModel.isAnnualSelected
//                     ? Text(
//                         "/year",
//                         style: TextStyle(
//                             fontSize: 16,
//                             fontFamily: 'Work-Sans',
//                             fontWeight: FontWeight.normal,
//                             color: Colors.white),
//                       )
//                     : Text(
//                         "/month",
//                         style: TextStyle(
//                             fontSize: 16,
//                             fontFamily: 'Work-Sans',
//                             fontWeight: FontWeight.normal,
//                             color: Colors.white),
//                       ),
//               ],
//             ),
//             SizedBox(
//               width: 12,
//             ),
//             CircleAvatar(
//               backgroundColor: Colors.white,
//               radius: 2,
//             ),
//             SizedBox(
//               width: 12,
//             ),
//             Text(
//               'PURCHASE',
//               style: TextStyle(
//                 fontSize: 20,
//                 fontFamily: 'Montserrat',
//                 fontWeight: FontWeight.bold,
//                 color: Colors.white,
//               ),
//             ),
//             Spacer(),
//             Icon(
//               Icons.arrow_forward_rounded,
//               size: 36,
//               color: Colors.white,
//             )
//           ],
//         ),
//       ),
//     );
//   }

//   Future<Widget> userImage(PremiumViewModel viewModel) async {
//     int? selectedID = viewModel.prefs.getInt('SelectedID');

//     if (selectedID != null) {
//       var userImage = await UserImage().returnSelectedUserImage(selectedID);
//       return Image.file(userImage,
//           fit: BoxFit.contain,
//           width: 260,
//           height: 260,
//           alignment: Alignment.bottomCenter);
//     } else {
//       return Container(
//           child: SvgPicture.asset('Asset/SVG/ImagePlaceholder.svg'));
//     }
//   }
// }
