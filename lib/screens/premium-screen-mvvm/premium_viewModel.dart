// import 'package:stacked/stacked.dart';

// import '../../utils/Singletons/prefs_singleton.dart';

// class PremiumViewModel extends BaseViewModel {
//   //instances
//   final Prefs _prefs = Prefs.instance;

//   //getters and setters
//   Prefs get prefs => _prefs;
//   bool get premiumStatus => _prefs.getBool('isPremium') ?? false;
//   int get monthlyAmount => 99;
//   int get annualAmount => 499;
//   String get monthplanid => "plan_NOpNzp9tSe77zk";
//   String get yearlyplanid => "plan_NOpOTDi9ff87uA";

//   bool _isAnnualSelected = true;
//   bool get isAnnualSelected => _isAnnualSelected;
//   set isAnnualSelected(value) {
//     _isAnnualSelected = value;
//     notifyListeners();
//   }
// }

import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:bjpnew/screens/premium-screen-mvvm/premium_paymentLive.dart';
import 'package:bjpnew/services/phonePe.dart';
import 'package:bjpnew/utils/Singletons/prefs_singleton.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';

class PremiumViewModel extends BaseViewModel {
  bool _isAnnualSelected = true;
  bool get isAnnualSelected => _isAnnualSelected;
  late String selectedPackageId;

  void selectPackage(String packageId) {
    selectedPackageId = packageId;
  }

  final Prefs _prefs = Prefs.instance;

  //getters and setters
  Prefs get prefs => _prefs;
  bool get premiumStatus => _prefs.getBool('isPremium') ?? false;
  set isAnnualSelected(bool value) {
    _isAnnualSelected = value;
    notifyListeners();
  }

  int _monthlyAmount = 0;
  int get monthlyAmount => _monthlyAmount;
  int _priceAfterTax = 0;
  int get priceAfterTax => _priceAfterTax;
  int _priceAfterTaxAnnual = 0;
  int get priceAfterTaxAnnual => _priceAfterTaxAnnual;

  int _annualAmount = 0;
  int get annualAmount => _annualAmount;

  String _monthPlanId = '';
  String get monthPlanId => _monthPlanId;

  String _yearlyPlanId = '';
  String get yearlyPlanId => _yearlyPlanId;

  bool _premiumUser = false;
  bool get premiumUser => _premiumUser;

  String _apiKey = '';
  String get apiKey => _apiKey;

  int _premiumTill = 0;
  int get premiumTill => _premiumTill;

  // Future<void> initiatePayment() async {
  //   try {
  //     final phonePe =
  //         PhonePe(packageId: _isAnnualSelected ? _yearlyPlanId : _monthPlanId);
  //     print(_isAnnualSelected ? _yearlyPlanId : _monthPlanId);
  //     final response = await phonePe.getResponse();
  //     final subscriptionId = jsonDecode(response)['message'];
  //     print(" phonepe $response");
  //     print(subscriptionId);

  //     RazorPayService razorpayservice = RazorPayService(
  //         subscriptionId: subscriptionId,
  //         amount: isAnnualSelected
  //             ? priceAfterTaxAnnual.toString()
  //             : priceAfterTax.toString());

  //     razorpayservice.initiateService();
  //     razorpayservice.startPayment();

  //     // Process the response as needed, e.g., redirect to payment page
  //   } catch (e) {
  //     // Handle any errors that occur during payment initiation
  //     print('Error initiating payment: $e');
  //   }
  // }

  Future<void> fetchSubscriptionDetails() async {
    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions');
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');
    print("deviceif for premiumview");
    print(deviceId);
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? "",
      'app-user-id': userId ?? "",
      "DEVICE_ID": deviceId ?? '',
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    try {
      final response = await http.get(url, headers: headers);
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final List<dynamic> subscriptionList = jsonData['subscriptionList'];

        // Assuming subscriptionList is not empty
        final monthlySubscription = subscriptionList[0];
        final annualSubscription = subscriptionList[1];

        _monthlyAmount = monthlySubscription['price'];
        _annualAmount = annualSubscription['price'];
        _priceAfterTax = monthlySubscription['priceAfterTax'];
        _priceAfterTaxAnnual = annualSubscription['priceAfterTax'];

        _monthPlanId = monthlySubscription['packageId'];
        print(",month$_monthPlanId");
        _yearlyPlanId = annualSubscription['packageId'];
        print(_yearlyPlanId);
        _premiumUser = jsonData['premiumUser'];
        _apiKey = jsonData['rzpApiKey'];
        _premiumTill = jsonData['premiumTill'];

        notifyListeners();
      } else {
        print('Failed to fetch subscription details: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching subscription details: $e');
    }
  }
}
