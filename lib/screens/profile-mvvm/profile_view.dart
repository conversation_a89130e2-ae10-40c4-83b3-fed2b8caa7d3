import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:bjpnew/global/bjp_card.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/bottombar.dart/bottombar.dart';

import 'package:bjpnew/screens/home-mvvm/home_view.dart';
import 'package:bjpnew/screens/profile-mvvm/profile_viewmodel.dart';
import 'package:bjpnew/services/download_share_image.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import '../../global/CustomSecondaryButton.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:screenshot/screenshot.dart';

@RoutePage()
class ProfileView extends StatefulWidget {
  final VoidCallback onProfileDetailsChange;
  const ProfileView({
    super.key,
    required this.onProfileDetailsChange,
  });

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

var appuserId;
void getappid() async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  appuserId = await prefs.getString('appuserid') ?? '';
}

class _ProfileViewState extends State<ProfileView> {
  @override
  Widget build(BuildContext context) {
    getappid();
    ThemeData themeData = Theme.of(context);
    final ScreenshotController _controller = ScreenshotController();
    return ViewModelBuilder<ProfileViewModel>.reactive(
      viewModelBuilder: () => ProfileViewModel(),
      builder: (context, viewModel, child) => SafeArea(
        child: Scaffold(
          backgroundColor: themeData.colorScheme.background,
          body: SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 24),
                Stack(
                  children: [
                    header(viewModel),
                  ],
                ),
                const SizedBox(height: 24),
                FutureBuilder<Map<String, dynamic>>(
                  future: viewModel.fetchBJPLabelData(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return CircularProgressIndicator();
                    } else if (snapshot.hasError) {
                      return Text('Error: ${snapshot.error}');
                    } else if (snapshot.hasData) {
                      final userData = snapshot.data!;

                      return Stack(
                        alignment: Alignment.bottomLeft,
                        children: [
                          Screenshot(
                            controller: _controller,
                            child: BJPCard(
                              name: userData['name']!,
                              position: userData['position']!,
                              state: userData['state']!,
                              userImage: userData['image'],
                              appuserid: appuserId,
                            ),
                          ),
                          // ElevatedButton(
                          //     onPressed: () async {
                          //       await DownloadShareImage(
                          //               controller: _controller)
                          //           .shareScreenshot();
                          //     },
                          //     child: Text(
                          //       "Share ID Card",
                          //     )),
                        ],
                      );
                      //     }
                      //   },
                      // );
                    } else {
                      return Text('No data available');
                    }
                  },
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: PrimaryButton(
                    // iconPath: 'Asset/Icons/User-Photo.svg',
                    isEnabled: true,
                    height: 48,
                    isLoading: false,
                    onTap: () async {
                      await DownloadShareImage(controller: _controller)
                          .shareIDCard();
                    },
                    label: 'Share ID Card',
                    color: themeData.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                // callToActions(viewModel),
                // const SizedBox(height: 24),
                profileImageGrid(viewModel),
                const SizedBox(height: 24),
                const SizedBox(height: 44, width: 44),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<Widget> _userImage(ProfileViewModel viewModel) async {
    if (viewModel.selectedID != null) {
      var userImage =
          await UserImage().returnSelectedUserImage(viewModel.selectedID);
      return Image.file(
        userImage,
        fit: BoxFit.contain,
        width: 260,
        height: 260,
        alignment: Alignment.bottomCenter,
      );
    } else {
      return Container(
          child: SvgPicture.asset(
        'Asset/SVG/ImagePlaceholder.svg',
        fit: BoxFit.contain,
      ));
    }
  }

  Widget header(ProfileViewModel viewModel) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      width: MediaQuery.of(context).size.width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              AutoRouter.of(context).pop();
            },
            child: SvgPicture.asset(
              'Asset/Icons/Arrow-Back-BG.svg',
              width: 36,
            ),
          ),
          Spacer(),
          // GestureDetector(
          //   onTap: () async {
          //     AutoRouter.of(context)
          //         .push(EditProfileViewRoute(onDetailsSaved: () {
          //       widget.onProfileDetailsChange.call();
          //       setState(() {});
          //     }));
          //   },
          //   child: Container(
          //     padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(30),
          //       color: Colors.black.withAlpha(120),
          //     ),
          //     child: Text(
          //       'Edit Profile',
          //       style: TextStyle(
          //           color: Colors.white, fontSize: 16, fontFamily: 'Mukta'),
          //     ),
          //   ),
          // ),
          SizedBox(
            width: 12,
          ),
          ElevatedButton(
              onPressed: () async {
                viewModel.onTapChangeParty(context);
              },
              style: ElevatedButton.styleFrom(
                  // onPrimary: Colors.white,
                  backgroundColor: Colors.grey),
              child: Text(
                "Changes State",
              )),
          SizedBox(
            width: 12,
          ),
          GestureDetector(
            onTap: () {
              AutoRouter.of(context).push(SettingsViewRoute());
            },
            child: SvgPicture.asset(
              'Asset/Icons/Settings-Icon.svg',
              width: 36,
            ),
          ),
        ],
      ),
    );
  }

  Widget profileImageGrid(ProfileViewModel viewModel) {
    ThemeData themeData = Theme.of(context);
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          alignment: Alignment.topLeft,
          child: Row(
            children: [
              Text('Saved Profile Pictures',
                  style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Work-Sans',
                      fontWeight: FontWeight.w500,
                      color: themeData.colorScheme.onBackground)),
              // const Spacer(),
              SizedBox(
                width: 10,
              ),
              ElevatedButton(
                onPressed: () async {
                  await viewModel.userImageChange(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      Colors.orange, // Set the background color to orange
                  // onPrimary: Colors.white, // Set the text color to white
                  // minimumSize: Size(double.infinity,
                  //     48), // Make the button take the full width
                  // padding: EdgeInsets.symmetric(
                  //     vertical: 16), // Adjust the padding as needed
                  // shape: RoundedRectangleBorder(
                  //   borderRadius: BorderRadius.circular(
                  //       10), // Adjust the border radius as needed
                  // ),
                ),
                child: Text(
                  "Change Photo",
                  style: TextStyle(
                    fontSize: 16, // Adjust the font size as needed
                    fontWeight:
                        FontWeight.bold, // Adjust the font weight as needed
                  ),
                ),
              )

              // Padding(
              //   padding: const EdgeInsets.symmetric(horizontal: 16),
              //   child: PrimaryButton(
              //     iconPath: 'Asset/Icons/User-Photo.svg',
              //     isEnabled: true,
              //     height: 48,
              //     isLoading: false,
              //     onTap: () async {
              //       await viewModel.userImageChange(context);
              //     },
              //     label: 'Change Photo',
              //     color: themeData.colorScheme.primary,
              //   ),
              // ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 12),
          child: FutureBuilder(
            future: UserImage().returnListFileAddress(),
            builder: (BuildContext context, AsyncSnapshot snapshot) {
              if (snapshot.hasData && snapshot.data != null) {
                List<File> returnedUserAddress = snapshot.data;
                returnedUserAddress = returnedUserAddress.reversed.toList();
                return GridView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.vertical,
                  shrinkWrap: true,
                  itemCount: snapshot.data.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemBuilder: (BuildContext GridContext, int index) {
                    return GestureDetector(
                      onTap: () {
                        showCustomModalBottomSheet(index, context, viewModel);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          border: Border.all(
                            color: viewModel.selectedID == index
                                ? Colors.green
                                : Colors.white54,
                            width: 3,
                            strokeAlign: BorderSide.strokeAlignOutside,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        clipBehavior: Clip.hardEdge,
                        child: Image.file(
                          returnedUserAddress[index],
                          fit: BoxFit.cover,
                          alignment: Alignment.topCenter,
                        ),
                      ),
                    );
                  },
                );
              } else {
                return SizedBox();
              }
            },
          ),
        ),
      ],
    );
  }

  Future<void> showCustomModalBottomSheet(
      int index, BuildContext gridContext, ProfileViewModel viewModel) async {
    await showModalBottomSheet(
      isScrollControlled: true,
      context: gridContext,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(20),
          topLeft: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          alignment: Alignment.center,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                alignment: Alignment.center,
                width: 52,
                height: 4,
                decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(100),
                    borderRadius: BorderRadius.circular(40)),
              ),
              SizedBox(height: 24),
              GestureDetector(
                onTap: () async {
                  viewModel.prefs.setInt('SelectedID', index);
                  widget.onProfileDetailsChange.call();
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => MyBottomBarView(),
                    ),
                  );
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text('Photo Updated'),
                    duration: Duration(seconds: 1),
                  ));
                },
                child: Row(
                  children: [
                    Icon(Icons.account_box, color: Colors.black87, size: 32),
                    SizedBox(width: 12),
                    Text('Make this primary',
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Work-Sans',
                            fontSize: 16,
                            color: Colors.black87)),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
