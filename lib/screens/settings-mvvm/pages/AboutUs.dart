import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AboutUs extends StatelessWidget {
  const AboutUs({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Sized<PERSON><PERSON>(
                  height: 28,
                ),
                GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: SvgPicture.asset(
                      'Asset/Icons/ArrowLeft.svg',
                      height: 36,
                    )),
                SizedBox(
                  height: 36,
                ),
                Text(
                  'About Us',
                  style: Theme.of(context).textTheme.displayMedium,
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  'DZINE BOX CONSULTING SOLUTION is committed to delivering innovative and user-friendly mobile applications that empower individuals to unleash their creativity and make a difference. With our Poster for Bharat app, we provide a straightforward yet powerful tool for users to create impactful posters using their own photos. Our mission is to enable users to share their political views, support causes, and ignite conversations through visually compelling and shareable content. Join us and let your voice be heard with Poster for Bharat by DZINE BOX CONSULTING SOLUTION.',
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
