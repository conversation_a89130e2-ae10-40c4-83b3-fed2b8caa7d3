import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EULADocument extends StatelessWidget {
  const EULADocument({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 28,
                ),
                GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: SvgPicture.asset(
                      'Asset/Icons/ArrowLeft.svg',
                      height: 36,
                    )),
                SizedBox(
                  height: 36,
                ),
                Text(
                  'End-User License Agreement (EULA) for Poster For Bharat',
                  style: Theme.of(context).textTheme.displayMedium,
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  'This End-User License Agreement ("EULA") is a legal agreement between you (referred to as "User" or "You") and DZINE BOX CONSULTING SOLUTION (referred to as "Developer" or "We") for the use of the Poster for Bharat (referred to as "App"). By installing or using the App, You agree to be bound by the terms and conditions of this EULA.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '1. License Grant: Developer provides You with a limited, non-exclusive, non-transferable, revocable license to use the App solely for personal, non-commercial purposes on devices that You own or control. You are not permitted to use the App for any other purpose or on different devices without obtaining prior written approval from Developer.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '2. User-Generated Content: The App allows You to upload your photos to create posters that can be shared. You agree that You are fully responsible for the content You upload and confirm that You have the necessary rights, permissions, and consents to use it. Although Developer does not claim ownership of your content, You grant Developer a global, royalty-free, perpetual, irrevocable, and sublicensable license to use, reproduce, modify, adapt, publish, translate, distribute, and display your content for the purpose of operating and improving the App and its features.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '3. Restrictions: Without prior written consent from Developer, You agree not to: (a) copy, modify, or distribute the App; (b) reverse engineer, decompile, or disassemble the App; (c) remove, alter, or obscure any proprietary notices or labels on the App; (d) use the App in a manner that violates applicable laws or regulations; (e) use the App to infringe upon the intellectual property or privacy rights of any third party; (f) use the App to create, distribute, or share any offensive, harmful, or illegal content; or (g) use the App to engage in any fraudulent or unauthorized activities.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '4. Intellectual Property: The App, including its design, graphics, logos, trademarks, and content, is owned by Developer and protected under intellectual property laws. You understand that using the App does not grant You any ownership or intellectual property rights in the App. All rights not explicitly granted to You are reserved by Developer.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '5.  Privacy: Developer may collect and use Your personal information in line with its Privacy Policy, which can be accessed within the App. By using the App, You agree to the collection and use of Your personal information as outlined in the Privacy Policy.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '6. Warranty Disclaimer: The App is provided "as is" without any warranties or representations, whether express or implied, including but not limited to implied warranties of merchantability, fitness for a particular purpose, and non-infringement. Developer does not guarantee that the App will be error-free, uninterrupted, or free from viruses or other harmful elements.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '7. Limitation of Liability: To the fullest extent allowed by applicable law, Developer and its affiliates, officers, directors, employees, agents, and contractors shall not be liable for any direct, indirect, incidental, consequential, special, or exemplary damages arising from or related to the use or inability to use the App, even if Developer has been advised of the potential for such damages.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '8. Indemnification: You agree to indemnify, defend, and hold harmless Developer and its affiliates, officers, directors, employees, agents, and contractors from any claims, damages, losses, liabilities, and expenses (including attorney fees) arising from or related to Your use of the App, Your content, or any breach of this EULA.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '9. Termination: Developer may terminate this EULA and revoke Your access to the App at any time, with or without notice, for any reason or no reason, including but not limited to Your breach of this EULA. Upon termination, You must immediately stop using the App and delete any copies of the App that are in Your possession or control',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '10. Updates and Modifications: Developer may, at its sole discretion, release updates, modifications, or new versions of the App periodically, which may include bug fixes, improvements, or new features. You agree that these updates or modifications may be installed automatically and consent to receiving them.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '11. Governing Law and Jurisdiction: This EULA shall be governed by and interpreted in accordance with the laws of the jurisdiction where Developer is located, without regard to conflicts of law principles. Any disputes arising from or related to this EULA shall be resolved exclusively by the competent courts in the jurisdiction where Developer is located.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '12. Entire Agreement: This EULA represents the complete and exclusive agreement between You and Developer concerning the subject matter and supersedes all prior or contemporaneous agreements, understandings, and representations, whether oral or written. Any changes or amendments to this EULA must be made in writing and signed by both parties.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '13. Severability: If any provision of this EULA is determined to be invalid, illegal, or unenforceable, the remaining provisions will remain in full force and effect.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '14. Waiver: The failure of Developer to enforce any provision of this EULA does not constitute a waiver of that provision or any other provision of this EULA.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '15. Assignment: You may not assign or transfer this EULA or any of Your rights or obligations under it without prior written consent from Developer. Developer may assign or transfer this EULA freely and without restriction.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '16. Contact Information: If You have any questions or comments regarding this EULA or the App, please reach out to Developer at [<EMAIL>].',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
