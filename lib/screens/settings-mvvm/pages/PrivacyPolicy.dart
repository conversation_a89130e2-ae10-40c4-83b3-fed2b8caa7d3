import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PrivacyDocument extends StatelessWidget {
  const PrivacyDocument({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 28,
                ),
                GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: SvgPicture.asset(
                      'Asset/Icons/ArrowLeft.svg',
                      height: 36,
                    )),
                SizedBox(
                  height: 36,
                ),
                Text(
                  'Privacy Policy',
                  style: Theme.of(context).textTheme.displayMedium,
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  'DZINE BOX CONSULTING SOLUTION ("Developer," "we," "our," or "us") values your privacy and is dedicated to safeguarding your personal information. This Privacy Policy outlines how we collect, use, disclose, and protect the personal information you provide while using the Poster for Bharat app (the "App"). The App is operated by DZINE BOX CONSULTING SOLUTION.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '1. Information Collection: When you use the App, we may gather personal details such as your name, email address, and any photos you upload. We may also collect non-personal information, including data on how you use the App, device specifics, and analytics, to help us improve the App  performance and features.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '2. Use of Information: We may utilize the personal information we collect to deliver and enhance the App, address your inquiries or requests, send you promotional content, perform research and analysis, and meet legal obligations.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '3.Disclosure of Information: We may share your personal information with third-party service providers who assist us in delivering the App, to comply with legal obligations, to protect our rights and assets, and for other lawful purposes as allowed by law.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '4. Security: We implement reasonable measures to safeguard the personal information you provide through the App from loss, theft, unauthorized access, disclosure, alteration, and destruction. However, we cannot guarantee that data transmission or storage is completely secure. It is your responsibility to maintain the confidentiality of your account information and to take appropriate steps to protect your account and data.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '5. Third-Party Links: The App may include links to websites or services operated by third parties that are not under our control. We are not responsible for the privacy practices or content of these third-party sites or services. We recommend that you review the privacy policies of any third parties before sharing any personal information with them.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '6. Children Privacy: The App is not designed for children under the age of 13. We do not intentionally collect or seek personal information from children under 13. If we become aware that we have collected personal information from a child under 13, we will promptly remove that information.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '7. Changes to Privacy Policy: We may periodically update this Privacy Policy at our discretion. We will inform you of any significant changes by posting the revised Privacy Policy on the App or through other methods. Your continued use of the App after such updates signifies your acceptance of the new Privacy Policy.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '8. Contact Information: If you have any questions or concerns regarding this Privacy Policy or the App, please reach out to <NAME_EMAIL>.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  'By using the Poster For Bharat app, you acknowledge that you have read, understood, and agreed to the terms and conditions outlined in this Privacy Policy.',
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
