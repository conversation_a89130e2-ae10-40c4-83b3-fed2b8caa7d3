import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class REFUNDDocument extends StatelessWidget {
  const REFUNDDocument({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 28,
                ),
                GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: SvgPicture.asset(
                      'Asset/Icons/ArrowLeft.svg',
                      height: 36,
                    )),
                SizedBox(
                  height: 36,
                ),
                Text(
                  'Cancellation and Refund Policy',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  "1.Premium Plan Services:",
                  // style: Theme.of(context).textTheme.displayMedium,
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Poster for Bharat offers a premium plan that enables users to create and share personalized posters featuring their photos and names. Access to these premium services requires a recurring subscription fee.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '2.Cancellation Process:',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Users can request cancellation at any time <NAME_EMAIL> and including their registered mobile number or email ID. Membership cancellations will be processed within 24 hours of receiving the request.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '3. Refund Policy:',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Payments made before cancellation are non-refundable',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '4. Continuation of Benefits:',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Users will retain access to premium benefits until the end of the current billing cycle, even if the cancellation occurs during the subscription period.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '5.Dispute Resolution:',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'The company reserves the right to arbitrate any disputes that arise from the cancellation or refund process.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '6.Account Deletion:',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Users can request the deletion of their account and associated data by emailing their concern to  <EMAIL>',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  'Note: It is essential to review and acknowledge these terms before proceeding with the cancellation or account deletion process. By submitting the form, you confirm that you have read and agree to the conditions outlined in this document.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
