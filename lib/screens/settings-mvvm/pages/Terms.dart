import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class TERMSDocument extends StatelessWidget {
  const TERMSDocument({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 28,
                ),
                GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: SvgPicture.asset(
                      'Asset/Icons/ArrowLeft.svg',
                      height: 36,
                    )),
                SizedBox(
                  height: 36,
                ),
                Text(
                  'Terms and Conditions for  Poster for Bharat',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 16,
                ),

                // ),
                Text(
                  "1.Introduction",
                  // style: Theme.of(context).textTheme.displayMedium,
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Welcome to the Poster for Bharat App! These Terms of Use ("Terms") along with the Poster for Bharat mobile application (together referred to as the "Platform") are provided by Dzine Box Consulting Solution ("Poster for Bharat App," "Company," "we," "us," or "our"). By accessing and using the Platform, you agree to comply with these Terms. Please review them carefully. These Terms should be read in conjunction with the Poster for Bharat App Privacy Policy. If you do not agree with these Terms and conditions, kindly refrain from using the Platform.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '2. Changes to Terms and Services',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),

                SizedBox(
                  height: 8,
                ),
                Text(
                  'Poster For Bharat is constantly evolving, and our services may change over time. We reserve the right to adjust the services and features provided on the Platform at any time, without prior notice. Please review this page regularly for updates and developments. Your continued use of the Platform signifies your acceptance of any updated terms.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '3. User Eligibility',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'The Poster For Bharat App is designed to help you engage with your community and share content. You are permitted to use our services if you are able to enter into a binding agreement with us and are legally allowed to do so in your jurisdiction. If you are accepting these Terms on behalf of a company or legal entity, you confirm that you have the authority to bind that entity to this agreement.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '4. How to Use Our Services',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'The Poster For Bharat App provides a unique platform that promotes community involvement by supporting your political party. To access our services, you are required to register on the app by providing your name and mobile number. We value your privacy and will not access any information on your device without your consent.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '5. Cancellation and Refund',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Poster For Bharat App provides a premium plan for accessing new templates, available with both monthly and annual payment options.'
                  'You may cancel your subscription at any time by contacting <NAME_EMAIL> with your registered mobile number or email ID.'
                  'Membership cancellations will be processed within 24 hours of receiving the cancellation request.'
                  'Payments made before cancellation are non-refundable.'
                  'Premium features will remain active until the end of the current billing cycle.'
                  'The company reserves the right to interpret these terms in the event of any disputes.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '6. Privacy Policy',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'To improve our services, we collect information in accordance with the Poster For Bharat App Privacy Policy. This includes your phone number and name, which are securely stored on AWS and Google Cloud servers.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '7. Your Commitments',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Provide accurate information on the Platform.'
                  'Maintain the security of your device by using antivirus software.'
                  'Adhere to Poster For Bharat App’s content removal and account termination policies.'
                  'Ensure that your use of the Platform complies with applicable Indian laws.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '8. Content Rights and Liabilities',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Poster For Bharat App values freedom of expression. Users retain ownership of the content they share, and Poster For Bharat App does not assert any intellectual property rights over user-generated content.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '9. Intermediary Status and No Liability',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Poster For Bharat App functions as an intermediary under Indian law, offering a platform for users. The app is not responsible for user actions and does not endorse any content shared by users.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '10. Permissions You Give to Us',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Accept automatic downloads and updates for the Poster For Bharat App mobile application.'
                  'Grant permission for the use of cookies to ensure Platform functionality.'
                  'Agree to the data retention policies as outlined in the Poster For Bharat App Privacy Policy.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '11. Limitation of Liability',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Poster For Bharat App is not responsible for any loss or damage resulting from inaccuracies in information or breaches. The Platform is provided on an "as is" basis, and Poster For Bharat App liability is limited to the amount paid for the use of the Platform',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '12. Indemnification',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'Users agree to indemnify Poster For Bharat App against any claims arising from their use of the Platform, including breaches of obligations and violations of third-party rights.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '13. Unsolicited Material',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  "Poster For Bharat App values feedback and may use suggestions without any obligation to provide compensation. Additionally, Poster For Bharat App is not required to keep such suggestions confidential.",
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '14. General',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'If any part of these Terms is found to be unenforceable, the remaining provisions will still apply. Amendments or waivers to these Terms must be made in writing. Failure to enforce any provision of these Terms does not waive Poster For Bharat App’s rights. Poster For Bharat App reserves all rights not expressly granted.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '15. Dispute Resolution',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'All disputes will be governed by the laws of the Republic of India, and the courts in Lucknow will have exclusive jurisdiction.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  '16. Grievance Officer',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.07, // Responsive font size
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Text(
                  'For concerns related to data safety, privacy, and Platform usage, please contact our Grievance <NAME_EMAIL>.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
                Text(
                  'We firmly believe in the freedom of expression and encourage you to share photos, images, videos, music, and other content on our Platform. We do not claim ownership of the content you share, and all rights to your content remain solely with you. However, you must not use our Platform to violate or infringe upon the intellectual property rights of Dzine Box Consulting Solution or any third party. Such actions are prohibited under the Dzine Box Consulting Solution Terms of Use and may result in the removal of the content from our Platform.If you use any content created by us, we retain the intellectual property rights to that content. By sharing, posting, or uploading content through our Services, you grant us a non-exclusive, royalty-free, transferable, sub-licensable, worldwide license to host, use, distribute, display, perform publicly, translate, and create derivative works from your content (in line with your privacy and application settings).You may request the deletion of your content and/or account at any time. However, if your content has been shared with others, it may continue to appear on the Platform. To learn more about how we handle your information and how you can manage or delete your content, please refer to the Dzine Box Consulting Solution Privacy Policy.You remain fully responsible for the content you post on our Platform. We do not endorse and are not liable for any content shared or posted through our Platform, nor for the consequences of such sharing or posting. The presence of our logo or any trademark on content you share does not imply our endorsement or sponsorship. Additionally, we are not responsible for the outcomes of any transactions you engage in with other users of the Platform. You retain ownership and responsibility for the content you share. While we will not claim intellectual property rights over your content, we will have a cost-free, perpetual license to use the content you share and post on our Platform.',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width *
                        0.060, // Responsive font size
                    // fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
