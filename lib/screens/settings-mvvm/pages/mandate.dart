import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

class Mnadate extends StatelessWidget {
  const Mnadate({Key? key}) : super(key: key);
  final String url = 'https://youtu.be/ZkpwDqHAbeY?si=2xdmUZKpYwh_pFgJ';

  void _launchURL() async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: 20,
            ),
            GestureDetector(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(
                  'Asset/Icons/ArrowLeft.svg',
                  height: 36,
                )),
            SizedBox(
              height: 50,
            ),
            Text(
              'Create a Subscription Mandate',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              "1. Open the app and select a poster to share.\n"
              "2. You’ll be prompted to buy a subscription.\n"
              "3. After clicking, you’ll be redirected to the payment gateway.\n"
              "4. Choose UPI as the payment method.\n"
              "5. Confirm the subscription plan (monthly/yearly). Auto-pay will be set up, and the first payment will be deducted.\n"
              "6. Your premium membership will activate immediately.",
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              'Amend Your Subscription Mandate',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              "1. Open your UPI app and go to the Auto-Pay/Subscriptions section.\n2. Select the current Poster App subscription and cancel the auto-pay.\n3. Return to the Poster App, select a poster, and proceed to buy a new subscription.\n4. You can now choose a new payment method (UPI, credit card, etc.) or a different UPI app to create a fresh mandate.",
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              'Cancel a Mandate & Suspend and Revoke a Mandate',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            SizedBox(
              height: 10,
            ),
            Text(
              'Click on this link mentioned below ',
              style: TextStyle(fontSize: 20),
            ),
            ElevatedButton(
              onPressed: _launchURL,
              child: Text(url),
            ),
          ],
        ),
      ),
    ));
  }
}
