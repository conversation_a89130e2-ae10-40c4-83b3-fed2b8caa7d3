import 'package:auto_route/auto_route.dart';
import 'package:bjpnew/global/custom_toast.dart';
import 'package:bjpnew/screens/home-mvvm/home_view.dart';
import 'package:bjpnew/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:bjpnew/global/bjp_card.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/route/route.gr.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/Refundpolicy.dart';
import 'package:bjpnew/screens/settings-mvvm/pages/Terms.dart';
import 'package:bjpnew/screens/settings-mvvm/settings_viewmodel.dart';
import 'package:stacked/stacked.dart';
import 'pages/AboutUs.dart';
import '../../global/CustomSecondaryButton.dart';
import 'pages/EULA.dart';
import '../../services/support_email.dart';
import 'pages/PrivacyPolicy.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class SettingsView extends StatefulWidget {
  const SettingsView({super.key});

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  final SettingsViewModel _settingsViewModel = SettingsViewModel();

  @override
  void initState() {
    super.initState();
    _settingsViewModel.checkPremiumStatus();
  }

  @override
  Widget build(BuildContext context) {
    const IconData assignment_sharp =
        IconData(0xe7a8, fontFamily: 'MaterialIcons', matchTextDirection: true);
    const IconData admin_panel_settings_rounded =
        IconData(0xf540, fontFamily: 'MaterialIcons');
    ThemeData themeData = Theme.of(context);
    return ViewModelBuilder<SettingsViewModel>.reactive(
        viewModelBuilder: () => _settingsViewModel,
        onViewModelReady: (viewModel) => viewModel.onViewModelReady(),
        builder: (context, viewModel, child) => SafeArea(
              child: Scaffold(
                appBar: AppBar(
                  backgroundColor: themeData.colorScheme.background,
                  elevation: 0,
                  centerTitle: false,
                  title: Text(
                    'Premium Status',
                    style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Work-Sans',
                        fontSize: 24,
                        color: themeData.colorScheme.onBackground),
                  ),
                ),
                body: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      premiumDetails(viewModel),
                      const SizedBox(height: 20),
                      Text(
                        viewModel.userEmail ?? '',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Colors.black87,
                            fontSize: 14,
                            fontFamily: 'Mukta'),
                      ),
                      Text(
                        'This app is under DZINE BOX CONSULTING SOLUTION  ',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Colors.black87,
                            fontSize: 14,
                            fontFamily: 'Mukta'),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      cancellation(viewModel),
                      SizedBox(
                        height: 10,
                      ),
                      // viewModel.subscriptionStatus == "CANCELLED"
                      //     ? SizedBox()
                      //     : pause(viewModel),
                      // SizedBox(
                      //   height: 10,
                      // ),
                      // viewModel.subscriptionStatus == "CANCELLED"
                      //     ? SizedBox()
                      //     : resume(viewModel)
                    ],
                  ),
                ),
              ),
            ));
  }

  Widget premiumDetails(SettingsViewModel viewModel) {
    ThemeData themeData = Theme.of(context);

    if (viewModel.getRemainingDays() != null) {
      int remainingDays = viewModel.getRemainingDays()!;
      if (remainingDays < 0) {
        remainingDays = 0;
      }

      if (viewModel.subscriptionStatus == null) {
        viewModel.getSubscriptionStatus();
      }

      return Column(
        children: [
          SizedBox(
            height: 32,
          ),
          Container(
            padding: EdgeInsets.all(16),
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, 0),
                  spreadRadius: 2,
                  blurRadius: 14,
                  color: themeData.colorScheme.shadow.withAlpha(30),
                ),
              ],
            ),
            child: Row(
              children: [
                SvgPicture.asset(
                  'Asset/SVG/PremiumCrown.svg',
                  height: 64,
                ),
                SizedBox(
                  width: 12,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      remainingDays <= 0
                          ? 'आपका प्रीमियम समाप्त हो गया है'
                          : remainingDays <= 3
                              ? 'आपका प्रीमियम समाप्त होने वाला है'
                              : 'प्रीमियम एक्टिव है',
                      style: TextStyle(
                        fontFamily: 'Work-Sans',
                        fontWeight: FontWeight.w500,
                        fontSize: 20,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    Text(
                      'बचे हुए दिन: $remainingDays',
                      style: TextStyle(
                        color: remainingDays <= 3 ? Colors.red : Colors.black87,
                        fontFamily: 'Work-Sans',
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                      ),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    Text(
                      'Subscription Status : \n ${viewModel.subscriptionStatus}',
                      style: TextStyle(
                        color: viewModel.subscriptionStatus == 'CONFIRMED'
                            ? Colors.green
                            : Colors.red,
                        fontFamily: 'Work-Sans',
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      softWrap: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      return SizedBox();
    }
  }

  Widget cancellation(SettingsViewModel viewModel) {
    ThemeData themeData = Theme.of(context);
    return GestureDetector(
        onTap: () async {
          await showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  contentPadding: EdgeInsets.all(16),
                  backgroundColor: Colors.white,
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LottieBuilder.asset(
                        'Asset/Lottie/alert.json',
                        height: 84,
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      Text(
                        'Want to cancel autopay ?',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 24,
                            fontFamily: 'Mukta',
                            height: 1.2,
                            color: Colors.black87),
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Text(
                        'After doing this you will have to recharge again to share the poster.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 20,
                            fontFamily: 'Mukta',
                            height: 1.2,
                            color: Colors.black54),
                      ),
                      SizedBox(
                        height: 24,
                      )
                    ],
                  ),
                  actions: [
                    PrimaryButton(
                      onTap: () => Navigator.pop(context),
                      label: 'No, don\'t cancel',
                      isEnabled: true,
                      height: 48,
                      isLoading: false,
                      color: themeData.colorScheme.primary,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    CustomSecondaryButton(
                      showIcon: false,
                      leadingIcon: '',
                      buttonColor: Colors.transparent,
                      onPressed: () {
                        onCancel(viewModel);
                        // const url =
                        //     'https://youtu.be/qVMj3I874zw?si=UQ2LHHpKMv05Gx9v';
                        // if (await canLaunch(url)) {
                        //   await launch(url);
                        // } else {
                        //   throw 'Could not launch $url';
                        // }
                        // await viewModel.cancelSubscription();
                        // AutoRouter.of(context).pop();
                        // await successMessage();
                      },
                      buttonText: 'Yes, cancel',
                    )
                  ],
                );
              });
        },
        child: viewModel.subscribedUser &&
                viewModel.subscriptionStatus != "CANCELLED"
            ? Text(
                'Cancel Autopay Subscription',
                style: TextStyle(
                    fontFamily: 'Work-Sans',
                    fontSize: 16,
                    fontWeight: FontWeight.w800,
                    color: Colors.red),
              )
            : SizedBox());
  }

  Widget resume(SettingsViewModel viewModel) {
    ThemeData themeData = Theme.of(context);
    return GestureDetector(
        onTap: () async {
          await showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  contentPadding: EdgeInsets.all(16),
                  backgroundColor: Colors.white,
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LottieBuilder.asset(
                        'Asset/Lottie/alert.json',
                        height: 84,
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      Text(
                        'Want to Resume Subscription ?',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 24,
                            fontFamily: 'Mukta',
                            height: 1.2,
                            color: Colors.black87),
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Text(
                        'By doing this your subscription will be activated',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 20,
                            fontFamily: 'Mukta',
                            height: 1.2,
                            color: Colors.black54),
                      ),
                      SizedBox(
                        height: 24,
                      )
                    ],
                  ),
                  actions: [
                    PrimaryButton(
                      onTap: () => Navigator.pop(context),
                      label: 'No, don\'t Resume.',
                      isEnabled: true,
                      height: 48,
                      isLoading: false,
                      color: themeData.colorScheme.primary,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    CustomSecondaryButton(
                      showIcon: false,
                      leadingIcon: '',
                      buttonColor: Colors.transparent,
                      onPressed: () {
                        OnResume(viewModel);
                        // const url =
                        //     'https://youtu.be/qVMj3I874zw?si=UQ2LHHpKMv05Gx9v';
                        // if (await canLaunch(url)) {
                        //   await launch(url);
                        // } else {
                        //   throw 'Could not launch $url';
                        // }
                        // await viewModel.cancelSubscription();
                        // AutoRouter.of(context).pop();
                        // await successMessage();
                      },
                      buttonText: 'Yes, Resume',
                    )
                  ],
                );
              });
        },
        child: viewModel.subscribedUser
            ? Text(
                'Resume Subscription',
                style: TextStyle(
                    fontFamily: 'Work-Sans',
                    fontSize: 16,
                    fontWeight: FontWeight.w800,
                    color: Colors.red),
              )
            : SizedBox());
  }

  Widget pause(SettingsViewModel viewModel) {
    ThemeData themeData = Theme.of(context);
    return GestureDetector(
        onTap: () async {
          await showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  contentPadding: EdgeInsets.all(16),
                  backgroundColor: Colors.white,
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LottieBuilder.asset(
                        'Asset/Lottie/alert.json',
                        height: 84,
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      Text(
                        'Want to pause autopay?',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 24,
                            fontFamily: 'Mukta',
                            height: 1.2,
                            color: Colors.black87),
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Text(
                        // 'If you are in a subscription of Rs 5 and the auto cycle(99 rs) has not started even once, then your subscription will be canceled. If you do this, you will have to recharge again. Poster is for sharing.',
                        'By doing this your subscription will be paused.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 20,
                            fontFamily: 'Mukta',
                            height: 1.2,
                            color: Colors.black54),
                      ),
                      SizedBox(
                        height: 24,
                      )
                    ],
                  ),
                  actions: [
                    PrimaryButton(
                      onTap: () => Navigator.pop(context),
                      label: 'No, don\'t pause',
                      isEnabled: true,
                      height: 48,
                      isLoading: false,
                      color: themeData.colorScheme.primary,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    CustomSecondaryButton(
                      showIcon: false,
                      leadingIcon: '',
                      buttonColor: Colors.transparent,
                      onPressed: () {
                        OnPause(viewModel);
                      },
                      buttonText: 'Yes, pause',
                    )
                  ],
                );
              });
        },
        child: viewModel.subscribedUser
            ? Text(
                'Pause Subscription',
                style: TextStyle(
                    fontFamily: 'Work-Sans',
                    fontSize: 16,
                    fontWeight: FontWeight.w800,
                    color: Colors.red),
              )
            : SizedBox());
  }

  Future<void> successMessage() async {
    ThemeData themeData = Theme.of(context);
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
            backgroundColor: Colors.white,
            content: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(
                      width: 150,
                      height: 150,
                      child: Lottie.asset('Asset/Lottie/success-lottie.json'),
                    ),
                    Text('ऑटोपे कैंसिल कर दिया गया है',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 20,
                            fontFamily: 'Mukta',
                            fontWeight: FontWeight.w600,
                            color: themeData.colorScheme.onSurface))
                  ],
                ),
              ),
            ),
            actions: <Widget>[
              PrimaryButton(
                isEnabled: true,
                height: 48,
                isLoading: false,
                onTap: () {
                  Navigator.pop(context);
                },
                label: 'Okay',
                color: themeData.colorScheme.primary,
              )
            ],
          );
        });
  }

  Future OnPause(SettingsViewModel viewModel) async {
    var sucess = await viewModel.pauseSubscription();
    if (sucess == true) {
      Navigator.push(
          context, MaterialPageRoute(builder: (context) => HomeView()));
    } else {
      showToast(context, 'Something went wrong!');
    }
  }

  Future OnResume(SettingsViewModel viewModel) async {
    var sucess = await viewModel.resumeSubscription();
    if (sucess == true) {
      Navigator.push(
          context, MaterialPageRoute(builder: (context) => HomeView()));
    } else {
      showToast(context, 'Something went wrong!');
    }
  }

  Future onCancel(SettingsViewModel viewModel) async {
    var sucess = await viewModel.cancelSubscription();
    // await viewModel.checkPremiumStatus();
    if (sucess == true) {
      // showToast(
      //     context, 'Your Autopay Subscription has sucessfully cancelled!');
      // Navigator.push(
      //     context, MaterialPageRoute(builder: (context) => HomeView()));
      Utils.showSuccessDialog(
          context: context,
          title: "Your Auto-pay is successfully cancelled",
          subText: "to share poster with photo purchase again",
          onTap: () {
            Navigator.push(
                context, MaterialPageRoute(builder: (context) => HomeView()));
          });
    } else {
      showToast(context, 'Something went wrong!');
      Navigator.push(
          context, MaterialPageRoute(builder: (context) => HomeView()));
    }
  }
}
