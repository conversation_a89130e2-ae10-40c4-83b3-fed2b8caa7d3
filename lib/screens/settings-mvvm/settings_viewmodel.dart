import 'dart:developer';

import 'package:bjpnew/services/user_db_operations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:bjpnew/services/cancel_subscriptions.dart';
import 'package:bjpnew/utils/Singletons/prefs_singleton.dart';
import 'package:bjpnew/utils/authentication/authentication.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:stacked/stacked.dart';
import 'package:http/http.dart' as http;

class SettingsViewModel extends BaseViewModel {
  final Prefs _prefs = Prefs.instance;

  int getRemainingDays() {
    int? premiumTill = _prefs.getInt('premiumTill');
    if (premiumTill == null) return 0;

    DateTime now = DateTime.now();
    DateTime premiumTillDate = DateTime.fromMillisecondsSinceEpoch(premiumTill);

    // Calculate the difference in days
    int daysRemaining = premiumTillDate.difference(now).inDays;

    return daysRemaining;
  }

  String formatRemainingDays(int days) {
    if (days <= 0) {
      return 'Expired';
    } else if (days == 1) {
      return '1 day remaining';
    } else {
      return '$days days remaining';
    }
  }

  String get remainingDaysFormatted => formatRemainingDays(getRemainingDays());

  bool get subscribedUser => _prefs.getBool('isPremium') ?? false;
  set subscribedUser(value) => _prefs.setBool('isPremium', value);

  String? get userEmail => FirebaseAuth.instance.currentUser?.email;

  //----------------------------
  String? _subscriptionStatus;
  String? get subscriptionStatus => _subscriptionStatus;

  Future<String?> getSubscriptionStatus() async {
    _subscriptionStatus = _prefs.getString('subscriptionStatus');
    if (_subscriptionStatus == null) {
      await checkPremiumStatus();
    }
    return _subscriptionStatus;
  }

  Future<void> loadSubscriptionStatus() async {
    _subscriptionStatus = _prefs.getString('subscriptionStatus');
    notifyListeners(); // Updates the UI
  }

  Future checkPremiumStatus() async {
    await UserDatabase().checkPremiumStatus() ?? false;
    loadSubscriptionStatus();
  }

  Future<bool> pauseSubscription() async {
    final String token = _prefs.getString('token') ?? '';
    final String userId = _prefs.getString('userId') ?? '';
    final String? email = _prefs.getString('email');
    final String? deviceId = _prefs.getString('deviceId');
    print(email);

    final String url =
        'https://backend.designboxconsuting.com/poster/subscription/v1/subscription/update?action=PAUSE';

    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      'token': token,
      'app-user-id': userId,
      'package-name': 'com.postersforb.BB'
    };

    try {
      final response = await http.post(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        print('Subscription pause successfully: ${response.body}');
        return true;
      } else {
        print(
            'Failed to  pause subscription: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error: $e');
      return false;
    }
  }

  // Future cancelSubscription() async {
  //   String? userID = FirebaseAuth.instance.currentUser?.email;
  //   await CancelSubscriptions(userID: userID ?? '').cancelSubscription();
  //   _prefs.setBool('isSubscribedUser', false);
  //   notifyListeners();
  // }

  Future<bool> cancelSubscription() async {
    final String token = _prefs.getString('token') ?? '';
    final String userId = _prefs.getString('userId') ?? '';
    final String? email = _prefs.getString('email');
    final String? deviceId = _prefs.getString('deviceId');
    print(email);

    final String url =
        'https://backend.designboxconsuting.com/poster/subscription/v1/subscription/update?action=CANCEL';

    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      'token': token,
      'app-user-id': userId,
      'package-name': 'com.postersforb.BB'
    };

    try {
      final response = await http.post(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        print('Subscription canceled successfully: ${response.body}');
        checkPremiumStatus();
        return true;
      } else {
        print(
            'Failed to cancel subscription: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error: $e');
      return false;
    }
  }

  Future<bool> resumeSubscription() async {
    final String token = _prefs.getString('token') ?? '';
    final String userId = _prefs.getString('userId') ?? '';
    final String? email = _prefs.getString('email');
    final String? deviceId = _prefs.getString('deviceId');
    print(email);

    final String url =
        'https://backend.designboxconsuting.com/poster/subscription/v1/subscription/update?action=RESUME';

    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      'token': token,
      'app-user-id': userId,
      'package-name': 'com.postersforb.BB'
    };

    try {
      final response = await http.post(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        print('Subscription resume successfully: ${response.body}');
        checkPremiumStatus();
        return true;
      } else {
        print(
            'Failed to cancel subscription: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error: $e');
      return false;
    }
  }

  Future onViewModelReady() async {
    await loadSubscriptionStatus();
  }
}
