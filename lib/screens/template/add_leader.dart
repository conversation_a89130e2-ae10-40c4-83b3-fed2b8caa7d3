import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_launcher_icons/xml_templates.dart';

import 'package:image_picker/image_picker.dart';

import 'package:bjpnew/screens/template/add_leader_viewmodel.dart';

import 'package:bjpnew/screens/template/templates_viewModel.dart';

import 'package:bjpnew/global/custom_toast.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';

// ignore: must_be_immutable
class AddLeaderPhotoView extends StatefulWidget {
  var data;
  var matchedData;
  BuildContext con;
  AddleaderViewModel viewModel;

  AddLeaderPhotoView(
      {super.key,
      required this.data,
      required this.matchedData,
      required this.con,
      required this.viewModel});

  @override
  State<AddLeaderPhotoView> createState() => _AddLeaderPhotoViewState();
}

class _AddLeaderPhotoViewState extends State<AddLeaderPhotoView> {
  int findImagePosition(var imageUrl) {
    print(imageUrl);

    return widget.matchedData.indexOf(imageUrl) +
        1; // +1 to make it 1-based index
  }

  String normalizeData(String data) {
    // Remove "File: " prefix if present
    if (data.startsWith('File: ')) {
      data = data.replaceFirst('File: ', '');
    }

    data = data.replaceAll("'", "");

    // Trim leading/trailing spaces
    data = data.trim();

    // Print for debugging purposes
    print(data);
    return data;
  }

  @override
  Widget build(BuildContext con) {
    return SingleChildScrollView(
        child: Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Add Leader Photo',
                  style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.green),
                ),
                Text(
                  '(Limit 6 Leaders)',
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey),
                ),
              ],
            ),
            SizedBox(
              width: MediaQuery.of(context).size.height * 0.02,
            ),
            Container(
              height: MediaQuery.of(context).size.height * 0.13,
              width: MediaQuery.of(context).size.width * 0.13,
              //  margin: EdgeInsets.all(  5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey, width: 3.0),
              ),
              child: IconButton(
                icon: Icon(Icons.add),
                onPressed: () async {
                  await widget.viewModel.addLogo();
                  showToast(context, "Photo has been added !");
                  // Navigator.pop(context);
                },
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.height * 0.02,
            ),
            SizedBox(
              height: 50,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    elevation: 0,
                    backgroundColor: Color.fromARGB(255, 231, 231, 231),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(13),
                    )),
                onPressed: () async {
                  await widget.viewModel.saveSelectedImages();
                  await widget.viewModel.saveImages();
                  showToast(context, 'Selections saved!');

                  Navigator.pop(context);
                },
                child: Text("Done",
                    style: TextStyle(
                        fontWeight: FontWeight.w800,
                        fontSize: 16,
                        color: Colors.black87,
                        fontFamily: 'Mukta')),
              ),
            )
          ],
        ),
        SizedBox(height: 16.0),
        widget.data == null
            ? SizedBox()
            : SizedBox(
                height: MediaQuery.of(context).size.height * 0.29,
                child: GridView.builder(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 3,
                      mainAxisSpacing: 2,
                    ),
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: widget.data.length,
                    itemBuilder: (BuildContext context, int index) {
                      var matchedPosition =
                          findImagePosition(widget.data[index]);
                      print(widget.viewModel.selectedImages);

                      String imagePath = widget.data[index];
                      bool isSelected = widget.viewModel.isSelected(imagePath);
                      return Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              widget.viewModel.toggleSelection(imagePath);
                            },
                            child: Container(
                              height: MediaQuery.of(context).size.height * 0.11,
                              width: MediaQuery.of(context).size.height * 0.11,
                              margin: EdgeInsets.only(right: 5),
                              decoration: BoxDecoration(
                                color: Colors.yellow,
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                  image: NetworkImage(imagePath),
                                  fit: BoxFit.cover,
                                ),
                                border: isSelected
                                    ? Border.all(
                                        color: Colors.green, width: 3.0)
                                    : null,
                              ),
                              child: Stack(
                                children: [
                                  isSelected
                                      ? Container(
                                          padding: EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: Colors.green),
                                          child: Icon(
                                            Icons.check,
                                            color: Colors.white,
                                            size: 20,
                                          ))
                                      : SizedBox()
                                ],
                              ),
                            ),
                          ),
                          matchedPosition == 0
                              ? SizedBox()
                              : Text("Position : $matchedPosition"),
                        ],
                      );
                    }
                    // },
                    ),
              ),
        SizedBox(
          height: 5,
        ),
        SizedBox(
            height: MediaQuery.of(context).size.height * 0.29,
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 3,
                mainAxisSpacing: 2,
              ),
              itemCount: widget.viewModel.uploadedLogos.length,
              itemBuilder: (BuildContext context, int index) {
                File imagePath = widget.viewModel.uploadedLogos[index];
                bool isSelected = widget.viewModel.isSelected(imagePath.path);

                return GestureDetector(
                  onTap: () {
                    widget.viewModel.toggleSelection(imagePath.path);
                  },
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.11,
                    width: MediaQuery.of(context).size.height * 0.11,
                    margin: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: FileImage(imagePath),
                        fit: BoxFit.cover,
                      ),
                      border: isSelected
                          ? Border.all(color: Colors.green, width: 3.0)
                          : null,
                    ),
                    child: Stack(
                      children: [
                        isSelected
                            ? Container(
                                padding: EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.green,
                                ),
                                child: Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              )
                            : SizedBox(),
                      ],
                    ),
                  ),
                );
              },
            ))
      ]),
    ));
  }
}
