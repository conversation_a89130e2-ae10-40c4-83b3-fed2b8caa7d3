import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bjpnew/utils/Singletons/prefs_singleton.dart';
import 'package:stacked/stacked.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class AddleaderViewModel extends BaseViewModel {
  List<String> _selectedImages = [];

  List<String> get selectedImages => _selectedImages;
  List<File> _uploadedLogos = [];

  List<File> get uploadedLogos => _uploadedLogos;

  AddleaderViewModel() {
    _loadSelectedImages();
    loadImages();
  }

  void toggleSelection(String imagePath) {
    if (_selectedImages.contains(imagePath)) {
      _selectedImages.remove(imagePath);
    } else {
      _selectedImages.add(imagePath);
    }
    notifyListeners(); // Ensure UI updates
    saveImages(); // Persist changes
  }

  bool isSelected(String imagePath) {
    return _selectedImages.contains(imagePath);
  }

  Future<void> _loadSelectedImages() async {
    final prefs = await SharedPreferences.getInstance();
    _selectedImages = prefs.getStringList('selectedImages') ?? [];
    notifyListeners();
  }

  Future<void> saveSelectedImages() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('selectedImages', _selectedImages);
    notifyListeners();
  }

//   Future<Map<String, dynamic>> fetchStateInfos() async {
//   final configUrl = Uri.parse(
//       'https://backend.designboxconsuting.com/poster/config/v1/config');
//   try {
//     final response = await http.get(configUrl);
//     if (response.statusCode == 200) {
//       await Prefs.instance.setString('config', response.body);
//       print("config");
//       print(response.body);

//       final prefs = await SharedPreferences.getInstance();
//       final String? token = prefs.getString('token');
//       final String? userId = prefs.getString('userId');
//       print(userId);
//       print(token);
//     } else {
//       // Handle error
//       print('Failed to load config: ${response.statusCode}');
//     }
//   } catch (e) {
//     // Handle network error
//     print('Failed to load config: $e');
//   }
//  final _prefs = await SharedPreferences.getInstance();
//   String? configJson = _prefs.getString('config');
//   if (configJson != null) {
//     Map<String, dynamic> config = jsonDecode(utf8.decode(configJson.codeUnits));
//     List<dynamic> stateInfos = config['stateInfos'];
//     String? stateName = _prefs.getString('CategorySelected');

//     final selectedStateInfo = stateInfos.firstWhere(
//       (info) => info["state"] == stateName,
//       orElse: () => null,
//     );

//     print(stateName);
//     print(selectedStateInfo?["leadersImgUrls"]);
//     List<String> images = selectedStateInfo?["leadersImgUrls"]?.map((img) => img.toString())?.toList() ?? [];

//     return {
//       "stateName": stateName,
//       "data": images
//     };
//   } else {
//     return {};
//   }
// }

  final ImagePicker _picker = ImagePicker();

//   Future<void> loadImages() async {
//   SharedPreferences prefs = await SharedPreferences.getInstance();
//   List<String>? logoPaths = prefs.getStringList('uploadedLogos');
//   print('Loaded logos: $logoPaths');
//   if (logoPaths != null) {
//     _uploadedLogos = logoPaths.map((path) => File(path)).toList();
//   }
//   notifyListeners(); // Ensure UI updates
// }

  Future<void> loadImages() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String>? logoPaths = prefs.getStringList('uploadedLogos');
    List<String>? selectedPaths = prefs.getStringList('selectedImages');

    if (logoPaths != null) {
      _uploadedLogos = logoPaths.map((path) => File(path)).toList();
    }
    if (selectedPaths != null) {
      _selectedImages = selectedPaths;
    }

    notifyListeners();
  }

  // Future<void> saveImages() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();

  //   // Save uploaded logos
  //   List<String> logoPaths = _uploadedLogos.map((file) => file.path).toList();
  //   await prefs.setStringList('uploadedLogos', logoPaths);

  //   // Save selected images
  //   await prefs.setStringList('selectedImages', _selectedImages);
  // }

//   Future<void> saveImages() async {
//   SharedPreferences prefs = await SharedPreferences.getInstance();
//   List<String> logoPaths = _uploadedLogos.map((file) => file.path).toList();
//     print('Saving logos: $logoPaths');
//   await prefs.setStringList('uploadedLogos', logoPaths);
//   await saveSelectedImages(); // Save selected images too
// }
  Future<void> saveImages() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> logoPaths = _uploadedLogos.map((file) => file.path).toList();
    await prefs.setStringList('uploadedLogos', logoPaths);
    await prefs.setStringList('selectedImages', _selectedImages);
  }

  Future<void> addLogo() async {
    final XFile? pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      File newFile = File(pickedFile.path);
      if (!_uploadedLogos.any((file) => file.path == newFile.path)) {
        _uploadedLogos.add(newFile);
      }

      if (!_selectedImages.contains(newFile.path)) {
        _selectedImages.add(newFile.path);
      }

      notifyListeners();
      await saveImages();
    }
  }
}
