import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bjpnew/global/CustomSecondaryButton.dart';
import 'package:bjpnew/global/customloader.dart';
import 'package:bjpnew/global/primary_button.dart';
import 'package:bjpnew/locator/app.locator.dart';
import 'package:bjpnew/screens/edit-profile-mvvm/edit_profile_viewmodel.dart';
import 'package:bjpnew/screens/home-mvvm/home_viewmodel.dart';
import 'package:bjpnew/screens/nameplate/first_name_design.dart';
import 'package:bjpnew/screens/nameplate/fourthNameplate.dart';
import 'package:bjpnew/screens/nameplate/secondnameplate.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplate.dart';
import 'package:bjpnew/screens/profile-mvvm/profile_viewmodel.dart';
import 'package:bjpnew/screens/template/edit_poster_button.dart';
import 'package:bjpnew/screens/template/imageViewModel.dart';
import 'package:bjpnew/screens/template/template_left_1.dart';
import 'package:bjpnew/screens/template/templates_viewModel.dart';
import 'package:bjpnew/services/download_share_image.dart';
import 'package:bjpnew/services/photo_background_removal.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:bjpnew/global/custom_toast.dart';

import 'package:bjpnew/screens/edit-profile-mvvm/edit_profile_view.dart';
import 'package:path_provider/path_provider.dart';

import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import 'package:transparent_image/transparent_image.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;

class BirthdayTemplate extends StatefulWidget {
  final String imageUrl;
  final bool premiumStatus;
  final bool isPoster;
  final bool showCTA;
  final VoidCallback? onImageAdded;
  final bool isHttp;
  final bool isHOme;
  final bool? isBottomsheet;
//   final VoidCallback? onLeaderImageAdded;
//   final VoidCallback? onnamePlateLoaded;
//  final VoidCallback?  onProfileDetailsChange;

  const BirthdayTemplate({
    Key? key,
    required this.isHttp,
    required this.isHOme,
    required this.imageUrl,
    required this.isPoster,
    required this.premiumStatus,
    required this.showCTA,
    // this.onLeaderImageAdded,
    this.onImageAdded,
    this.isBottomsheet,
    // this.onnamePlateLoaded,
    // this.onProfileDetailsChange
  }) : super(key: key);

  @override
  State<BirthdayTemplate> createState() => _BirthdayTemplateState();
}

class _BirthdayTemplateState extends State<BirthdayTemplate> {
  final ScreenshotController _controller = ScreenshotController();
  final ScreenshotController _Stripcontroller = ScreenshotController();
  final ScreenshotController _userscreenshotController = ScreenshotController();
  // final ImagePicker _picker = ImagePicker();
  final GlobalKey _stripKey = GlobalKey(); // Add GlobalKey for the strip
  bool _isLoading = false;
  // List<File> _uploadedLogos = [];
  TemplatesViewModel viewm = TemplatesViewModel();
  ProfileViewModel profileViewModel = ProfileViewModel();
  EditProfileViewModel editprofileViewModel = EditProfileViewModel();

  void setLoading(bool isLoading) {
    setState(() {
      _isLoading = isLoading;
    });
  }

  @override
  void initState() {
    super.initState();
    // _loadUploadedLogos();
    _getImageData();
    _pickBottomColor();
  }

  var image = [];

  var selectedIndexFornamePlate;

  _getImageData() async {
    var data = await viewm.fetchStateInfos();
    image = data['data'];
    print(image);

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    int check = prefs.getInt('count') ?? 0;

    String checkstate = prefs.getString("checkstate") ?? '';
    // selectedIndexFornamePlate = await prefs.getInt("selectedIndex");
    // print('select $selectedIndexFornamePlate');

    if (check == 0 || data['stateName'] != checkstate) {
      await prefs.setString('checkstate', data['stateName']);

      await prefs.setInt('count', 1);

      List<String> stringList =
          image.map((element) => element.toString()).toList();

      await prefs.setStringList('my_string_list', stringList);
    }
  }

  getnameplatedata() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    selectedIndexFornamePlate = await prefs.getInt("selectedIndex");
  }

  void _rebuildUI() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    getnameplatedata();

    return Stack(
      children: [
        ViewModelBuilder<TemplatesViewModel>.reactive(
            viewModelBuilder: () => locator<TemplatesViewModel>(),
            onModelReady: (model) => model.loadStoredName(),
            onViewModelReady: (viewModel) async {
              await viewModel.initialize();
            },
            builder: (context, viewModel, child) => RefreshIndicator(
                onRefresh: () => viewModel.getImageList(),
                child: politicalTemplate_id_left1(viewModel, _rebuildUI))),
        if (_isLoading)
          CustomLoader(
            message: 'Processing Video..',
          ),
      ],
    );
  }

  bool isValidMediaUrl(bool isPoster, String url) {
    if (isPoster) {
      return url.endsWith('.jpg') ||
          url.endsWith('.jpeg') ||
          url.endsWith('.png') ||
          url.endsWith('.gif') ||
          url.endsWith('.bmp') ||
          url.endsWith('.webp') ||
          url.contains('images') ||
          url.endsWith('.mp4');
    } else {
      return url.endsWith('.mp4') ||
          url.endsWith('.avi') ||
          url.endsWith('.mov') ||
          url.endsWith('.wmv') ||
          url.endsWith('.flv') ||
          url.endsWith('.mkv') ||
          url.contains('video');
    }
  }

  GlobalKey imageKey = GlobalKey();
  Color bottomColor = Colors.grey;

  Future<void> _pickBottomColor() async {
    try {
      // Fetch the image data from the URL
      final http.Response response = await http.get(Uri.parse(widget.imageUrl));
      final Uint8List imageData = response.bodyBytes;

      // Decode the image data to get image dimensions and pixel data
      final img.Image? decodedImage = img.decodeImage(imageData);

      if (decodedImage != null) {
        // Get the bottom-right pixel
        final img.Pixel bottomRightPixel = decodedImage.getPixel(
          decodedImage.width - 1, // x-coordinate (rightmost pixel)
          decodedImage.height - 1, // y-coordinate (bottommost pixel)
        );

        // Convert the Pixel object to a Color
        setState(() {
          bottomColor = Color.fromARGB(
            bottomRightPixel.a.toInt(), // Convert num to int
            bottomRightPixel.r.toInt(), // Convert num to int
            bottomRightPixel.g.toInt(), // Convert num to int
            bottomRightPixel.b.toInt(),
          );
        });
      }
    } catch (e) {
      print("Error picking bottom-right color: $e");
    }
  }

  bool isVideoFile(String imageUrl) {
    print(imageUrl);
    return imageUrl.endsWith('.mp4') ||
        imageUrl.endsWith('.mov') ||
        imageUrl.endsWith('.avi');
  }

  var matchedData = [];
  Widget politicalTemplate_id_left1(
      TemplatesViewModel viewModel, VoidCallback onloaded) {
    return !isValidMediaUrl(widget.isPoster, widget.imageUrl)
        ? SizedBox(
            height: 0,
            width: 0,
          )
        // ? Container(
        //     color: Colors.black,
        //   )
        : Container(
            decoration: BoxDecoration(color: Colors.white),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.height * 0.01,
                      vertical: MediaQuery.of(context).size.height * 0.005),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                    color: Color.fromARGB(255, 188, 186, 186),
                  ),
                  child: Column(
                    children: [
                      Screenshot(
                        controller: _controller,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.black, width: 2),
                            color: Colors.white,
                          ),
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Column(
                                children: [
                                  Stack(
                                    children: [
                                      ClipRRect(
                                          clipBehavior: Clip.hardEdge,
                                          child:
                                              // widget.isPoster
                                              //     ?   widget.isHttp == true
                                              //             ? FadeInImage.memoryNetwork(
                                              //                 image: widget.imageUrl,
                                              //                 placeholder:
                                              //                     kTransparentImage,
                                              //                 fit: BoxFit.cover,
                                              //               )
                                              //             :
                                              Container(
                                            decoration: BoxDecoration(
                                                image: DecorationImage(
                                                    image: AssetImage(
                                                        widget.imageUrl))),
                                          )
                                          // : Container(),
                                          ),
                                      Padding(
                                        padding: const EdgeInsets.only(top: 5),
                                        child: FutureBuilder<List<dynamic>>(
                                          future: viewModel.getImageList(),
                                          builder: (context, snapshot) {
                                            if (snapshot.connectionState ==
                                                ConnectionState.waiting) {
                                              return Text("");
                                              // Center(
                                              //     child:
                                              //         CircularProgressIndicator());
                                            } else if (snapshot.hasError) {
                                              return Center(
                                                  child: Text(
                                                      'Error: ${snapshot.error}'));
                                            } else if (!snapshot.hasData ||
                                                snapshot.data!.isEmpty) {
                                              return Center(child: Text(''));
                                            } else {
                                              List<dynamic> imageData =
                                                  snapshot.data!;
                                              matchedData = imageData;

                                              // Separate first image
                                              var firstImage = imageData.first;
                                              var remainingImages =
                                                  imageData.sublist(1);
                                              // print("full $imageData");
                                              // print("half $firstImage");
                                              // print("other $remainingImages");

                                              return Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  // Display first image separately
                                                  SizedBox(
                                                    height:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.08,
                                                    child: Container(
                                                      width:
                                                          MediaQuery.of(context)
                                                                  .size
                                                                  .width *
                                                              0.133,
                                                      margin: EdgeInsets.only(
                                                          left: 3),
                                                      decoration: BoxDecoration(
                                                        // color: Colors.yellow,
                                                        border: Border.all(
                                                            color:
                                                                Colors.green),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(5.0),
                                                        image: DecorationImage(
                                                          image: firstImage
                                                                  .startsWith(
                                                                      'http')
                                                              ? NetworkImage(
                                                                  firstImage)
                                                              : FileImage(File(
                                                                      firstImage))
                                                                  as ImageProvider,
                                                          fit: BoxFit.cover,
                                                        ),
                                                      ),
                                                    ),
                                                  ),

                                                  // ListView for remaining images
                                                  Container(
                                                    height: getHeight(context,
                                                        remainingImages),
                                                    padding: EdgeInsets.zero,

                                                    //  remainingImages.length < 5 ?  MediaQuery.of(context).size.height * 0.12:remainingImages.length < 8? MediaQuery.of(context).size.height * 0.11:MediaQuery.of(context).size.height * 0.1  ,
                                                    width:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .width *
                                                            0.7,
                                                    child: ListView.builder(
                                                      itemCount: remainingImages
                                                          .length,
                                                      scrollDirection:
                                                          Axis.horizontal,
                                                      physics:
                                                          NeverScrollableScrollPhysics(),
                                                      itemBuilder:
                                                          (context, index) {
                                                        return Container(
                                                          padding:
                                                              EdgeInsets.zero,
                                                          width: getWidth(
                                                              context,
                                                              remainingImages),
                                                          margin: EdgeInsets.only(
                                                              left: remainingImages
                                                                          .length <
                                                                      8
                                                                  ? 5
                                                                  : 1),
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color: Colors
                                                                    .green),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        5.0),
                                                            image:
                                                                DecorationImage(
                                                              image: remainingImages[
                                                                          index]
                                                                      .startsWith(
                                                                          'http')
                                                                  ? NetworkImage(
                                                                      remainingImages[
                                                                          index])
                                                                  : FileImage(File(
                                                                          remainingImages[
                                                                              index]))
                                                                      as ImageProvider,
                                                              fit: BoxFit.cover,
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                  )
                                                ],
                                              );
                                            }
                                          },
                                        ),
                                      )
                                    ],
                                  ),
                                  //                          Container(
                                  //   width: 100,
                                  //   height: 50,
                                  //   color: bottomColor,
                                  // ),
                                  viewModel.getSelectedWidget(
                                      _Stripcontroller, _stripKey, bottomColor)
                                ],
                              ),
                              Positioned(
                                  right: (MediaQuery.of(context).size.width) *
                                      -.015,
                                  bottom:
                                      viewModel.getHeightForNamePlate(context),
                                  child: Screenshot(
                                    controller: _userscreenshotController,
                                    child: GestureDetector(
                                      onTap: () async {
                                        showCustomDialog(context, viewModel);
                                      },
                                      child: FutureBuilder(
                                          future: viewModel.leaderImage(),
                                          builder: (context, snapshot) {
                                            //  print(snapshot.data);

                                            return Container(
                                                //  color: Colors.red,
                                                //  padding:EdgeInsets.only(left: MediaQuery.of(context).size.width*0.076),
                                                alignment:
                                                    Alignment.bottomRight,
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.2 *
                                                    viewModel.imageSizeFactor,
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.37 *
                                                    viewModel.imageSizeFactor,
                                                // height:
                                                //     selectedIndexFornamePlate ==
                                                //             2
                                                //         ? MediaQuery.of(context)
                                                //                 .size
                                                //                 .height *
                                                //             .26
                                                //         : MediaQuery.of(context)
                                                //                 .size
                                                //                 .height *
                                                //             .2,
                                                // width:
                                                //     selectedIndexFornamePlate ==
                                                //             2
                                                //         ? MediaQuery.of(context)
                                                //                 .size
                                                //                 .width *
                                                //             0.34
                                                //         : MediaQuery.of(context)
                                                //                 .size
                                                //                 .width *

                                                //  0.37,
                                                child: snapshot.data != null
                                                    ? ClipRect(
                                                        child: Image(
                                                          image: (snapshot.data
                                                                  as Image)
                                                              .image,
                                                          fit: BoxFit
                                                              .cover, // Ensures the image covers both height and width
                                                          width:
                                                              double.infinity,
                                                          height:
                                                              double.infinity,
                                                        ),
                                                      )
                                                    : Container());
                                          }),
                                    ),
                                  )),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                widget.showCTA ? returnCTA(viewModel, _rebuildUI) : SizedBox(),
              ],
            ),
          );
  }

  Future<void> shareAssetImage() async {
    // Step 1: Load the asset image into memory
    final byteData = await rootBundle.load('Asset/Images/share.jpeg');

    // Step 2: Write the image to a temporary file
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/share_image.jpeg');
    await file.writeAsBytes(byteData.buffer.asUint8List());

    // Step 3: Get the share link from SharedPreferences
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var textlist = prefs.getString('shareLink');

    // Step 4: Share the file
    await Share.shareXFiles(
      [XFile(file.path)], // Share the image file
      text:
          'अपने फोटो के साथ अपनी पार्टी का दैनिक पोस्टर पायें अभी ऐप डाउनलोड करें: $textlist', // Add the text
    );
  }

  void showCustomDialog(BuildContext context, TemplatesViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          title: Center(child: Text('Choose One Option')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  var changedImage = await viewModel.userImageChange(context);
                  if (changedImage != null) {
                    await removeBackground(changedImage, false);
                    widget.onImageAdded?.call();
                  }
                  print("Change User Photo Pressed");
                },
                child: Text('Change User Photo'),
              ),
              SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  // Close the current dialog or screen
                  Navigator.pop(context);

                  // Use Future.delayed to wait for the pop to complete before showing the bottom sheet

                  _showImageSizeBottomSheet(context, viewModel);
                },
                child: Text('Change User Pic Size'),
              ),
            ],
          ),
        );
      },
    );
  }

  void showfontsize(BuildContext context, TemplatesViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          title: Center(child: Text('Choose One Option')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  DialogHelper.showTextSizeDialog(context, viewModel, 'text1');
                },
                child: Text('Change Name Font Size'),
              ),
              SizedBox(height: 10),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  DialogHelper.showTextSizeDialog(context, viewModel, 'text2');
                },
                child: Text('change Position Font Size'),
              ),
            ],
          ),
        );
      },
    );
  }

  double getHeight(BuildContext context, List<dynamic> remainingImages) {
    if (remainingImages.length > 7) {
      return MediaQuery.of(context).size.height * 0.05;
    } else if (remainingImages.length > 4) {
      return MediaQuery.of(context).size.height * 0.06;
    } else {
      return MediaQuery.of(context).size.height * 0.075;
    }
  }

  double getWidth(BuildContext context, List<dynamic> remainingImages) {
    if (remainingImages.length > 7) {
      return MediaQuery.of(context).size.width * 0.07;
    } else if (remainingImages.length > 4) {
      return MediaQuery.of(context).size.width * 0.08;
    } else {
      return MediaQuery.of(context).size.width * 0.125;
    }
  }

  Widget returnUploadPhoto(
      TemplatesViewModel viewModel, VoidCallback onloaded) {
    return CustomSecondaryButton(
      showIcon: false,
      leadingIcon: 'Asset/Icons/Download-Icon.svg',
      bgcolor: Colors.grey,
      onPressed: () async {
        _showUpEditPoster(context, viewModel, onloaded);
      },
      buttonText: "Edit Poster - Profile, Design, Leader photo",
      buttonColor: Colors.black,
    );
  }

  Future<void> _showUpEditPoster(BuildContext context,
      TemplatesViewModel viewModel, VoidCallback onloaded) async {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.35,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    EditButtons(
                        width: MediaQuery.of(context).size.width * 0.5,
                        onTap: () async {
                          _showUploadLogoDialog(
                              context, image, viewModel, onloaded);
                          Navigator.pop(context);
                        },
                        text: 'Add More Leader Photo'),
                    SizedBox(
                      width: 10,
                    ),
                    EditButtons(
                        width: MediaQuery.of(context).size.width * 0.4,
                        onTap: () async {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => EditProfileView(
                                      isHome: widget.isHOme,
                                      onDetailsSaved: () {
                                        setState(() {});
                                      },
                                    )),
                          );
                        },
                        text: 'Edit Profile'),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    EditButtons(
                        width: MediaQuery.of(context).size.width * 0.5,
                        onTap: () {
                          _showNamePlateDesigns(context, viewModel, onloaded);

                          Navigator.pop(context);
                        },
                        text: 'Change Name Plate'),
                    SizedBox(
                      width: 10,
                    ),
                    EditButtons(
                        width: MediaQuery.of(context).size.width * 0.4,
                        onTap: () async {
                          Navigator.pop(context);
                          await profileImageGrid(context, profileViewModel);
                          widget.onImageAdded!.call();
                        },
                        text: 'Change Your Photo')
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    EditButtons(
                        width: MediaQuery.of(context).size.width * 0.4,
                        onTap: () async {
                          _showFontTypeDesigns(context, viewModel, onloaded);
                          Navigator.pop(context);
                        },
                        text: 'Change Font Type'),
                    SizedBox(
                      width: 10,
                    ),
                    EditButtons(
                        width: MediaQuery.of(context).size.width * 0.4,
                        onTap: () async {
                          Navigator.pop(context);
                          _showImageSizeBottomSheet(context, viewModel);
                          widget.onImageAdded!.call();
                        },
                        text: 'Change Image size')
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                EditButtons(
                    width: MediaQuery.of(context).size.width * 0.4,
                    onTap: () async {
                      Navigator.pop(context);
                      showfontsize(context, viewModel);

                      widget.onImageAdded!.call();
                    },
                    text: 'Change Font size')
              ],
            ),
          );
        });
  }

  Future<void> _showFontTypeDesigns(BuildContext context,
      TemplatesViewModel viewModel, VoidCallback onloaded) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final List<String> fontFamilies = [
      'AnekTelugu',
      'Anek',
      'Baloo2',
      'Mukta',
      'Arya',
      'Work-Sans'
    ];

    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Container(
            padding: EdgeInsets.all(16.0),
            height: 200,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // ElevatedButton(onPressed: (){
                  //    viewModel.updateFontFamily(font);
                  //    onloaded.call() ;
                  //   Navigator.pop(context);
                  // }, child: Text("Save")),
                  DropdownButton<String>(
                    value: viewModel.selectedFontFamily,
                    items: fontFamilies.map((font) {
                      return DropdownMenuItem<String>(
                        value: font,
                        child: Text(
                          font, // Display font name in its own style
                          style: TextStyle(fontFamily: font, fontSize: 20),
                        ),
                      );
                    }).toList(),
                    onChanged: (newFont) {
                      if (newFont != null) {
                        viewModel.updateFontFamily(newFont);
                        Navigator.pop(context);
                      }
                    },
                  ),
                ],
              ),
            ),
          );
        });
  }

  Future<void> profileImageGrid(
      BuildContext context, ProfileViewModel profileViewModel) async {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                //  padding: const EdgeInsets.symmetric(  vertical: 16 , ),
                // alignment: Alignment.topLeft,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Saved Profile Pictures',
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Work-Sans',
                        fontWeight: FontWeight.w500,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(width: 10),
                    ElevatedButton(
                      onPressed: () async {
                        await profileViewModel.userImageChange(context);
                        widget.onImageAdded!.call();
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        // onPrimary: Colors.white,
                      ),
                      child: Text(
                        "Change Photo",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: FutureBuilder(
                  future: UserImage().returnListFileAddress(),
                  builder: (BuildContext context, AsyncSnapshot snapshot) {
                    if (snapshot.hasData && snapshot.data != null) {
                      List<File> returnedUserAddress = snapshot.data;
                      returnedUserAddress =
                          returnedUserAddress.reversed.toList();
                      return Container(
                        height: (MediaQuery.of(context).size.height * 0.25) *
                            snapshot.data.length,
                        child: GridView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          itemCount: snapshot.data.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          itemBuilder: (BuildContext GridContext, int index) {
                            return GestureDetector(
                              onTap: () {
                                profileViewModel.prefs
                                    .setInt('SelectedID', index);
                                widget.onImageAdded!.call();
                                Navigator.pop(context);
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text('Photo Updated'),
                                  duration: Duration(seconds: 1),
                                ));
                                // showCustomModalBottomSheet(
                                //   index,
                                //   context,
                                //   // profileviewModel,
                                // );
                              },
                              child: Stack(
                                children: [
                                  Container(
                                    height:
                                        (MediaQuery.of(context).size.height *
                                            0.25),
                                    width: (MediaQuery.of(context).size.height *
                                        0.25),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(
                                        color:
                                            profileViewModel.selectedID == index
                                                ? Colors.green
                                                : Colors.grey,
                                        width: 3,
                                        strokeAlign:
                                            BorderSide.strokeAlignOutside,
                                      ),
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    clipBehavior: Clip.hardEdge,
                                    child: Image.file(
                                      returnedUserAddress[index],
                                      fit: BoxFit.contain,
                                      height:
                                          (MediaQuery.of(context).size.height *
                                              0.17),
                                      width:
                                          (MediaQuery.of(context).size.width *
                                              0.17),
                                      alignment: Alignment.topCenter,
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                        color:
                                            profileViewModel.selectedID == index
                                                ? Colors.green
                                                : Colors.grey,
                                        shape: BoxShape.circle),
                                    child: Icon(
                                      Icons.check,
                                      color: Colors.white,
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        ),
                      );
                    } else {
                      return SizedBox();
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  int findImagePosition(var imageUrl) {
    print(imageUrl);

    print(matchedData.indexOf(imageUrl) + 1);
    return matchedData.indexOf(imageUrl) + 1; // +1 to make it 1-based index
  }

  String normalizeData(String data) {
    // Remove "File: " prefix if present
    if (data.startsWith('File: ')) {
      data = data.replaceFirst('File: ', '');
    }

    data = data.replaceAll("'", "");

    // Trim leading/trailing spaces
    data = data.trim();

    // Print for debugging purposes

    return data;
  }

  Future<void> _showUploadLogoDialog(BuildContext context, var data,
      TemplatesViewModel viewModel, VoidCallback onloaded) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var imageDatas = await prefs.getStringList('my_string_list');

    print(matchedData);
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16.0),
          height: 350,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          'Add Leader Photo',
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green),
                        ),
                        Text(
                          '(Limit 10 Leaders)',
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey),
                        ),
                      ],
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.height * 0.02,
                    ),
                    Container(
                      height: MediaQuery.of(context).size.height * 0.13,
                      width: MediaQuery.of(context).size.width * 0.13,
                      //  margin: EdgeInsets.all(  5),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.grey, width: 3.0),
                      ),
                      child: IconButton(
                        icon: Icon(Icons.add),
                        onPressed: () async {
                          // if(matchedData.length !< 10){
                          await viewModel.addLogo();
//                            showToast(context, "Photo has been added !");
//                           }else{
//  showToast(context, "LeaderLogo Limit 10 Completed!");
//                           }

                          Navigator.pop(context);
                        },
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.height * 0.02,
                    ),
                    SizedBox(
                      height: 50,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            elevation: 0,
                            backgroundColor: Color.fromARGB(255, 231, 231, 231),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(13),
                            )),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text("Done",
                            style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontSize: 16,
                                color: Colors.black87,
                                fontFamily: 'Mukta')),
                      ),
                    )
                  ],
                ),
                SizedBox(height: 16.0),
                data == null
                    ? SizedBox()
                    : SizedBox(
                        height: MediaQuery.of(context).size.height * 0.29,
                        child: GridView.builder(
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 3,
                              mainAxisSpacing: 2,
                            ),
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: data.length,
                            itemBuilder: (BuildContext context, int index) {
                              print(data);
                              var matchedPosition =
                                  findImagePosition(data[index]);
//                             if (index == 0) {
//                               return Column(
//                                 children: [

                              //                 SizedBox(height: 1,)
                              //   ],
                              // );
//                             } else {
                              File imagePath = File(data[index]);
                              // bool  isSelected =   viewModel.isSelectedlocator(imagePath);
                              // bool isSelected = imageDatas!.contains(data[index]);
                              return Column(
                                children: [
                                  FutureBuilder<bool>(
                                      future: viewModel.isSelectedlocator(
                                          imagePath), // Update to use Future<bool>
                                      builder: (context, snapshot) {
                                        bool isSelected =
                                            snapshot.data ?? false;

                                        return GestureDetector(
                                            onTap: () async {
                                              if (isSelected) {
                                                viewModel
                                                    .removeImage(data[index]);
                                                widget.onImageAdded?.call();
                                                onloaded();
                                                showToast(context,
                                                    "Photo has been removed !");
                                                Navigator.pop(context);
                                              } else {
                                                viewModel.addImage(data[index]);
                                                widget.onImageAdded?.call();
                                                onloaded();
                                                showToast(context,
                                                    "Photo has been added !");
                                                Navigator.pop(context);
                                              }
                                              widget.onImageAdded?.call();
                                              viewModel.notifyListeners();
                                            },
                                            child: Container(
                                                height: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.11,
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .height *
                                                    0.11,
                                                margin:
                                                    EdgeInsets.only(right: 5),
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  image: DecorationImage(
                                                    image: FileImage(imagePath),
                                                    //  NetworkImage(imagePath),
                                                    fit: BoxFit.cover,
                                                  ),
                                                  border: isSelected
                                                      ? Border.all(
                                                          color: Colors.green,
                                                          width: 3.0)
                                                      : null,
                                                ),
                                                child: Stack(
                                                  children: [
                                                    isSelected
                                                        ? Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    2),
                                                            decoration:
                                                                BoxDecoration(
                                                                    shape: BoxShape
                                                                        .circle,
                                                                    color: Colors
                                                                        .green),
                                                            child: Icon(
                                                              Icons.check,
                                                              color:
                                                                  Colors.white,
                                                              size: 20,
                                                            ))
                                                        : SizedBox(),
                                                  ],
                                                )));
                                      }),
                                  matchedPosition == 0
                                      ? SizedBox()
                                      : Text("Position : ${matchedPosition}")
                                ],
                              );
                            }
                            // },
                            ),
                      ),
                SizedBox(
                  height: 5,
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.29,
                  child: GridView.builder(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 3,
                      mainAxisSpacing: 2,
                    ),
                    itemCount: viewModel.uploadedLogos.length,
                    itemBuilder: (BuildContext context, int index) {
                      File imagePath = viewModel.uploadedLogos[index];
                      //  bool isSelected = viewModel.isSelectedlocator(imagePath);
                      // bool isSelected = imageDatas!.contains(imagePath.path);
                      String normalizedData1 =
                          normalizeData(imagePath.toString());
                      //  print(matchedData);
                      //  print(matchedData[4]);
                      //  print(normalizedData1);
                      //  print(matchedData[4]==normalizedData1);
                      var matchedPosition = findImagePosition(normalizedData1);

                      return Column(
                        children: [
                          FutureBuilder<bool>(
                              future: viewModel.isSelectedlocator(
                                  imagePath), // Update to use Future<bool>
                              builder: (context, snapshot) {
                                bool isSelected = snapshot.data ?? false;

                                return GestureDetector(
                                  onTap: () async {
                                    if (isSelected) {
                                      viewModel.removeImage(imagePath);
                                      widget.onImageAdded?.call();
                                      onloaded();
                                      showToast(
                                          context, "Photo has been removed !");
                                      Navigator.pop(context);
                                    } else {
                                      viewModel.addImage(imagePath);
                                      widget.onImageAdded?.call();
                                      onloaded();
                                      showToast(
                                          context, "Photo has been added !");
                                      Navigator.pop(context);
                                    }

                                    widget.onImageAdded?.call();
                                  },
                                  child: Container(
                                    height: MediaQuery.of(context).size.height *
                                        0.11,
                                    width: MediaQuery.of(context).size.height *
                                        0.11,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                        image: FileImage(
                                            viewModel.uploadedLogos[index]),
                                        fit: BoxFit.cover,
                                      ),
                                      border: isSelected
                                          ? Border.all(
                                              color: Colors.green, width: 3.0)
                                          : null,
                                    ),
                                    child: Stack(
                                      children: [
                                        isSelected
                                            ? Container(
                                                padding: EdgeInsets.all(2),
                                                decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: Colors.green),
                                                child: Icon(
                                                  Icons.check,
                                                  color: Colors.white,
                                                  size: 20,
                                                ))
                                            : SizedBox()
                                      ],
                                    ),
                                  ),
                                );
                              }),
                          matchedPosition == 0
                              ? SizedBox()
                              : Text("Position : ${matchedPosition}")
                        ],
                      );
                    },
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _showNamePlateDesigns(BuildContext context,
      TemplatesViewModel viewModel, VoidCallback onloaded) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: 10,
              ),
              Text(
                "Select Another Name Plate Design",
                style: TextStyle(fontSize: 20),
              ),
              Container(
                padding: EdgeInsets.all(16.0),
                height: 1000,
                child: ListView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: viewModel.images.length,
                  itemBuilder: (context, index) {
                    bool isSelected = viewModel.selectedIndex == index;

                    return GestureDetector(
                      onTap: () {
                        viewModel.selectIndex(index);
                        // _updateNamePlateData();
                        widget.onImageAdded!.call();
                        Navigator.pop(context);
                      },
                      child: Container(
                        margin: EdgeInsets.all(8.0),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: isSelected ? Colors.green : Colors.grey,
                            width: 2.0,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text("NamePlate Design ${index + 1}"),
                            Stack(
                              children: [
                                Image.asset(viewModel.images[index]),
                                isSelected
                                    ? Positioned(
                                        top: 8,
                                        right: 8,
                                        child: Icon(
                                          Icons.check_circle,
                                          color: Colors.green,
                                        ),
                                      )
                                    : Positioned(
                                        top: 8,
                                        right: 8,
                                        child: Icon(
                                          Icons.check_circle,
                                          color: Colors.grey,
                                        ),
                                      ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showImageSizeBottomSheet(
      BuildContext context, TemplatesViewModel viewModel) {
    // Get initial slider value
    double initialSliderValue = (viewModel.imageSizeFactor - 1.0) * 100;
    print(initialSliderValue);
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Container(
                padding: EdgeInsets.all(16),
                height: 200,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('Adjust Image Size', style: TextStyle(fontSize: 18)),
                    SizedBox(height: 10),

                    // Slider for both width and height adjustment
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('-30%', style: TextStyle(fontSize: 16)),
                        Expanded(
                          child: Slider(
                            value: initialSliderValue.clamp(-30.0, 30.0),
                            min: -30,
                            max: 29.9,
                            divisions: 12, // 10% increments
                            label: '${initialSliderValue.round()}%',
                            onChanged: (newValue) {
                              setState(() {
                                initialSliderValue = newValue;
                              });
                            },
                            onChangeEnd: (newValue) {
                              viewModel
                                  .changeImageSize(newValue.clamp(-30.0, 30.0));
                            },
                          ),
                        ),
                        Text('+30%', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ],
                ),
              );
            },
          );
        });
  }

  Widget returnCTA(TemplatesViewModel viewModel, VoidCallback onloaded) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 14),
      child: LayoutBuilder(
        builder: (context, constraints) {
          bool isWideScreen = constraints.maxWidth > 600;
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    flex: 3,
                    child: PrimaryButton(
                      iconPath: 'Asset/Icons/Whatsapp-Icon.svg',
                      isEnabled: true,
                      isLoading: false,
                      onTap: () async {
                        await viewModel.conditionalButtonClick1(
                          controller:
                              widget.isPoster ? _controller : _Stripcontroller,
                          context: context,
                          imageUrl: widget.imageUrl,
                          isTestUser: false,
                          premiumStatus: widget.premiumStatus,
                          isPoster: widget.isPoster,
                          setLoading: setLoading,
                          userController: _userscreenshotController,
                        );
                      },
                      height: 48,
                      color: Colors.green,
                      label: "| Share With Your Photo",
                    ),
                  ),
                  if (isWideScreen) SizedBox(width: 2),
                  Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10, right: 10),
                      child: IconButton(
                          onPressed: () async {
                            showContactUsDialog(context, viewModel);
                          },
                          icon: Icon(
                            Icons.download,
                            color: Colors.white,
                            size: 25,
                          ),
                          // label: Text(""),
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ))),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(child: returnUploadPhoto(viewModel, onloaded))
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  void showContactUsDialog(BuildContext context, TemplatesViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Poster Share or Download',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 30),
              ElevatedButton.icon(
                icon: Icon(Icons.share),
                label: Text('Share Without Photo'),
                onPressed: () async {
                  if (widget.premiumStatus) {
                    await DownloadShareImage(controller: _controller)
                        .downloadPremiumScreenshot();
                  } else {
                    await DownloadShareImage()
                        .nonPremiumShare(imageUrl: widget.imageUrl);
                  }
                },
              ),
              ElevatedButton.icon(
                icon: Icon(Icons.download),
                label: Text('Download Poster'),
                onPressed: () async {
                  await viewModel.conditionalButtonClick1(
                    controller:
                        widget.isPoster ? _controller : _Stripcontroller,
                    context: context,
                    imageUrl: widget.imageUrl,
                    premiumStatus: widget.premiumStatus,
                    isTestUser: false,
                    isPoster: widget.isPoster,
                    setLoading: setLoading,
                    userController: _userscreenshotController,
                  );
                  // Implement email functionality here
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future removeBackground(XFile? image, bool? leader) {
    ThemeData themeData = Theme.of(context);
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return StreamBuilder(
              stream: PhotoBackgroundRemoval().executeEverything(image),
              builder: (context, snapshot) {
                print(snapshot.data);
                return AlertDialog(
                  backgroundColor: Colors.white,
                  title: Text(
                    'Removing background, please wait..',
                    textAlign: TextAlign.center,
                  ),
                  content: SingleChildScrollView(
                    child: ListBody(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Container(width: 300, child: snapshot.data),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    snapshot.connectionState == ConnectionState.done
                        ? PrimaryButton(
                            isEnabled: true,
                            isLoading: false,
                            onTap: () {
                              Navigator.pop(context);
                            },
                            label: 'Add Image',
                            color: themeData.colorScheme.primary,
                          )
                        : Center(child: CircularProgressIndicator()),
                  ],
                );
              });
        });
  }
}
