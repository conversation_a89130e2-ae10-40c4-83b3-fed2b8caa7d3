import 'package:flutter/material.dart';

class EditButtons extends StatelessWidget {
  Function()? onTap;
  String text;
  double? width;
  EditButtons({super.key, required this.onTap, required this.text, this.width});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.black.withAlpha(120),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
              color: Colors.white,
              fontSize: MediaQuery.of(context).size.height * 0.02,
              fontFamily: 'Mukta'),
        ),
      ),
    );
  }
}
