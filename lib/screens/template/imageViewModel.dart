// viewmodels/image_size_viewmodel.dart
import 'package:bjpnew/locator/app.locator.dart';
import 'package:bjpnew/services/imagesizeservice.dart';
import 'package:stacked/stacked.dart';

class ImageSizeViewModel extends BaseViewModel {
  final ImageSizeService _imageSizeService = locator<ImageSizeService>();

  bool _isLoading = false;

  bool get isLoading => _isLoading;
  double get imageSizeFactor => _imageSizeService.imageSizeFactor;

  ImageSizeViewModel() {
    _loadImageSizeFactor();
  }

  Future<void> _loadImageSizeFactor() async {
    setBusy(true);
    await _imageSizeService.loadImageSizeFactor();
    setBusy(false);
    notifyListeners();
  }

  Future<void> changeImageSize(double factor) async {
    setBusy(true);
    await _imageSizeService.changeImageSize(factor);
    setBusy(false);
    notifyListeners();
  }
}
