// import 'package:flutter/material.dart';
// import 'package:video_player/video_player.dart';

// class TemplateVideo extends StatefulWidget {
//   final String videoUrl;
//   final bool premiumStatus;
//   final bool showCTA;
//   final VoidCallback? onVideoAdded;

//   TemplateVideo({
//     Key? key,
//     required this.videoUrl,
//     required this.premiumStatus,
//     required this.showCTA,
//     this.onVideoAdded,
//   }) : super(key: key);

//   @override
//   _TemplateVideoState createState() => _TemplateVideoState();
// }

// class _TemplateVideoState extends State<TemplateVideo> {
//   late VideoPlayerController _controller;

//   @override
//   void initState() {
//     super.initState();
//     _controller = VideoPlayerController.network(widget.videoUrl)
//       ..initialize().then((_) {
//         setState(() {});
//       });
//     _controller.play();
//     _controller.setLooping(true);
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: EdgeInsets.all(16),
//       color: Colors.pink,
//       child: Column(
//         children: [
//           _controller.value.isInitialized
//               ? AspectRatio(
//                   aspectRatio: _controller.value.aspectRatio,
//                   child: VideoPlayer(_controller),
//                 )
//               : Center(child: CircularProgressIndicator()),
//           SizedBox(height: 8),
//         ]
//       ),
//     );
//   }}

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class TemplateVideo extends StatefulWidget {
  final String videoUrl;
  final bool premiumStatus;
  final bool showCTA;
  final VoidCallback? onVideoAdded;

  TemplateVideo({
    Key? key,
    required this.videoUrl,
    required this.premiumStatus,
    required this.showCTA,
    this.onVideoAdded,
  }) : super(key: key);

  @override
  _TemplateVideoState createState() => _TemplateVideoState();
}

class _TemplateVideoState extends State<TemplateVideo> {
  late VideoPlayerController _controller;
  bool _isPlaying = true; // To track the video play/pause state

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl);
    if (widget.premiumStatus) {
      _initializeVideoPlayer();
    } else {
      _controller.initialize().then((_) {
        setState(() {});
      });
    }
  }

  void _initializeVideoPlayer() {
    _controller.initialize().then((_) {
      setState(() {}); // Ensure the widget rebuilds after video is initialized
      _controller.setLooping(true); // Set video to loop
      _controller.play(); // Start playing the video
    }).catchError((error) {
      print("Video player error: $error");
      // Handle error here (e.g., show a message or a fallback UI)
    });
  }

  @override
  void dispose() {
    _controller
        .dispose(); // Clean up the controller when the widget is disposed
    super.dispose();
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying ? _controller.pause() : _controller.play();
      _isPlaying = !_isPlaying;
    });
  }

  void _showPurchaseDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Purchase Premium"),
          content: Text(
              "Unlock this video and share it by purchasing the premium subscription."),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: Text("Cancel"),
            ),
            ElevatedButton(
              onPressed: () {
                // Implement the purchase premium logic here
                Navigator.of(context).pop(); // Close the dialog after purchase
              },
              child: Text("Purchase"),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          // Check if user is premium
          widget.premiumStatus
              ? _controller.value.isInitialized
                  ? AspectRatio(
                      aspectRatio: _controller.value.aspectRatio,
                      child: VideoPlayer(_controller),
                    )
                  : Center(
                      child: CircularProgressIndicator(),
                    ) // Loading indicator while video initializes
              : Stack(
                  alignment: Alignment.center,
                  children: [
                    // Display the video thumbnail in a greyed-out state
                    _controller.value.isInitialized
                        ? AspectRatio(
                            aspectRatio: _controller.value.aspectRatio,
                            child: ColorFiltered(
                              colorFilter: ColorFilter.mode(
                                Colors.transparent,
                                BlendMode.saturation, // Greyscale effect
                              ),
                              child: VideoPlayer(_controller),
                            ),
                          )
                        : AspectRatio(
                            aspectRatio: 16 / 9,
                            child: Container(
                              color: Colors.black.withOpacity(0.5),
                            ),
                          ),
                    // Lock icon on the top-right corner with click action
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: _showPurchaseDialog, // Show popup on tap
                        child: Icon(Icons.lock, size: 50, color: Colors.white),
                      ),
                    ),
                  ],
                ),
          SizedBox(height: 8),
          widget.premiumStatus
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      onPressed: _togglePlayPause,
                      icon: Icon(
                        _isPlaying ? Icons.pause : Icons.play_arrow,
                        size: 30,
                      ),
                    ),
                  ],
                )
              : SizedBox(), // Hide play/pause controls if not premium
          widget.showCTA && widget.premiumStatus
              ? ElevatedButton(
                  onPressed: () {
                    // Implement your CTA action here
                  },
                  child: Text("Call to Action"),
                )
              : SizedBox(), // Conditionally show the CTA button
        ],
      ),
    );
  }
}
