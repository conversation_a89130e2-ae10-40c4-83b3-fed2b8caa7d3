import 'dart:convert';
import 'dart:io';
import 'dart:developer';

import 'package:bjpnew/screens/nameplate/fifth_name_plate.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';

import 'package:bjpnew/global/processvideo.dart';
import 'package:bjpnew/locator/app.locator.dart';
import 'package:bjpnew/screens/nameplate/first_name_design.dart';
import 'package:bjpnew/screens/nameplate/fourthNameplate.dart';
import 'package:bjpnew/screens/nameplate/secondnameplate.dart';
import 'package:bjpnew/screens/nameplate/thirdnameplate.dart';
import 'package:bjpnew/screens/premium-screen-mvvm/phonepe_premiumview.dart';
import 'package:bjpnew/services/imagesizeservice.dart';
import 'package:bjpnew/services/leaderPhotoservice.dart';
import 'package:bjpnew/services/textsizeservice.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import '../../global/primary_button.dart';

import '../../services/download_share_image.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import '../../utils/Singletons/prefs_singleton.dart';

import 'package:crypto/crypto.dart';
import 'package:path/path.dart' as path;

class TemplatesViewModel extends BaseViewModel {
  //instances
  final Prefs _prefs = Prefs.instance;

  //for showing and hiding leader photos in case of birthday..
  bool _showLeaderPhotos = false;
  bool _isBjp = true;
  bool get isBjp => _isBjp;

  bool get showLeaderPhotos => _showLeaderPhotos;
  void setShowLeaderPhotos(bool value) async {
    _showLeaderPhotos = value;
    notifyListeners();
    _prefs.setBool('showLeaderPhotos', value);
  }

  //getters and setters
  int? get selectedID => _prefs.getInt('SelectedID');

  String _selectedCategory = '';
  String get selectedCategory => _selectedCategory;
  set selectedCategory(value) {
    _selectedCategory = value;
    notifyListeners();
  }

  Color _containerColor = HexColor('#FFFFFF');
  Color _mutedColor = HexColor('#c4c2c2');
  Color get containerColor => _containerColor;
  Color get mutedColor => _mutedColor;
  set containerColor(value) {
    _containerColor = value;
    notifyListeners();
  }

  //height of designation strip + socials
  double _wrapHeight = 0;
  double get wrapHeight => _wrapHeight;
  void setWrapHeight(double height) {
    _wrapHeight = height;
    notifyListeners();
  }

  

   int getSocialCount() {
    final socialCount = [_facebookId, _instaId, _twitterId, _number, _address]
        .where((e) => e.isNotEmpty)
        .length;
    return socialCount;
  }

  double getHeaderHeight(BuildContext context) {
    return wrapHeight;
  }

  // bool _premiumStatus = false;
  // bool get premiumStatus => _premiumStatus;
  // set premiumStatus(value) {
  //   _premiumStatus = value;
  //   notifyListeners();
  // }

  late List<String> _userDetails;
  List<String> get userDetails => _userDetails;
  set userDetails(value) {
    userDetails = value;
  }

  //functions for view
  Future initialize() async {
    _selectedCategory = _prefs.getString('CategorySelected') ?? '';
    _showLeaderPhotos = _prefs.getBool('showLeaderPhotos') ?? false;
    await fetchUserDetails(isTestUser: false);
    setContainerColor();
    _loadPreferences();
    checkParty();
    notifyListeners();
  }

  Future checkParty() async {
    String? party = _prefs.getString('selectedParty');
    _isBjp = (party == 'BJP');
  }

  Future fetchUserDetails({required bool isTestUser}) async {
    List<String> _details = [];
    String userName = _prefs.getString('Name') ?? '';
    String userTitle = _prefs.getString('Title') ?? '';
    _facebookId = _prefs.getString('facebookId') ?? '';
    _instaId = _prefs.getString('instaid') ?? '';
    _twitterId = _prefs.getString('twitterid') ?? '';
    _secondLine = _prefs.getString('secondTitle') ?? '';
    _address = _prefs.getString('address') ?? '';

    _details.addAll([userName, userTitle]);
    _userDetails = _details;
  }

  Future<Widget> userImage() async {
    if (selectedID != null) {
      var userImage = await UserImage().returnSelectedUserImage(selectedID);
      return Image.file(
        userImage,
        fit: BoxFit.contain,
        width: 260,
        height: 260,
        alignment: Alignment.bottomCenter,
      );
    } else {
      return Container(
          child: SvgPicture.asset(
        'Asset/SVG/ImagePlaceholder.svg',
        fit: BoxFit.contain,
      ));
    }
  }

  String _selectedFontFamily = 'Baloo2';

  String get selectedFontFamily => _selectedFontFamily;

  void updateFontFamily(String newFontFamily) {
    _selectedFontFamily = newFontFamily;
    notifyListeners();
  }

  Future<Widget> leaderImage() async {
    if (selectedID != null) {
      var userImage = await UserImage().returnSelectedUserImage(selectedID);
      return Image.file(
        userImage,
        fit: BoxFit.contain,
        width: 260,
        height: 260,
        alignment: Alignment.bottomCenter,
      );
    } else {
      return Container(
          child: SvgPicture.asset(
        'Asset/SVG/ImagePlaceholder.svg',
        fit: BoxFit.contain,
      ));
    }
  }

  Future<Widget> userImages() async {
    if (selectedID != null) {
      var userImage = await UserImage().returnSelectedUserImage(selectedID);
      return Image.file(
        userImage,
        fit: BoxFit.contain,
        width: 260,
        height: 260,
        alignment: Alignment.bottomCenter,
      );
    } else {
      return Container(
          child: SvgPicture.asset(
        'Asset/SVG/ImagePlaceholder.svg',
        fit: BoxFit.contain,
      ));
    }
  }

  void setContainerColor({bool refresh = true}) {
    switch (_selectedCategory) {
      case 'BJP':
        {
          _containerColor = HexColor('#F97D09');
          break;
        }
      case 'Congress':
        {
          _containerColor = HexColor('#0e813e');
          break;
        }
      case 'BSP':
        {
          _containerColor = HexColor('#2747b6');
          break;
        }
      case 'SP':
        {
          containerColor = HexColor('#0e6c37');
          break;
        }
      case 'AAP':
        {
          containerColor = HexColor('#1073ac');
          break;
        }
      default:
        containerColor = HexColor('#F97D09');
        break;
    }
    _mutedColor = HSLColor.fromColor(containerColor)
        .withLightness((HSLColor.fromColor(containerColor).lightness * 0.7)
            .clamp(0.0, 1.0))
        .toColor();
    if (refresh) notifyListeners();
  }

  Future<XFile?> userImageChange(context) async {
    ThemeData themeData = Theme.of(context);
    var image = await UserImage().pickImage(themeData);
    await UserImage().addUserImage(image);
    return image;
  }

  Map<String, String> processedVideoCache = {};

// Function to generate a unique identifier
  String generateUniqueIdentifier(String videoUrl) {
    return md5.convert(utf8.encode(videoUrl)).toString();
  }

  Future<void> conditionalButtonClick1({
    required ScreenshotController controller,
    required BuildContext context,
    required premiumStatus,
    required bool isPoster,
    required String imageUrl,
    required bool isTestUser,
    required Function setLoading,
    required ScreenshotController userController,
    bool? isBirthday,
    String? birthdayPhoto,
    String? birthdayName,
  }) async {
    int? selectedID = await SharedPreferences.getInstance()
        .then((prefs) => prefs.getInt('SelectedID'));

    if (selectedID == null) {
      await showPhotoWarning(premiumStatus, context);
      // print("abcd");
      // print(premiumStatus);
    } else {
      if (isPoster) {
        if (premiumStatus) {
          DownloadShareImage(controller: controller).shareScreenshot();
        } else {
          Navigator.of(context, rootNavigator: true).push(
            MaterialPageRoute(
              builder: (BuildContext context) {
                return PremiumView(
                  imageUrl: imageUrl,
                  isPoster: isPoster,
                  isTestUser: isTestUser,
                  isBirthday: isBirthday,
                  birthdayName: birthdayName,
                  birthdayPhoto: birthdayPhoto,
                );
              },
            ),
          );
          // Navigator.push(
          //   context,
          //   MaterialPageRoute(
          //     builder: (context) => PremiumView(
          //       imageUrl: imageUrl,
          //       isPoster: isPoster,
          //       isTestUser: isTestUser,
          //     ),
          //   ),
          // );
        }
      } else {
        // Generate a unique identifier for the video
        String uniqueIdentifier = generateUniqueIdentifier(imageUrl);

        String? processedVideoPath = processedVideoCache[uniqueIdentifier];
        if (processedVideoPath == null) {
          setLoading(true);
          // Process video with overlay
          List<String> imagePaths = await _imageService.getImageList();
          print('Image paths: $imagePaths'); // Debug print

          processedVideoPath = await processVideoWithOverlay(context, imageUrl,
              imagePaths, uniqueIdentifier, controller, userController

              // 5
              );
          print('Original video path: $imageUrl');
          print('Processed video path: $processedVideoPath');

          // Save the processed video path in the cache
          processedVideoCache[uniqueIdentifier] = processedVideoPath;
          setLoading(false);
        } else {
          print('Using cached processed video path: $processedVideoPath');
        }

        if (premiumStatus) {
          DownloadShareImage().shareVideos(processedVideoPath);
          print(processedVideoPath);
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PremiumView(
                imageUrl: processedVideoPath ?? '',
                isPoster: isPoster,
                isTestUser: isTestUser,
                isBirthday: isBirthday,
                birthdayName: birthdayName,
                birthdayPhoto: birthdayPhoto,
              ),
            ),
          );
        }
      }
    }
  }

  void conditionalButtonClick(
      {required ScreenshotController controller,
      required BuildContext context,
      required premiumStatus,
      required bool isPoster,
      required String imageUrl,
      required bool isTestUser}) async {
    int? selectedID = _prefs.getInt('SelectedID');
    if (selectedID == null) {
      await showPhotoWarning(premiumStatus, context);
      print("abcd");
      print(premiumStatus);
    } else if (premiumStatus) {
      if (isPoster) {
        DownloadShareImage(controller: controller).shareScreenshot();
      } else {
        DownloadShareImage().shareVideo(imageUrl);
      }
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PremiumView(
            imageUrl: imageUrl,
            isTestUser: isTestUser,
            isPoster: isPoster,
          ),
        ),
      );
    }
    //   AutoRouter.of(context)
    //       .push(PremiumViewRoute(imageUrl: imageUrl, isTestUser: isTestUser,isPoster: isPoster,));
    // }
  }
  //   if (selectedID == null) {
  //     await showPhotoWarning(premiumStatus, context);
  //     print("abcd");
  //     print(premiumStatus);
  //   } else if (premiumStatus) {
  //     DownloadShareImage(controller: controller).shareScreenshot();
  //   } else {
  //     AutoRouter.of(context)
  //         .push(PremiumViewRoute(imageUrl: imageUrl, isTestUser: isTestUser));
  //   }
  // }

  Future showPhotoWarning(premiumStatus, context) {
    ThemeData themeData = Theme.of(context);
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            content: Container(
              padding: EdgeInsets.all(20),
              child: SingleChildScrollView(
                  child: ListBody(
                children: [
                  LottieBuilder.asset(
                    'Asset/Lottie/alert.json',
                    height: 84,
                    width: 84,
                  ),
                  Text(
                    'Please upload photo!',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  )
                ],
              )),
            ),
            actions: [
              PrimaryButton(
                isEnabled: true,
                isLoading: false,
                onTap: () async {
                  await userImageChange(context);
                  // if(isPhotoAdded){Navigator.pop(context);}
                },
                label: 'Upload Photo',
                color: themeData.colorScheme.primary,
              )
            ],
          );
        });
  }

  var image = [];

  Future<Map<String, dynamic>> fetchStateInfos() async {
    // final configUrl = Uri.parse(
    //     'https://backend.designboxconsuting.com/poster/config/v1/config');
    // try {
    //   final response = await http.get(configUrl);
    //   if (response.statusCode == 200) {
    //     await Prefs.instance.setString('config', response.body);
    //     print("config");
    //     print(response.body);
    final prefs = await SharedPreferences.getInstance();
    //     final String? token = prefs.getString('token');
    //     final String? userId = prefs.getString('userId');
    //     print(userId);
    //     print(token);
    //   } else {
    //     // Handle error
    //     print('Failed to load config: ${response.statusCode}');
    //   }
    // } catch (e) {
    //   // Handle network error
    //   print('Failed to load config: $e');
    // }

    String? configJson = prefs.getString('config');
    print(configJson);
    if (configJson != null) {
      Map<String, dynamic> config =
          jsonDecode(utf8.decode(configJson.codeUnits));
      List<dynamic> stateInfos = config['stateInfos'];
      var stateName = prefs.getString('CategorySelected');
      var partyName = prefs.getString('selectedParty');

      final selectedStateInfo = stateInfos.firstWhere(
        (info) => info["state"] == stateName,
        orElse: () => null,
      );

      // final selectedPartyInfo = selectedStateInfo["partyInfoList"].firstWhere(
      //   (info) => info["partyId"] == partyName,
      //   orElse: () => null,
      // );
      // image = selectedPartyInfo["leadersImgUrls"] ?? "";
      image = selectedStateInfo["leadersImgUrls"];

      List<File> savedFiles = await updateImages(image);
      List<String> list = [];
      // You can now use the list of savedFiles in your app
      for (File file in savedFiles) {
        // print('Saved file: ${file.path}');
        list.add(file.path);
      }

      //new added
      await prefs.setStringList('my_string_list', list);

      print('list :  $list');
      notifyListeners();

      return {
        "stateName": stateName,
        "data": list
        // print(stateName);
        // String jsonString = jsonEncode(selectedStateInfo["leadersImgUrls"]);
        // print(selectedStateInfo["leadersImgUrls"]);
        // await prefs.setString('LeaderPhoto' ,jsonString );

        /////////////////////////////////////////
        // String? configJson = _prefs.getString('config');
        // if (configJson != null) {
        //   Map<String, dynamic> config =
        //       jsonDecode(utf8.decode(configJson.codeUnits));
        //   List<dynamic> stateInfos = config['stateInfos'];
        //   var stateName = _prefs.getString('CategorySelected');

        //   final selectedStateInfo = stateInfos.firstWhere(
        //     (info) => info["state"] == stateName,
        //     orElse: () => null,
        //   );
        //   print(stateName);
        // //   print(selectedStateInfo["leadersImgUrls"]);
        // var Leaderphoto = await prefs.getString("LeaderPhoto");
        // List<dynamic> list = jsonDecode(Leaderphoto!);

        //   image= list! ;
        //   print(image);
      };
    } else {
      return {};
    }
  }

  final Dio _dio = Dio();

  Future<List<File>> updateImages(List<dynamic> imageUrls) async {
    // Get the directory to save images
    final directory = await getApplicationDocumentsDirectory();
    final imagesDirectory = Directory('${directory.path}/images');

    // Create images directory if it doesn't exist
    if (!imagesDirectory.existsSync()) {
      imagesDirectory.createSync();
    }

    // List to hold saved image files
    List<File> savedFiles = [];

    // Set to track new image file names from the URLs
    final newFileNames = imageUrls.map((url) => path.basename(url)).toSet();

    // List of image files that are currently saved locally
    final existingImageFiles =
        imagesDirectory.listSync().whereType<File>().toList();

    // Set to track existing local file names
    // final existingFileNames = existingImageFiles.map((file) => path.basename(file.path)).toSet();

    // Download and save new images
    for (String url in imageUrls) {
      final fileName = path.basename(url);
      final localImageFile = File('${imagesDirectory.path}/$fileName');

      // Check if the image already exists locally
      if (!localImageFile.existsSync()) {
        try {
          final response = await _dio.get<List<int>>(
            url,
            options: Options(responseType: ResponseType.bytes),
          );
          localImageFile.writeAsBytesSync(response.data!);
          // print('Image saved: $fileName');
          savedFiles.add(localImageFile); // Add the new file to the list
        } catch (e) {
          print('Error downloading image: $e');
        }
      } else {
        print('Image already exists: $fileName');
        savedFiles.add(localImageFile); // Add existing file to the list
      }
    }

    // Remove images that are no longer in the image URLs list
    for (FileSystemEntity file in existingImageFiles) {
      final fileName = path.basename(file.path);
      if (!newFileNames.contains(fileName) && file.existsSync()) {
        file.deleteSync();
        print('Removed unused image: $fileName');
      }
    }

    // Return the list of saved files
    return savedFiles;
  }

  // List<String> _selectedImages = [];

  // List<String> get selectedImages => _selectedImages;

  // ImageSelectionViewModel()async {

  //   _loadSelectedImages();

  // }

  Future<void> loadStoredName() async {
    final prefs = await SharedPreferences.getInstance();

    _checkbox4 = prefs.getBool('checkbox4') ?? false;
    _checkbox5 = prefs.getBool('checkbox5') ?? false;
    _checkbox6 = prefs.getBool('checkbox6') ?? false;
    _checkbox7 = prefs.getBool('checkbox7') ?? false;
    notifyListeners();
  }

  bool _checkbox4 = true;
  bool _checkbox5 = false;
  bool _checkbox6 = false;
  bool _checkbox7 = false;

  bool get checkbox4 => _checkbox4;
  bool get checkbox5 => _checkbox5;
  bool get checkbox6 => _checkbox6;
  bool get checkbox7 => _checkbox7;

//   void toggleSelection(String imagePath) {
//     if (_selectedImages.contains(imagePath)) {
//       _selectedImages.remove(imagePath);
//     } else {
//       _selectedImages.add(imagePath);
//     }
//     notifyListeners();
//   }

//   bool isSelectedd(String imagePath) {
//     return _selectedImages.contains(imagePath);
//   }

//   Future<void> _loadSelectedImages() async {
//     final prefs = await SharedPreferences.getInstance();
//     _selectedImages = prefs.getStringList('selectedImages') ?? [];
//     notifyListeners();
//   }

//   Future<void> saveSelectedImages() async {
//     final prefs = await SharedPreferences.getInstance();
//     await prefs.setStringList('selectedImages', _selectedImages);
//   }

//   List<String> _imageData = [];
//   List<String> get imageData => _imageData;

//   // void addImage(String imagePath) {
//   //  if (!_imageData.contains(imagePath)) {
//   //     _imageData.add(imagePath);
//   //     notifyListeners(); // Notify listeners to update UI
//   //   } // Notify listeners to update UI
//   // }
//   void addImage(dynamic imagePath) async {
//     String pathToAdd = imagePath is File ? imagePath.path : imagePath;

//     if (!_imageData.contains(pathToAdd)) {
//       _imageData.add(pathToAdd);
//       final SharedPreferences prefs = await SharedPreferences.getInstance();

//       // Retrieve the existing list
//       List<String> existingList = prefs.getStringList('my_string_list') ?? [];

//       // Add the new item to the list
//       existingList.add(pathToAdd);

//       // Save the updated list back to SharedPreferences
//       await prefs.setStringList('my_string_list', existingList);

//       //     final SharedPreferences prefs = await SharedPreferences.getInstance();
//       // await prefs.setStringList('my_string_list',  _imageData);

//       notifyListeners();
//     }
//   }

//   Future<List<String>> getImageList() async {
//     final SharedPreferences prefs = await SharedPreferences.getInstance();
//     var image =await prefs.getStringList('my_string_list') ?? [] ;

//     return image;
//   }

//   void removeImage(dynamic imagePath, [File? fileImagePath]) async {
//     String pathToRemove = imagePath is File ? imagePath.path : imagePath;

//     final SharedPreferences prefs = await SharedPreferences.getInstance();
//     // Retrieve the existing list
//     List<String> existingList = prefs.getStringList('my_string_list') ?? [];

// // print(existingList);
// //  print(existingList.length);
//     existingList.remove(pathToRemove);

//     // Save the updated list back to SharedPreferences
//     await prefs.setStringList('my_string_list', existingList);
// //   print(existingList);
// //  print(existingList.length);
//   }

//   var imageDataLocal;
//   fetchData() async {
//     final SharedPreferences prefs = await SharedPreferences.getInstance();
//     imageDataLocal = prefs.getStringList('my_string_list');
//   }

//   bool isSelected(dynamic imagePath) {
//     // Convert File to String if a File is passed
//     //  fetchData();
//     //  notifyListeners();
//     String pathToCheck = imagePath is File ? imagePath.path : imagePath;
//     return _imageData.contains(pathToCheck);
//   }

// add leader photo with locator
  final ImageService _imageService =
      locator<ImageService>(); // Access service directly from locator

  List<String> _selectedImages = [];
  List<String> get selectedImages => _selectedImages;

  List<String> _imageData = [];
  List<String> get imageData => _imageData;

  // Toggle image selection
  void toggleSelection(String imagePath) {
    if (_selectedImages.contains(imagePath)) {
      _selectedImages.remove(imagePath);
    } else {
      _selectedImages.add(imagePath);
    }
    notifyListeners();
    _imageService.saveSelectedImages(_selectedImages);
  }

  // Check if an image is selected
  bool isSelected(String imagePath) {
    return _selectedImages.contains(imagePath);
  }

  // Load selected images
  Future<void> _loadSelectedImages() async {
    _selectedImages = await _imageService.getSelectedImages();
    notifyListeners();
  }

  // Add image to list
  void addImage(dynamic imagePath) async {
    String pathToAdd = imagePath is File ? imagePath.path : imagePath;

    if (!_imageData.contains(pathToAdd)) {
      _imageData.add(pathToAdd);
      await _imageService.addImageToList(pathToAdd);
      notifyListeners();
    }
  }

  // Remove image from list
  void removeImage(dynamic imagePath) async {
    String pathToRemove = imagePath is File ? imagePath.path : imagePath;

    _imageData.remove(pathToRemove);
    await _imageService.removeImageFromList(pathToRemove);
    notifyListeners();
  }

  // Update the entire image list
  void updateImageList(List<dynamic> newList) async {
    List<String> updatedList =
        newList.map((e) => e is File ? e.path : e.toString()).toList();
    _imageData = updatedList; // Update local list
    await _imageService
        .updateImageList(updatedList); // Update SharedPreferences
    notifyListeners();
  }

  bool check = false;
  // Load the image list
  Future<void> _loadImageList() async {
    // Fetch data from the image service or SharedPreferences
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    _imageData = prefs.getStringList('my_string_list') ?? [];

    if (_imageData.isEmpty && !check) {
      await fetchStateInfos();
      check = true;
    }

    notifyListeners(); // This will notify the UI to rebuild when data is ready
  }

  // Method to retrieve image list from the service
  Future<List<String>> getImageList() async {
    if (_imageData.isEmpty) {
      await _loadImageList();
    }
    return _imageData;
  }

  // Check if image is in the list
  bool isImageInList(String imagePath) {
    return _imageData.contains(imagePath);
  }

  Future<bool> isSelectedlocator(dynamic imagePath) {
    String pathToCheck = imagePath is File ? imagePath.path : imagePath;
    return _imageService.isSelected(imagePath);
  }

//add leader photo

  List<File> get uploadedLogos => _imageService.uploadedLogos;

  Future<void> loadUploadedLogos() async {
    await _imageService.loadUploadedLogos();
    notifyListeners();
  }

  Future<void> addLogo() async {
    var image = await _imageService.addLogo();

    String pathToAdd = image is File ? image : image;

    if (!_imageData.contains(pathToAdd)) {
      _imageData.add(pathToAdd);
      await _imageService.addImageToList(pathToAdd);
      notifyListeners();
    }

    notifyListeners();
  }

  Future<void> addMultiLogos() async {
    var images =
        await _imageService.addLogos(); // Call modified multi-image function
    for (String imagePath in images) {
      if (!_imageData.contains(imagePath)) {
        _imageData.add(imagePath);
        await _imageService.addImageToList(imagePath);
      }
    }
    notifyListeners();
  }

  ///name plate
  int? _selectedIndex;
  List<String> _images = [
    'Asset/Nameplates/firstnameplate.jpeg', // Replace with your image URLs
    'Asset/Nameplates/secondnameplate.jpeg',
    'Asset/Nameplates/third.jpeg',
    'Asset/Nameplates/fourthnameplate.jpeg',
    'Asset/Nameplates/fifthnameplate.jpeg',
    'Asset/Nameplates/fifthCustomNameplate.jpg'
  ];

  int? get selectedIndex => _selectedIndex;
  List<String> get images => _images;

  TemplatesViewModel() {
    _loadSelectedIndex();
    _loadImageSizeFactor();
    _loadTextSizes();
    _loadSelectedImages();
    _loadImageList();
    loadUploadedLogos();

    updateLeaderPhotos();
  }

  Future<void> updateLeaderPhotos() async {
    var data = await fetchStateInfos();
    List<String> image = data['data'];

    List<String> stringList =
        image.map((element) => element.toString()).toList();
    await _prefs.setStringList('my_string_list', stringList);

    _imageData = stringList;
    notifyListeners();
  }

  Future<void> _loadSelectedIndex() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    // _selectedIndex = prefs.getInt('selectedIndex') ;
    _selectedIndex = prefs.getInt('selectedIndex') ?? 5;
    notifyListeners();
  }

  Future<void> selectIndex(int index) async {
    _selectedIndex = index;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt('selectedIndex', index);
    notifyListeners();
  }

  Widget getSelectedWidget(ScreenshotController _Stripcontroller,
      GlobalKey<State<StatefulWidget>> _stripKey, Color color) {
    switch (_selectedIndex) {
      case 0:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child:
                FirstNamePlate(viewModel: this, fontfamily: selectedFontFamily),
          ),
        );
      case 1:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child: Secondnameplate(
                viewModel: this, fontfamily: selectedFontFamily),
          ),
        );
      case 2:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child: ThirdNamePlate(
                viewModel: this, color: color, fontfamily: selectedFontFamily),
          ),
        );
      case 3:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child: FourthNamePlate(
                viewModel: this,
                isfourth: false,
                fontfamily: selectedFontFamily),
          ),
        );
      case 4:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child: FourthNamePlate(
                viewModel: this,
                isfourth: true,
                fontfamily: selectedFontFamily),
          ),
        );
       case 5:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child: FifthNamePlate(
              viewModel: this,
              logo: 'Asset/Political Logos/BJP-Logo.png',
              color: isBjp ? Color(0xFFFF9933) : Color(0xFF008000),
              fontfamily: selectedFontFamily,
            ),
          ),
        );
      default:
        return Screenshot(
          controller: _Stripcontroller,
          child: RepaintBoundary(
            key: _stripKey,
            child:
                ThirdNamePlate(viewModel: this, fontfamily: selectedFontFamily),
          ),
        );
    }
  }

  bool checkAnyTrue() {
    return checkbox4 || checkbox5 || checkbox6 || checkbox7;
  }

  double getHeightForNamePlate(BuildContext context) {
    if (_selectedIndex == null) {
      return MediaQuery.of(context).size.height *
          0.06; // Default value when no index is selected
    }

    switch (_selectedIndex) {
      case 0:
        return MediaQuery.of(context).size.height * 0.02;
      case 1:
        return MediaQuery.of(context).size.height * 0.02;
      case 2:
        return checkAnyTrue() == false
            ? MediaQuery.of(context).size.height * 0.04
            : MediaQuery.of(context).size.height * 0.06;
      case 3:
        return MediaQuery.of(context).size.height * 0.00;
      case 4:
        return MediaQuery.of(context).size.height * 0.063;
      case 5:
        return getHeaderHeight(context);
      // Add more cases as needed
      // Example for index 3
      // Example for index 2
      default:
        return MediaQuery.of(context).size.height *
            0.02; // Default case for other indexes
    }
  }

  String _facebookId = '';
  String _instaId = '';
  String _twitterId = '';
  String _secondLine = '';
  String _number = '';
  String _address = '';

  String get facebookId => _facebookId;
  String get instaId => _instaId;
  String get twitterId => _twitterId;
  String get secondLine => _secondLine;
  String get number => _number;
  String get address => _address;

  set facebookId(String value) => _facebookId = value;
  set instaId(String value) => _instaId = value;
  set twitterId(String value) => _twitterId = value;
  set secondLine(String value) => _secondLine = value;
  set number(String value) => _number = value;
  set address(String value) => _address = value;


  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    _facebookId = prefs.getString('facebookId') ?? '';
    _instaId = prefs.getString('instaid') ?? '';
    _twitterId = prefs.getString('twitterid') ?? '';
    _secondLine = prefs.getString('secondTitle') ?? '';
    _number = prefs.getString('number') ?? "0";
     _address = prefs.getString('address') ?? "";
    notifyListeners();
  }
  // double _imageSizeFactor = 1.0; // Single factor for both width and height
  // bool _isLoading = false;

  // bool get isLoading => _isLoading;
  // double get imageSizeFactor => _imageSizeFactor; // This will be used for both width and height

  // static const String imageSizeFactorKey = 'imageSizeFactor';

  // ImageSizeViewModel() {
  //   _loadImageSizeFactor();
  // }

  // // Method to change the size factor
  // Future<void> changeImageSize(double factor) async {
  //   _isLoading = true;
  //   notifyListeners();

  //   // Adjust the size factor based on the slider value
  //   _imageSizeFactor = 1.0 + (factor / 100);

  //   _isLoading = false;
  //   notifyListeners();

  //   // Save the updated size factor
  //   await _saveImageSizeFactor();
  // }

  // // Load image size factor from SharedPreferences
  // Future<void> _loadImageSizeFactor() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   _imageSizeFactor = prefs.getDouble(imageSizeFactorKey) ?? 1.0;
  //   notifyListeners();
  // }

  // // Save image size factor to SharedPreferences
  // Future<void> _saveImageSizeFactor() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   await prefs.setDouble(imageSizeFactorKey, _imageSizeFactor);
  // }

  final ImageSizeService _imageSizeService = locator<ImageSizeService>();

  bool _isLoading = false;

  bool get isLoading => _isLoading;
  double get imageSizeFactor => _imageSizeService.imageSizeFactor;

  Future<void> _loadImageSizeFactor() async {
    setBusy(true);
    await _imageSizeService.loadImageSizeFactor();
    setBusy(false);
    notifyListeners();
  }

  Future<void> changeImageSize(double factor) async {
    setBusy(true);
    await _imageSizeService.changeImageSize(factor);
    setBusy(false);
    notifyListeners();
  }

  final TextSizeService _textSizeService = locator<TextSizeService>();

  bool _isLoadingg = false;
  bool get isLoadingg => _isLoadingg;

  // Method to get the text size for a particular key
  double getTextSize(String key) => _textSizeService.getTextSize(key);

  // Method to update the text size for a specific key
  Future<void> changeTextSize(String key, double sliderValue) async {
    _isLoadingg = true;
    notifyListeners();

    await _textSizeService.changeTextSize(key, sliderValue);

    _isLoadingg = false;
    notifyListeners();
  }

  // Load text sizes by calling the service
  Future<void> _loadTextSizes() async {
    await _textSizeService.loadTextSizes();
    notifyListeners(); // Trigger UI update after loading
  }
}
