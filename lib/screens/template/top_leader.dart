import 'dart:io';

import 'package:flutter/material.dart';
import 'package:bjpnew/screens/template/add_leader_viewmodel.dart';
import 'package:stacked/stacked.dart';
// Adjust the import path

class TopleaderImage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AddleaderViewModel>.reactive(
        viewModelBuilder: () => AddleaderViewModel(),
        builder: (context, viewModel, child) {
          return Column(
            children: [
              Container(
                height: 100,
                width: MediaQuery.of(context).size.width,
                child: FutureBuilder<void>(
                  future: viewModel.loadImages(), // Ensure images are loaded
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Center(child: CircularProgressIndicator());
                    } else if (snapshot.hasError) {
                      return Center(child: Text('Error: ${snapshot.error}'));
                    } else {
                      // Filter selected images
                      var selectedImages = viewModel.selectedImages;

                      return ListView.builder(
                        itemCount: selectedImages.length,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          var imagePath = selectedImages[index];
                          print(imagePath);
                          return Container(
                            height: imagePath.length < 6 ? 60 : 35,
                            width: imagePath.length < 6 ? 50.0 : 35,
                            margin:
                                EdgeInsets.only(bottom: 40, left: 5, top: 5),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.green),
                              borderRadius: BorderRadius.circular(5.0),
                              image: DecorationImage(
                                image: imagePath[index].startsWith('http')
                                    ? NetworkImage(imagePath[index])
                                    : FileImage(File(imagePath[index]))
                                        as ImageProvider,
                                fit: BoxFit.cover,
                              ),
                            ),
                          );
                        },
                      );
                    }
                  },
                ),
              ),
            ],
          );
        });
  }
}
