import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:bjpnew/screens/premium-screen-mvvm/payment.dart';
import 'package:bjpnew/screens/premium-screen-mvvm/premium_paymentLive.dart';
import 'package:bjpnew/screens/upi-mvvm/upi-viewModel.dart';
import 'package:bjpnew/services/phonePe.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';
import 'package:bjpnew/screens/payment-init-mvvm/payment_init_view.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

@RoutePage()
class UPIView extends StatefulWidget {
  final String duration;
  final String packageId;
  final int amount;
  final String apiKey;
  const UPIView(
      {super.key,
      required this.duration,
      required this.amount,
      required this.packageId,
      required this.apiKey});

  @override
  State<UPIView> createState() => _UPIViewState();
}

class _UPIViewState extends State<UPIView> {
  bool _loading = true;
  bool _isRazorpayActive = false;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    await _checkPaymentGateways();
  }

  Future<void> _checkPaymentGateways({bool initiatePayment = true}) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');
    print("packageId in _checkPaymentGateways: ${widget.packageId}");
    String packagid = widget.packageId;

    final body = jsonEncode({
      'packageId': packagid,
      // 'vpa': "swapniluiitk@okhdfcbank",
      'paymentFlow': "UPI_INTENT",
      'targetApp': "com.phonepe.app",
      "recurring": "true"
    });
    print("packageid, ${widget.packageId}");
    print("packageidkkkk, $packagid");

    final response = await http.post(
        Uri.parse(
            "https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions"),
        headers: {
          "Content-Type": "application/json",
          'TOKEN': token ?? "",
          'app-user-id': userId ?? "",
          "DEVICE_ID": deviceId ?? "",
          "CLIENT_VERSION": "39",
          "CLIENT_TYPE": "ANDROID",
          "CLIENT_VERSION_CODE": "94"
        },
        body: body);
    final status = response.statusCode;
    // final response = await http.get(Uri.parse(
    //     'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions'));
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);

      var rporderid = data['data']['rpOrderId'];
      await prefs.setString('rpOrderId', rporderid);
      _isRazorpayActive =
          data['data']['phonePe'] == null && data['data']['rpOrderId'] != null;
      setState(() {
        _loading = false;
      });
      // _isRazorpayActive = false;
      if (_isRazorpayActive && initiatePayment) {
        log("Payment Success : [RPORDERID] " + rporderid);
        var service = RazorpayPayment(
            subscriptionId: rporderid,
            apiKey: widget.apiKey,
            amount: widget.amount.toString(),
            context: context); // Replace with your Razorpay view
        try {
          service.initRazorpay();
          service.openCheckout();

          // Process the response as needed, e.g., redirect to payment page
        } catch (e) {
          // Handle any errors that occur during payment initiation
          print('Error initiating payment: $e');
        }
      }
    } else {
      throw Exception('Failed to load payment gateways');
    }
  }

  @override
  Widget build(BuildContext context) {
    ThemeData themeData = Theme.of(context);

    if (_loading) {
      return Scaffold(
        backgroundColor: themeData.colorScheme.background,
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_isRazorpayActive) {
      return Scaffold(
        backgroundColor: themeData.colorScheme.background,
        body: GestureDetector(
          // onTap: () async {
          //   await _checkPaymentGateways(initiatePayment: true);
          //   // Add your desired onTap action here
          // },
          child: Center(
            child: Text('Payment Processing...'),
          ),
        ),
      );
    }

    return ViewModelBuilder<UPIViewModel>.reactive(
      viewModelBuilder: () => UPIViewModel(),
      onViewModelReady: (viewModel) {
        viewModel.disableAutoPay();
      },
      builder: (context, viewModel, child) => Scaffold(
        backgroundColor: themeData.colorScheme.background,
        appBar: AppBar(
          backgroundColor: themeData.colorScheme.primary,
          leading: Icon(
            Icons.arrow_back_sharp,
            color: Colors.white,
          ),
          title: Text(
            'पेमेंट पूरा करें',
            style: TextStyle(
              fontFamily: 'Mukta',
              fontWeight: FontWeight.w500,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
        ),
        body: Column(
          children: [
            FutureBuilder(
              future: viewModel.upiAppsData(),
              builder: (BuildContext context, snapshot) {
                if (snapshot.data != null) {
                  return ListView(
                    padding: EdgeInsets.only(top: 24),
                    shrinkWrap: true,
                    children: [
                      ...snapshot.data!.map(
                        (e) => Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 16),
                          child: ListTile(
                            onTap: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PaymentInitView(
                                    targetApp: e.packageName,
                                    packageId: widget.packageId,
                                  ),
                                ),
                              );
                            },
                            leading: Image.memory(
                              e.appIcon,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(Icons.error);
                              },
                            ),
                            title: Text(
                              e.appName,
                              style: TextStyle(
                                fontFamily: 'Mukta',
                                fontSize: 20,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            tileColor: Colors.white,
                            contentPadding: EdgeInsets.all(16),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20)),
                          ),
                        ),
                      ),
                    ],
                  );
                } else {
                  return CircularProgressIndicator();
                }
              },
            ),
            Spacer(),
            addCheckBox(viewModel),
          ],
        ),
      ),
    );
  }

  Widget addCheckBox(UPIViewModel viewModel) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Checkbox(
            value: viewModel.isSubscriptionUser,
            onChanged: (value) {
              setState(() {
                viewModel.isSubscriptionUser = value!;
              });
            }),
        SizedBox(
          width: 4,
        ),
        Text(
          'Auto-renews according to chosen plan',
          style: TextStyle(
              fontFamily: 'Mukta',
              fontSize: 14,
              fontWeight: FontWeight.w800,
              color: Colors.black87),
        )
      ],
    );
  }
}
