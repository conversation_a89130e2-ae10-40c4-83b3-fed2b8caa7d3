import 'package:phone_pe_pg/phone_pe_pg.dart';
import 'package:stacked/stacked.dart';

class UPIViewModel extends BaseViewModel {
  bool _isSubscriptionUser = true;
  bool get isSubscriptionUser => _isSubscriptionUser;
  set isSubscriptionUser(value) {
    _isSubscriptionUser = value;
    notifyListeners();
  }

  Future disableAutoPay() async {
    List<UpiAppInfo>? info = await upiAppsData();
    if (info == null || info.isEmpty) {
      _isSubscriptionUser = false;
      notifyListeners();
    }
  }

  Future<List<UpiAppInfo>?> upiAppsData() async {
    List<UpiAppInfo>? appInfo = await PhonePePg.getUpiApps();
    List<UpiAppInfo>? showApps = [];
    if (isSubscriptionUser) {
      showApps = appInfo!
          .where((e) =>
              e.packageName == "com.phonepe.app" ||
              e.packageName == "com.google.android.apps.nbu.paisa.user" ||
              e.packageName == "net.one97.paytm")
          .toList();
    } else {
      showApps = appInfo;
    }
    return showApps;
  }
}
