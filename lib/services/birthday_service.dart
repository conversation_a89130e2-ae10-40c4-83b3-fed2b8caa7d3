import 'dart:convert';
import 'dart:developer';
import 'package:bjpnew/services/photo_background_removal.dart';
import 'package:bjpnew/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:bjpnew/objects/Birthday.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BirthdayService {
  final String apiUrl =
      'https://backend.designboxconsuting.com'; // Replace with your actual API URL

  Future<Map<String, String>> getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    var userId = prefs.getString('userId');
    final String deviceId = prefs.getString('deviceId') ?? '';

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      "DEVICE_ID": deviceId,
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };
    return headers;
  }

  Future<List<Birthday>> fetchBirthdays() async {
    var headers = await getHeaders();
    final prefs = await SharedPreferences.getInstance();
    var userId = prefs.getString('userId') ?? '';

    final response = await http.get(
        Uri.parse(apiUrl + '/poster/v1/Birthdayposter?userId=' + userId),
        headers: headers);

    if (response.statusCode == 200) {
      Map<String, dynamic> jsonData = json.decode(decodeUtf8(response.body));
      List<dynamic> posters = jsonData["posters"] ?? [];
      log("LIST SIZE checkM : ${posters.length}");
      return posters.map((data) => Birthday.fromAPI(data)).toList();
    } else {
      throw Exception('Failed to load birthdays');
    }
  }

  Future<void> addBirthday(Birthday birthday) async {
    var headers = await getHeaders();
    String? imageUrl = birthday.imagePath != null &&
            birthday.imagePath!.trim() != '' &&
            !birthday.imagePath!.startsWith('https')
        ? await PhotoBackgroundRemoval()
            .imageUploadWithPath(birthday.imagePath!)
        : birthday.imagePath; //upload image
    final response = await http.post(
      Uri.parse(apiUrl + '/poster/v1/Birthdayposter'),
      headers: headers,
      body: jsonEncode({
        "url": imageUrl ?? '',
        "name": birthday.name,
        "dob": birthday.dob?.millisecondsSinceEpoch ?? 0,
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to add birthday');
    }
  }

  Future<void> updateBirthday(Birthday birthday) async {
    var headers = await getHeaders();
    final prefs = await SharedPreferences.getInstance();
    var userId = prefs.getString('userId');
    String? imageUrl = birthday.imagePath != null &&
            birthday.imagePath!.trim() != '' &&
            !birthday.imagePath!.startsWith('https')
        ? await PhotoBackgroundRemoval()
            .imageUploadWithPath(birthday.imagePath!)
        : birthday.imagePath; //upload image

    final response = await http.put(
      Uri.parse(
          '$apiUrl/poster/v1/Birthdayposter'), // Assuming the API accepts an ID in the URL
      headers: headers,
      body: jsonEncode({
        "posterId": birthday.posterId,
        "userId": userId,
        "name": birthday.name,
        "url": imageUrl ?? '',
        "dob": birthday.dob?.millisecondsSinceEpoch ?? 0,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update birthday');
    }
  }

  Future<void> deleteBirthday(String id) async {
    var headers = await getHeaders();
    final response = await http.delete(
        Uri.parse('$apiUrl/poster/v1/Birthdayposter/$id'),
        headers: headers);

    if (response.statusCode != 200) {
      throw Exception('Failed to delete birthday');
    }
  }
}
