import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class PosterService {
  Future<void> uploadPoster({
    required String party,
    required String language,
    required String url,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    print(token);
    final Map<String, dynamic> requestBody = {
      "description": "hello",
      "party": party,
      "language": language,
      "mediaType": "IMAGE",
      "liveAt": 0,
      "url": url,
    };
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      "DEVICE_ID": "567",
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final response = await http.post(
      Uri.parse('https://backend.designboxconsuting.com/poster/post/v1/post'),
      body: requestBody,
      headers: headers,
    );

    if (response.statusCode == 200) {
      // Successfully uploaded poster
      print('Poster uploaded successfully');
    } else {
      // Failed to upload poster
      print('Failed to upload poster. Status code: ${response.statusCode}');
    }
  }
}
