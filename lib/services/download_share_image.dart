import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class DownloadShareImage {
  final ScreenshotController? controller;

  const DownloadShareImage({Key? key, this.controller});

  Future<void> shareScreenshot() async {
    final imageFile = await controller?.capture(pixelRatio: 3);
    //   if (imageFile == null) return;

    // final imageFileSize = await imageFile.length;

    // if (imageFileSize > 200 * 1024) {
    //   final compressedImageFile = await compressImage(imageFile);

    //   if (compressedImageFile != null) {
    //     // Use the compressed image file here
    //     print('Compressed Image Path: ${compressedImageFile }');
    Uint8List? imageInUnit8List = imageFile; // store unit8List image here ;
    final tempDir = await getTemporaryDirectory();
    final path = tempDir.path;
    File file = await File('$path/image.png').create();
    file.writeAsBytesSync(imageInUnit8List!);
    Share.shareXFiles(
      [XFile('$path/image.png')],
    );
    //   } else {
    //     print('Failed to compress the image');
    //   }
    // } else {
    //   // Image is already within the size limit
    //   print('Image size is already under 200KB');
    // }
  }

  Future<Uint8List?> compressImage(Uint8List imageBytes) async {
    final int targetSizeInKB = 200;
    final int targetSizeInBytes = targetSizeInKB * 1024;

    // Create a temporary file to hold the image
    final tempDir = await getTemporaryDirectory();
    final tempImageFile = File('${tempDir.path}/temp_image.jpg');
    await tempImageFile.writeAsBytes(imageBytes);

    int quality = 100;
    List<int>? compressedBytes;

    do {
      compressedBytes = await FlutterImageCompress.compressWithFile(
        tempImageFile.absolute.path,
        quality: quality,
      );
      quality -= 10;
    } while (compressedBytes != null &&
        compressedBytes.length > targetSizeInBytes &&
        quality > 0);

    // Clean up the temporary file
    await tempImageFile.delete();

    if (compressedBytes == null) {
      return null;
    }

    return Uint8List.fromList(compressedBytes);
  }

  Future<void> shareIDCard() async {
    final imageFile = await controller?.capture(pixelRatio: 3);
    Uint8List? imageInUnit8List = imageFile; // store unit8List image here ;
    final tempDir = await getTemporaryDirectory();
    final path = tempDir.path;
    File file = await File('$path/image.png').create();
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Convert map to JSON string

    var text = await prefs.getString('stateline2');
    file.writeAsBytesSync(imageInUnit8List!);
    Share.shareXFiles([XFile('$path/image.png')],
        text:
            '$text \n App Link: https://play.google.com/store/apps/details?id=com.postersforb.BB');
  }

  Future<void> downloadPremiumScreenshot() async {
    final imageFile = await controller?.capture(pixelRatio: 3);
    //    if (imageFile == null) return;

    // final imageFileSize = await imageFile.length;

    // if (imageFileSize > 200 * 1024) {
    //   final compressedImageFile = await compressImage(imageFile);

    //   if (compressedImageFile != null) {
    //     // Use the compressed image file here
    //     print('Compressed Image Path: ${compressedImageFile }');
    Uint8List? imageInUnit8List = imageFile; // store unit8List image here ;
    final tempDir = await getTemporaryDirectory();
    final path = tempDir.path;
    File file = await File('$path/image.png').create();
    file.writeAsBytesSync(imageInUnit8List!);
    OpenFilex.open(file.path);

    //   } else {
    //     print('Failed to compress the image');
    //   }
    // } else {
    //   // Image is already within the size limit
    //   print('Image size is already under 200KB');
    // }
  }

  void shareVideos(String videoPath) async {
    final file = File(videoPath);
    if (await file.exists()) {
      Share.shareFiles([videoPath], text: 'Check out this video!');
    } else {
      print("Error: Video file not found at $videoPath");
    }
  }

  Future<void> shareVideo(String videoUrl) async {
    final url = Uri.parse(videoUrl);
    final response = await http.get(url);
    final bytes = response.bodyBytes;
    final tempDir = await getTemporaryDirectory();
    final path = tempDir.path;
    // File file = await File('$path/video.mp4').create();
    File file = await File('$path/video.mp4').create();
    file.writeAsBytesSync(bytes);
    await Share.shareXFiles(
      [XFile('$path/video.mp4')],
      text: 'Check out this video!',
    );
  }

  Future<void> nonPremiumShare({required String imageUrl}) async {
    String? urlImage = imageUrl;
    final url = Uri.parse(urlImage);
    final response = await http.get(url);
    final bytes = response.bodyBytes;
    final tempDir = await getTemporaryDirectory();
    final path = tempDir.path;
    File file = await File('$path/image.jpg').create();
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    var textlist = await prefs.getString('shareLink');

    file.writeAsBytesSync(bytes);
    await Share.shareXFiles([XFile('$path/image.jpg')],
        text: 'अभी डाउनलोड करें: $textlist');
  }
}
