// import 'dart:io';
// import 'package:http/http.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_storage/firebase_storage.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// class ActivatedParties {
//   Future<List<String>> fetchPartyNames() async {
//     List<String>? partyNames = [];
//     final DocumentReference docRef =
//         FirebaseFirestore.instance.doc('/Party_List/ActivatedPartyList');
//     DocumentSnapshot docSnap = await docRef.get();
//     try {
//       Map<String, dynamic>? data = docSnap.data() as Map<String, dynamic>?;
//       data?.forEach((key, value) {
//         partyNames.add(value.toString());
//       });
//     } catch (e) {
//       //handle errors
//       print(e);
//     }
//     return partyNames;
//   }

//   //TODO: currently logos fetched are taken out of firebase storage once and downloaded in the directory,
//   //TODO: convert this into more dynamic service, fetch links from firebase database and show them on demand

//   Future<List<File>> getLogo(
//       {required List<String> selectedPoliticalParty}) async {
//     final appDir = await getApplicationDocumentsDirectory();
//     final storageRef = FirebaseStorage.instance.ref();
//     List<File> allLogoFiles = [];
//     SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
//     bool? logo_Download = sharedPreferences.getBool('Logo_Downloaded');

//     if (logo_Download == false || logo_Download == null) {
//       for (int i = 0; i < selectedPoliticalParty.length; i++) {
//         final firebasePathReference = storageRef.child(
//             "/Logo/Political-Party-Logo-${selectedPoliticalParty[i]}.png");
//         try {
//           // Download file on specified path. Then returning the file:
//           final downloadUrl = await firebasePathReference.getDownloadURL();
//           final response = await get(Uri.parse(downloadUrl));
//           final downloadFile = File(
//               '${appDir.path}/Political-Party-Logo-${selectedPoliticalParty[i]}.png');
//           await downloadFile.writeAsBytes(response.bodyBytes);
//           allLogoFiles.add(downloadFile);
//           sharedPreferences.setBool('Logo_Downloaded', true);
//         } catch (e) {
//           print('Error in fetching image URL: $e');
//           sharedPreferences.setBool('Logo_Downloaded', false);
//           return Future.error('Error in fetching image URL: $e');
//         }
//       }
//     } else {
//       for (int i = 0; i < selectedPoliticalParty.length; i++) {
//         File logoFiles = File(
//             '${appDir.path}/Political-Party-Logo-${selectedPoliticalParty[i]}.png');
//         allLogoFiles.add(logoFiles);
//       }
//     }
//     return allLogoFiles;
//   }
// }
