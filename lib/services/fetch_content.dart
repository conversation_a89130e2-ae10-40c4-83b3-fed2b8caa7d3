import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class FetchContent {
  final String? selectedCategory;

  const FetchContent({
    this.selectedCategory = '',
  });

  Future<List<String>> returnContent() async {
    if (selectedCategory == null || selectedCategory!.isEmpty) {
      return [];
    }

    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/feed/v1/feed?party=BJP&language=$selectedCategory');

    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');
    print("deviceif for fetch content");
    print(deviceId.toString());
    if (token == null || userId == null) {
      // Handle case when token or userId is null
      print('Token or userId not found in SharedPreferences');
    }

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? "",
      'app-user-id': userId ?? "",
      "DEVICE_ID": deviceId ?? "",
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };
    print(url);
    try {
      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        print(response.body);
        List<String> urls = [];
        for (var post in jsonData['posts']) {
          urls.add(post['postInfo']['url']);
        }
        print(urls.length);
        return urls;
      } else {
        print("error fetching posters");
        print(response.statusCode);
        throw Exception('Failed to load data');
      }
    } catch (e) {
      // handle any errors
      return [];
    }
  }

  Future<List<Map<String, String>>> fetchCategories() async {
    // List<Map<String, String>>
    final preffs = await SharedPreferences.getInstance();

    final SharedPreferences _prefs = await SharedPreferences.getInstance();
    String configJson = _prefs.getString('config') ?? '';
    log("ACTIVE CATEG :  JSON " + configJson);
    // Map<String, dynamic> config = jsonDecode(configJson!);
    Map<String, dynamic> config = jsonDecode(utf8.decode(configJson.codeUnits));

    final categories = config['activeCategories'] as List<dynamic>;
    print(categories);
    return categories.map((cat) {
      final categoryMap = cat as Map<String, dynamic>;
      return {
        'categoryId': categoryMap['categoryId'] as String,
        'name': categoryMap['name'] as String,
      };
    }).toList();
  }
}
