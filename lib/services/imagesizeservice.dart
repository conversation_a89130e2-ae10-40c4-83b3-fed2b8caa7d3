// services/image_size_service.dart
import 'package:shared_preferences/shared_preferences.dart';

class ImageSizeService {
  static const String imageSizeFactorKey = 'imageSizeFactor';
  double _imageSizeFactor = 1.0;

  double get imageSizeFactor => _imageSizeFactor;

  Future<void> loadImageSizeFactor() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    _imageSizeFactor = prefs.getDouble(imageSizeFactorKey) ?? 1.0;
  }

  Future<void> saveImageSizeFactor(double factor) async {
    _imageSizeFactor = factor;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(imageSizeFactorKey, _imageSizeFactor);
  }

  Future<void> changeImageSize(double factor) async {
    _imageSizeFactor = 1.0 + (factor / 100);
    await saveImageSizeFactor(_imageSizeFactor);
  }
}
