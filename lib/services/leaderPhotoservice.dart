import 'dart:io';

import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stacked/stacked.dart';

class ImageService with ListenableServiceMixin {
  static const String _selectedImagesKey = 'selectedImages';
  static const String _imageListKey = 'my_string_list';

  // @override
  // void listenToReactiveValues(List reactiveValues) => [_localUrl, _useLocal];

  // Get selected images
  Future<List<String>> getSelectedImages() async {
    final prefs = await SharedPreferences.getInstance();

    return prefs.getStringList(_selectedImagesKey) ?? [];
  }

  // Save selected images
  Future<void> saveSelectedImages(List<String> selectedImages) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_selectedImagesKey, selectedImages);
  }

  // Get image list
  Future<List<String>> getImageList() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_imageListKey) ?? [];
  }

  // Save image to list
  Future<void> addImageToList(String imagePath) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> existingList = prefs.getStringList(_imageListKey) ?? [];
    existingList.add(imagePath);
    await prefs.setStringList(_imageListKey, existingList);
  }

  // Remove image from list
  Future<void> removeImageFromList(String imagePath) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> existingList = prefs.getStringList(_imageListKey) ?? [];
    existingList.remove(imagePath);
    await prefs.setStringList(_imageListKey, existingList);
  }

  //Update the image list..
  Future<void> updateImageList(List<String> newList) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_imageListKey, newList);
  }
//  List<String>? _imageList;
//    Future<void> init() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     _imageList = prefs.getStringList(_imageListKey) ?? [];
//   }

  // Synchronously check if the image is selected using the preloaded list
  Future<bool> isSelected(dynamic imagePath) async {
    final prefs = await SharedPreferences.getInstance();
    String pathToCheck;

    // If the input is a File, get the file path as a string
    if (imagePath is File) {
      pathToCheck = imagePath.path;
    } else if (imagePath is String) {
      pathToCheck = imagePath;
    } else {
      return false; // Return false if the input is neither File nor String
    }
    //return_imageList?.contains(pathToCheck) ?? false;

    return prefs.getStringList(_imageListKey)!.contains(pathToCheck) ?? false;
  }

  ///add leader logo
  List<File> uploadedLogos = [];
  final ImagePicker _picker = ImagePicker();

  Future<void> loadUploadedLogos() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String>? logoPaths = prefs.getStringList('uploadedLogos');
    if (logoPaths != null) {
      uploadedLogos = logoPaths.map((path) => File(path)).toList();
    }
  }

  Future<String> addLogo() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      uploadedLogos.add(File(pickedFile.path)); // Add the file to the list
      await _saveUploadedLogos(); // Save the list of logos
    }
    return pickedFile!.path; // Return the XFile object
  }

  //picks multiple images..
  Future<List<String>> addLogos() async {
    final List<XFile>? pickedFiles = await _picker.pickMultiImage();

    if (pickedFiles != null && pickedFiles.isNotEmpty) {
      for (var pickedFile in pickedFiles) {
        uploadedLogos.add(File(pickedFile.path)); // Add each file to the list
      }
      await _saveUploadedLogos(); // Save the list of logos
      return pickedFiles
          .map((file) => file.path)
          .toList(); // Return list of paths
    }
    return [];
  }

  Future<void> _saveUploadedLogos() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String> logoPaths = uploadedLogos.map((file) => file.path).toList();
    await prefs.setStringList('uploadedLogos', logoPaths);
  }
}
