import 'dart:developer';

import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/services/birthday_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as db;
import 'package:timezone/timezone.dart' as tz;

class LocalNotificationControllerManager {
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future<void> requestAlarmPermission() async {
    if (await Permission.scheduleExactAlarm.isDenied) {
      await Permission.scheduleExactAlarm.request();
    }
  }

  static Future<void> initializeNotifications() async {
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();

    await requestAlarmPermission();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: DarwinInitializationSettings(),
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveBackgroundNotificationResponse:
          onDidReceiveBackgroundNotificationResponse,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );
  }

  static void onDidReceiveBackgroundNotificationResponse(
      NotificationResponse response) {
    // Handle background notification response
  }

  static void onDidReceiveNotificationResponse(NotificationResponse response) {
    // Handle notification response when the app is in the foreground or background
  }

  static Future<void> scheduleNotificationAfter5Seconds({
    required int notificationId,
    required String title,
    required String body,
  }) async {
    db.initializeTimeZones(); // Make sure to call this at the beginning of your app's initialization.

    final now = tz.TZDateTime.now(tz.local);
    final scheduledDate = now.add(const Duration(seconds: 5));

    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'your_channel_id',
      'your_channel_name',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
    );

    NotificationDetails platformChannelSpecifics = const NotificationDetails(
      android: androidPlatformChannelSpecifics,
    );

    await flutterLocalNotificationsPlugin.zonedSchedule(
      notificationId,
      title,
      body,
      scheduledDate,
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: DateTimeComponents.dateAndTime,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  static Future<void> showImmediateNotification({
    required int notificationId,
    required String title,
    required String body,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'immediate_channel_id',
      'Immediate Notifications',
      channelDescription: 'This channel is used for immediate notifications.',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
    );

    await flutterLocalNotificationsPlugin.show(
      notificationId,
      title, // Title of the notification
      body, // Body of the notification
      platformChannelSpecifics, // Notification details
      payload: 'ImmediateNotification', // Optional payload
    );
  }

  //mcheck
  static Future<void> scheduleNotificationByDateAndTime({
    required int notificationId,
    required String title,
    required String body,
    required DateTime scheduledDateTime,
  }) async {
    // db.initializeTimeZones(); // Make sure to call this at the beginning of your app's initialization.

    final tz.TZDateTime scheduledDate =
        tz.TZDateTime.from(scheduledDateTime, tz.local);

    //Logs for testing..
    final String timeZoneName = tz.local.name; // Get the current time zone name
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    log("Time Zone: $timeZoneName");
    log("Current Time (in tz.local): $now");
    log("Scheduled DateTime (Original): $scheduledDateTime");
    log("Scheduled DateTime (tz.TZDateTime): $scheduledDate");

    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'your_channel_id',
      'your_channel_name',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
    );

    await flutterLocalNotificationsPlugin.zonedSchedule(
      notificationId,
      title,
      body,
      scheduledDate,
      payload: scheduledDate.toString(),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      matchDateTimeComponents: DateTimeComponents.dateAndTime,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  static Future<void> checkScheduledNotifications() async {
    // Fetch the list of pending notifications
    final List<PendingNotificationRequest> pendingNotifications =
        await flutterLocalNotificationsPlugin.pendingNotificationRequests();

    // Check if there are any scheduled notifications
    if (pendingNotifications.isEmpty) {
      log("No scheduled notifications found.");
      return;
    }

    log("Scheduled Notifications:");
    for (var notification in pendingNotifications) {
      log("""
    Notification ID: ${notification.id}
    Title: ${notification.title}
    Body: ${notification.body}
    Payload: ${notification.payload}
    """);
    }
  }

  static Future<void> cancelAllNotifications() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  static Future<void> cancelNotificationsWithId(int notificationId) async {
    await flutterLocalNotificationsPlugin.cancel(notificationId);
  }

  //For Birthday specifics..
  static Future<void> scheduleNotification(Birthday birthday, int index) async {
    db.initializeTimeZones();
    tz.setLocalLocation(tz.getLocation('Asia/Kolkata'));

    int notificationId = birthday.id;
    if (birthday.dob == null) {
      log("ERROR: Birth Day Not Found.");
      return;
    }
    log("Before: Birth Day ${birthday.dob.toString()}");
    var dob = _nextInstanceOfDate(birthday.dob!, birthday);
    log("AFter: Birth Day ${dob.toString()}");
    scheduleNotificationByDateAndTime(
        notificationId: notificationId,
        title: "Birthday Reminder 🎉",
        body: "It's ${birthday.name}'s birthday today!",
        scheduledDateTime: dob);
  }

  static Future<void> cancelNotification(Birthday birthday) async {
    int notificationId = birthday.id;
    await cancelNotificationsWithId(notificationId);
  }

  static Future<void> ensureNotificationsSynced(
      List<Birthday> birthdays) async {
    final List<PendingNotificationRequest> pendingNotifications =
        await flutterLocalNotificationsPlugin.pendingNotificationRequests();

    final Set<int> scheduledIds =
        pendingNotifications.map((notification) => notification.id).toSet();

    log("BD Scheduled Notifications Count: ${scheduledIds.length}");

    // Get all the birthday IDs
    final Set<int> birthdayIds = birthdays.map((b) => b.id).toSet();

    // Cancel notifications that no longer have corresponding birthdays
    for (int scheduledId in scheduledIds) {
      if (!birthdayIds.contains(scheduledId)) {
        await flutterLocalNotificationsPlugin.cancel(scheduledId);
        log("Canceled notification ID: $scheduledId");
      }
    }

    // Schedule notifications for birthdays that are not yet scheduled
    for (Birthday birthday in birthdays) {
      if (!scheduledIds.contains(birthday.id)) {
        log("Scheduling notification for: ${birthday.name} - ${birthday.dob}");
        await scheduleNotification(birthday, 0);
      }
    }
  }

  /// Utility to get the next instance of a date
  static tz.TZDateTime _nextInstanceOfDate(DateTime date, Birthday birthday) {
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledDate =
        tz.TZDateTime(tz.local, now.year, date.month, date.day, 7); // At 7 AM

    // Check if scheduledDate is the same as today and the time has already passed
    if (scheduledDate.year == now.year &&
        scheduledDate.month == now.month &&
        scheduledDate.day == now.day &&
        scheduledDate.isBefore(now)) {
      showImmediateNotification(
          notificationId: 123 + birthday.id,
          title: "Birthday Reminder 🎉",
          body: "It's ${birthday.name}'s birthday today!");
    }

    if (scheduledDate.isBefore(now)) {
      scheduledDate =
          tz.TZDateTime(tz.local, now.year + 1, date.month, date.day, 7);
    }
    //one day previous because (notification was delaying by one day..)
    // final tz.TZDateTime previousDay =
    //     scheduledDate.subtract(const Duration(days: 1));
    // scheduledDate = previousDay;

    return scheduledDate;
  }

  static Future<void> addReferralBirthdayToPref(
      Birthday newBirthday, String referrer) async {
    final prefs = await SharedPreferences.getInstance();
    String? ref = prefs.getString('referrer');

    if (ref != null && ref.isNotEmpty) {
      log("Referrer Details Already Present");
      return;
    }

    final birthdaysString = prefs.getString('birthdays');
    List<Birthday> birthdays =
        birthdaysString != null && birthdaysString.isNotEmpty
            ? Birthday.decode(birthdaysString)
            : [];

    // Check if the birthday already exists based on both the name and the date
    bool alreadyExists = birthdays
        .any((b) => b.dob == newBirthday.dob && b.name == newBirthday.name);

    if (!alreadyExists) {
      birthdays.add(newBirthday);
      await prefs.setString('birthdays', Birthday.encode(birthdays));
      await prefs.setString('referrer', referrer);
      // Set notification
      var len = birthdays.length;
      LocalNotificationControllerManager.scheduleNotification(
          newBirthday, len - 1);
    }
  }

  static BirthdayService bday = BirthdayService();
  static Future<void> addReferralBirthdayToRemote(
      Birthday newBirthday, String referrer) async {
    final prefs = await SharedPreferences.getInstance();
    String? ref = prefs.getString('referrer');

    bool? isOnboarded = prefs.getBool('isOnboarded') ?? false;
    final String? userId = prefs.getString('userId');
    final String? token = prefs.getString('token');

    if (ref != null && ref.isNotEmpty) {
      log("Referrer Details Already Present");
      return;
    }
    if (userId == null || token == null) {
      log("User Not Logged In");
      return;
    }

    List<Birthday> birthdays = await bday.fetchBirthdays();

    // Check if the birthday already exists based on both the name and the date
    bool alreadyExists = birthdays
        .any((b) => b.dob == newBirthday.dob && b.name == newBirthday.name);

    if (!alreadyExists) {
      bday.addBirthday(newBirthday);
      await prefs.setString('referrer', referrer);
      // Set notification
      var len = birthdays.length;
      LocalNotificationControllerManager.scheduleNotification(
          newBirthday, len - 1);
    }
  }
}
