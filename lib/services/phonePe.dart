// // import 'dart:convert';
// // import 'package:http/http.dart' as http;
// // import 'package:shared_preferences/shared_preferences.dart';

// // class PhonePe {
// //   final String packageId;
// //   const PhonePe({
// //     required this.packageId,
// //   });

// //   Future getResponse() async {
// //     final prefs = await SharedPreferences.getInstance();
// //     final String? token = prefs.getString('token');
// //     final String? userId = prefs.getString('userId');
// //     final String? deviceId = prefs.getString('deviceId');
// //     print("deviceif for phonepe");
// //     final headers = {
// //       "Content-Type": "application/json",
// //       'TOKEN': token ?? "",
// //       'app-user-id': userId ?? "",
// //       "DEVICE_ID": deviceId ?? '',
// //          "CLIENT_VERSION": "39",
// //       "CLIENT_TYPE": "ANDROID",
// //       "CLIENT_VERSION_CODE": "75"
// //     };
// //     print("packageid");
// //     print(packageId);

// //     final url = Uri.parse(
// //         'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions?packageId=$packageId&recurring=true');
// //     // 'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions?packageId=$packageId');
// //     print(url);

// //     try {
// //       final res = await http.post(url, headers: headers);
// //       final status = res.statusCode;

// //       if (status != 200) {
// //         throw Exception('http.post error: statusCode= $status');
// //       }
// //       print(res.body);
// //       return res.body;
// //     } catch (e) {
// //       print('Error initiating payment: $e');
// //       throw Exception('Error initiating payment: $e');
// //     }
// //   }
// // }

// import 'dart:convert';
// import 'package:http/http.dart' as http;

// class PhonePe {

//   final String duration;
//   final String? targetApp;
//   final bool isSubscriptionUser;
//   const PhonePe(
//       {required this.amount,
//       required this.userID,
//       required this.duration,
//       required this.targetApp,
//       required this.isSubscriptionUser});

//   Future getResponse() async {
//     final headers = {
//       "Content-Type": "application/json",
//     };

//     final data = jsonEncode({
//       "payment_type": isSubscriptionUser ? "SUBSCRIPTION" : "UPI_INTENT",
//       "os": "ANDROID",
//       "amount": this.amount,
//       "user_id": this.userID,
//       "duration": this.duration,
//       "target_app": this.targetApp
//     });

//     final url = Uri.parse(
//         'https://asia-south2-political-posters-388916.cloudfunctions.net/ppe-upi-request-payment-pp-v2-mah');

//     final res = await http.post(url, headers: headers, body: data);
//     final status = res.statusCode;
//     if (status != 200) throw Exception('http.post error: statusCode= $status');
//     print(res.body);
//     return res.body;
//   }
// }

// import 'dart:convert';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';

// class PhonePe {
//   final String packageId;
//   // final int amount;
//   final String paymentFlow;
//   final String? targetApp;

//   const PhonePe({
//     // required this.amount,
//     required this.packageId,
//     required this.paymentFlow,
//     required this.targetApp,
//   });

//   Future<String> getResponse() async {
//     SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

//     final prefs = await SharedPreferences.getInstance();
//     final String? token = prefs.getString('token');
//     final String? userId = prefs.getString('userId');
//     final String? deviceId = prefs.getString('deviceId');
//     print("deviceid for premium");
//     // final headers = {
//     //   'app-user-id': userId,
//     //   'token':
//     //      token,
//     //   'Content-Type': 'application/json',
//     // };

//     final body = jsonEncode({
//       'packageId': this.packageId,
//       // 'vpa': "swapniluiitk@okhdfcbank",
//       'paymentFlow': this.paymentFlow,
//       'targetApp': this.targetApp,
//     });

//     final url = Uri.parse(
//         'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions/phonepe');

//     final response = await http.post(url,
//         headers: {
//           "Content-Type": "application/json",
//           'TOKEN': token ?? "",
//           'app-user-id': userId ?? "",
//           "DEVICE_ID": deviceId ?? "",
//              "CLIENT_VERSION": "39",
//           "CLIENT_TYPE": "ANDROID",
//           "CLIENT_VERSION_CODE": "75"
//         },
//         body: body);
//     final status = response.statusCode;

//     if (status != 200) {
//       throw Exception('http.post error: statusCode= $status');
//     }
//     final responseBody = jsonDecode(response.body);
//     return responseBody['data']['redirectUrl'];
//   }
// }
// --------------------last-----
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class PhonePe {
  final String packageId;
  // final int amount;
  final String paymentFlow;
  final String? targetApp;

  const PhonePe({
    // required this.amount,
    required this.packageId,
    required this.paymentFlow,
    required this.targetApp,
  });

  Future<String> getResponse() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');
    print("deviceid for premium phonepe");
    // final headers = {
    //   'app-user-id': userId,
    //   'token':
    //      token,
    //   'Content-Type': 'application/json',
    // };

    final body = jsonEncode({
      'packageId': this.packageId,
      // 'vpa': "swapniluiitk@okhdfcbank",
      'paymentFlow': this.paymentFlow,
      'targetApp': this.targetApp,
    });

    final url = Uri.parse(
        'https://backend.designboxconsuting.com/poster/subscription/v1/subscriptions');
    print("respons3 id");
    final response = await http.post(url,
        headers: {
          "Content-Type": "application/json",
          'TOKEN': token ?? "",
          'app-user-id': userId ?? "",
          "DEVICE_ID": deviceId ?? "",
          "CLIENT_VERSION": "39",
          "CLIENT_TYPE": "ANDROID",
          "CLIENT_VERSION_CODE": "94"
        },
        body: body);
    final status = response.statusCode;
    // print("xvyzawe");
    if (status != 200) {
      throw Exception('http.post error: statusCode= $status');
    }
    final responseBody = jsonDecode(response.body);
    // print("xvywsuwuw8");
    return responseBody['data']['phonePe']['redirectUrl'];
  }
}
