import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:bjpnew/services/user_image_operations.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:flutter_native_image/flutter_native_image.dart';
import 'permissions_service.dart';

class PhotoBackgroundRemoval {
  Stream executeEverything(XFile? image) async* {
    final controller = StreamController.broadcast();
    final sharedPrefs = await SharedPreferences.getInstance();

    Widget processingWidget = Stack(
      alignment: Alignment.center,
      children: [
        Image.file(File(image!.path)),
        Container(
          color: Colors.black.withAlpha(150),
        ),
        CircularProgressIndicator(
          color: Colors.white,
        ),
      ],
    );

    Widget errorWidget = Column(
      children: [
        Icon(
          Icons.info,
          size: 48,
          color: Colors.black.withAlpha(100),
        ),
        SizedBox(height: 24),
        Text(
          'Could not process. Please try again later or with different image',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        )
      ],
    );

    try {
      String? croppedURL = await newImageUpload(image);
      int rateLimiter = 1;

      var appDirectory = await getApplicationDocumentsDirectory();
      File file = File(appDirectory.path + 'dummy_path');

      Timer.periodic(const Duration(seconds: 3), (timer) async {
        if (timer.tick == 1) {
          controller.add(processingWidget);
        }

        var response = await http.get(Uri.parse(croppedURL!));
        file.writeAsBytesSync(response.bodyBytes);

        if (rateLimiter >= 10) {
          timer.cancel();
          controller.add(errorWidget);
          controller.close();
        }

        if (response.statusCode == 200) {
          timer.cancel();
          XFile? addImageFile = XFile(file.path);

          XFile? secondCroppedImage =
              await UserImage().croppedImage(addImageFile);

          await UserImage().addUserImage(secondCroppedImage);

          // print('image downloaded success');
          var processedWidget = Image.file(File(secondCroppedImage.path));
          sharedPrefs.setString('lastFetchedURL', '');

          controller.add(processedWidget);
          controller.close();
        } else {
          sharedPrefs.setString('lastFetchedURL', croppedURL);
          rateLimiter++;
        }
      });
    } catch (error) {
      controller.addError(error);
      controller.close();
      print('error: $error');
    }

    await for (var event in controller.stream) {
      yield event;
    }
  }

  // Future<String?> newImageUpload(XFile? imageFile) async {
  //   XFile? croppedFile = await cropImage(imageFile);

  //   var request = http.MultipartRequest(
  //     'GET', // Change the request method to POST
  //     Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/image'),
  //   );

  //   // Add image file to the request with the correct part name
  //   request.files
  //       .add(await http.MultipartFile.fromPath('file', croppedFile!.path));

  //   // Set headers
  //   final prefs = await SharedPreferences.getInstance();
  //   final String? token = prefs.getString('token');
  //   final String? userId = prefs.getString('userId');
  //   final String? deviceId = prefs.getString('deviceId');
  //   print("deviceif for bg");

  //   request.headers.addAll({
  //     'TOKEN': token ?? '',
  //     'app-user-id': userId ?? '',
  //     "DEVICE_ID": deviceId ?? '',
  //        "CLIENT_VERSION": "39",
  //     "CLIENT_TYPE": "ANDROID",
  //       "CLIENT_VERSION_CODE": "94"
  //   });

  //   try {
  //     final streamedResponse = await request.send();
  //     final response = await http.Response.fromStream(streamedResponse);

  //     if (response.statusCode == 200) {
  //       var responseDataURL = jsonDecode(response.body);
  //       String imageURL = responseDataURL["message"];
  //       print("succcesfully taken the image");
  //       return imageURL;
  //     } else {
  //       print('Error uploading image. Response: ${response.body}');
  //     }
  //   } catch (e) {
  //     print('Error uploading image: $e');
  //   }
  //   return null;
  // }
  /////////////////
//  Future<String?> newImageUpload(XFile? imageFile) async {
//   // Call the cropImage method to get the cropped image
//   XFile? croppedFile = await cropImage(imageFile);

//   if (croppedFile == null) {
//     print('Error: Cropped image is null.');
//     return null;
//   }

//   // Prepare the HTTP request
//   var request = http.MultipartRequest(
//     'GET', // Change the request method to POST
//     Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/image'),
//   );

//   // Add the image file to the request
//   request.files
//       .add(await http.MultipartFile.fromPath('file', croppedFile.path));

//   // Set headers
//   final prefs = await SharedPreferences.getInstance();
//   final String? token = prefs.getString('token');
//   final String? userId = prefs.getString('userId');
//   final String? deviceId = prefs.getString('deviceId');

//   request.headers.addAll({
//     'TOKEN': token ?? '',
//     'app-user-id': userId ?? '',
//     'DEVICE_ID': deviceId ?? '',
//     'CLIENT_VERSION': '34',
//     'CLIENT_TYPE': 'ANDROID',
//     'CLIENT_VERSION_CODE': '90',
//   });

//   // Polling variables
//   const int maxDurationInSeconds = 60;
//   const int pollingIntervalInSeconds = 2;
//   int elapsedSeconds = 0;

//   while (elapsedSeconds < maxDurationInSeconds) {
//     try {
//       // Send the request and wait for the response
//       final streamedResponse = await request.send();
//       final response = await http.Response.fromStream(streamedResponse);

//       if (response.statusCode == 200) {
//         var responseDataURL = jsonDecode(response.body);
//         String imageURL = responseDataURL["message"];
//         print("Successfully received the image URL.");
//         return imageURL;
//       } else {
//         print('Error uploading image. Response: ${response.body}');
//       }
//     } catch (e) {
//       print('Error during polling: $e');
//     }

//     // Wait for 2 seconds before retrying
//     await Future.delayed(Duration(seconds: pollingIntervalInSeconds));

//     // Increment elapsed time
//     elapsedSeconds += pollingIntervalInSeconds;
//   }

//   print('Polling timed out after 60 seconds.');
//   return null;
// }

  Future<String?> imageUploadWithPath(String imagePath) async {
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('https://backend.designboxconsuting.com/poster/files/v1/new'),
    );

    // Attach the image file
    request.files.add(await http.MultipartFile.fromPath('file', imagePath));

    // Retrieve headers from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');

    // Add headers
    request.headers.addAll({
      'token': token ?? '',
      'app-user-id': userId ?? '',
      'media-type': 'image/png',
    });

    // Add form field for media type
    request.fields['mediaType'] = 'IMAGE';

    try {
      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        var responseData = response.body;
        print('Upload successful: $responseData');
        return responseData.toString();
      } else {
        print('Error uploading image. Response: ${response.body}');
      }
    } catch (e) {
      print('Error uploading image: $e');
    }

    return null;
  }

////////////
  Future<String?> newImageUpload(XFile? imageFile) async {
    // Call the cropImage method to get the cropped image
    XFile? croppedFile = await cropImage(imageFile);

    if (croppedFile == null) {
      print('Error: Cropped image is null.');
      return null;
    }

    // Prepare the initial HTTP request to upload the image
    var request = http.MultipartRequest(
      'GET',
      Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/image'),
    );

    // Add the image file to the request
    request.files
        .add(await http.MultipartFile.fromPath('file', croppedFile.path));

    // Set headers
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String? deviceId = prefs.getString('deviceId');

    request.headers.addAll({
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      'DEVICE_ID': deviceId ?? '',
      'CLIENT_VERSION': '34',
      'CLIENT_TYPE': 'ANDROID',
      'CLIENT_VERSION_CODE': '90',
    });

    try {
      // Send the request and get the initial response
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        var responseData = jsonDecode(response.body);

        // Assuming you're receiving a URL in the "message" field for polling
        String pollingURL = responseData["message"];
        print("Polling URL: $pollingURL");

        // Now we need to poll this URL every 2 seconds for 60 seconds
        const int maxDurationInSeconds = 60;
        const int pollingIntervalInSeconds = 2;
        int elapsedSeconds = 0;

        while (elapsedSeconds < maxDurationInSeconds) {
          try {
            // Send a GET request to the polling URL
            final pollingResponse = await http.get(Uri.parse(pollingURL));

            if (pollingResponse.statusCode == 200) {
              // If status is 200, return the polling URL
              print("Polling successful. URL: $pollingURL");
              return pollingURL;
            } else {
              print('Still processing, checking again in 2 seconds...');
            }
          } catch (e) {
            print('Error during polling: $e');
          }

          // Wait for 2 seconds before retrying
          await Future.delayed(Duration(seconds: pollingIntervalInSeconds));

          // Increment elapsed time
          elapsedSeconds += pollingIntervalInSeconds;
        }

        print('Polling timed out after 60 seconds.');
      } else {
        print('Error uploading image. Response: ${response.body}');
      }
    } catch (e) {
      print('Error uploading image: $e');
    }

    return null;
  }

//OLD CODE
  // Future<XFile?> cropImage(XFile? file) async {
  //   ImageProperties properties =
  //       await FlutterNativeImage.getImageProperties(file!.path);
  //   var _resizedImage = await FlutterNativeImage.compressImage(file.path,
  //       targetWidth: 800,
  //       targetHeight: (properties.height! * 800 / (properties.width)!).round());
  //   return XFile(_resizedImage.path);
  // }

  Future<XFile?> cropImage(XFile? file) async {
    if (file == null) return null;

    // Load the image
    final imageBytes = await File(file.path).readAsBytes();
    img.Image? originalImage = img.decodeImage(imageBytes);

    if (originalImage == null) return null;

    // Define your target width and calculate target height
    int targetWidth = 800;
    int targetHeight =
        (originalImage.height * 800 / originalImage.width).round();

    // Resize the image
    img.Image resizedImage =
        img.copyResize(originalImage, width: targetWidth, height: targetHeight);

    // Save the resized image to a temporary location
    final resizedImagePath = '${file.path}_resized.png';
    File(resizedImagePath)..writeAsBytesSync(img.encodePng(resizedImage));

    return XFile(resizedImagePath);
  }

  Future<void> addUserImage(image) async {
    //checks permission access to write or not
    PermissionAccess().requestStoragePermission();
    final prefs = await SharedPreferences.getInstance();

    //Fetching app document and shared preferences to get user image ID
    final appDir = await getApplicationDocumentsDirectory();

    // checks if profile photo folder is created or not. If not created, then creates one.
    final profilePhotoDir = Directory('${appDir.path}/Profile Photo');
    if (!await profilePhotoDir.exists()) {
      await profilePhotoDir.create(recursive: true);
    }

    //photo IDs stored in shared preferences to tell us how many photos to fetch
    int? photoID = prefs.getInt('Photo ID');

    if (image != null) {
      //if photoID is null, set it to 0 and add the image
      if (photoID == null) {
        // setting photoID to 0 and caching the value in preferences for getting value everywhere else.
        // setting selected ID to 0 as well since this is the first photo in the stack
        photoID = 0;
        prefs.setInt('Photo ID', 0);
        prefs.setInt('SelectedID', 0);

        //adding image to directory:
        final profilePhotoFile =
            File('${profilePhotoDir.path}/profile-photo-$photoID.png/cropped');
        await profilePhotoFile.writeAsBytes(await image.readAsBytes());

        // returning selected user image:
        // returnSelectedUserImage(photoID);
      } else {
        // first we set photoID+1, so that we can add up new profile photos.
        photoID++;
        //caching photo ID, selected ID for the new stack
        await prefs.setInt('Photo ID', photoID);
        await prefs.setInt('SelectedID', photoID);

        //writing the photo to the directory
        final profilePhotoFile =
            File('${profilePhotoDir.path}/profile-photo-$photoID.jpg/cropped');
        await profilePhotoFile.writeAsBytes(await image.readAsBytes());
        // print('photo added to directory');
      }
    } else {
      //do nothing
    }
  }
}
