import 'dart:developer';
import 'package:android_play_install_referrer/android_play_install_referrer.dart';
import 'package:bjpnew/objects/Birthday.dart';
import 'package:bjpnew/services/local_notification_manager.dart';

class ReferrerService {
  Future<String> fetchReferrerData() async {
    try {
      ReferrerDetails referrerDetails =
          await AndroidPlayInstallReferrer.installReferrer;

      String referrer = referrerDetails.installReferrer ?? '';
      log("REFERRAL Received Data: $referrer");
      log("REFERRAL Received ALL: $referrerDetails.toString()");

      parseReferrerData(referrer);
      // return referrerDetails.toString();
      return referrer;
    } catch (e) {
      log("REFERRAL Error: $e");
      return "REFERRAL Error : $e";
    }
  }

  void parseReferrerData(String referrer) {
    try {
      final params = Uri.splitQueryString(referrer);

      String userId = params['userid'] ?? 'Not provided';
      String name = params['name'] ?? 'Not provided';
      String dob = params['dob'] ?? 'Not provided';
      String userImage = params['image'] ?? '';

      if (name != 'Not provided' && dob != 'Not provided') {
        // LocalNotificationControllerManager.addReferralBirthdayToPref(
        //   Birthday(name: name, imagePath: "", dob: DateTime.parse(dob)),
        //   referrer,
        // );
        LocalNotificationControllerManager.addReferralBirthdayToRemote(
          Birthday(name: name, imagePath: userImage, dob: DateTime.parse(dob)),
          referrer,
        );
        log("Parsed Data: UserId=$userId, Name=$name, DOB=$dob");
      } else {
        log("Referral not added due to missing or invalid data.");
      }
    } catch (e) {
      log("Error parsing referrer: $e");
    }
  }

  //UI Code for logging in live apk..
  // GestureDetector(
  //   onDoubleTap: () {
  //     setState(() {});
  //   },
  //   child: Text(referrerGlobal == "" ? "    " : referrerGlobal ,
  //       style: TextStyle(
  //           fontWeight: FontWeight.w800,
  //           fontSize: 16,
  //           fontFamily: 'Work-Sans',
  //           color: themeData.colorScheme.onBackground)),
  // ),
}
