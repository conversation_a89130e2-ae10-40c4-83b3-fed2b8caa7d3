import 'package:url_launcher/url_launcher.dart';

class LaunchEmail {
  launchEmail() async {
    String email = Uri.encodeComponent("<EMAIL>");
    String subject = Uri.encodeComponent("Support Poster For Bharat App ");
    String body = Uri.encodeComponent("Mention your query : ");
    Uri mail = Uri.parse("mailto:$email?subject=$subject&body=$body");
    if (await launchUrl(mail)) {
      //email app opened
    } else {
      //email app is not opened
    }
  }
}

class LaunchWhatsApp {
  openWhatsApp() async {
    final String phoneNumber = '+91 85281 62601';
    final String whatsappUrl = 'https://wa.me/$phoneNumber';

    if (await canLaunch(whatsappUrl)) {
      await launch(whatsappUrl);
    } else {
      throw 'Could not launch $whatsappUrl';
    }
  }
}
