import 'package:shared_preferences/shared_preferences.dart';

class TextSizeService {
  Map<String, double> _textSizes = {
    'text1': 20.0, // Default size for text1
    'text2': 18.0, // Default size for text2
  };

  static const String textSizeKeyPrefix = 'textSizeKey_';

  // Method to load text sizes from SharedPreferences
  Future<void> loadTextSizes() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    _textSizes['text1'] = prefs.getDouble(textSizeKeyPrefix + 'text1') ?? 20.0;
    _textSizes['text2'] = prefs.getDouble(textSizeKeyPrefix + 'text2') ?? 18.0;
  }

  // Method to get text size for a specific key
  double getTextSize(String key) => _textSizes[key] ?? 16.0;

  // Method to change the text size and save it to SharedPreferences
  Future<void> changeTextSize(String key, double sliderValue) async {
    _textSizes[key] = (key == 'text1' ? 20.0 : 18.0) +
        ((key == 'text1' ? 20.0 : 18.0) * sliderValue / 100);
    await _saveTextSize(key);
  }

  // Private method to save the text size for a specific key
  Future<void> _saveTextSize(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(textSizeKeyPrefix + key, _textSizes[key]!);
  }
}
