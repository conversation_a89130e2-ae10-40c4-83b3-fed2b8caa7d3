import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class UserDetailService {
  // Fetch user details
  Future<Map<String, dynamic>?> getUserDetails([String? uid]) async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    var userId = prefs.getString('userId');
    final String deviceId = prefs.getString('deviceId') ?? '';

    if (uid != null && uid.isNotEmpty) userId = uid;

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      "DEVICE_ID": deviceId,
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final url =
        Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/user');
    try {
      final response = await http.get(url, headers: headers);
      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        final name = userData['name'];
        final dob = userData['dob'];
        final userImage = userData['dob'];

        if (isValidString(name)) prefs.setString('name', name);
        if (isValidString(dob)) prefs.setString('dob', dob);
        if (isValidString(userImage)) prefs.setString('userImage', userImage);

        return userData;
      } else {
        print('Failed to fetch user details: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error fetching user details: $e');
      return null;
    }
  }

  bool isValidString(String? value) {
    return value != null &&
        value.trim().isNotEmpty &&
        value.trim().toLowerCase() != 'null';
  }

  Future<void> saveDOB(String dob) async {
    final cleanedDob = dob.replaceAll(RegExp(r'[^\x00-\x7F]'), '').trim();
    return saveUserDetails(userDetails: {'dob': cleanedDob});
  }

  // Save user details dynamically
  Future<void> saveUserDetails({
    required Map<String, dynamic> userDetails,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final String? token = prefs.getString('token');
    final String? userId = prefs.getString('userId');
    final String deviceId = prefs.getString('deviceId') ?? '';

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'TOKEN': token ?? '',
      'app-user-id': userId ?? '',
      "DEVICE_ID": deviceId,
      "CLIENT_VERSION": "39",
      "CLIENT_TYPE": "ANDROID",
      "CLIENT_VERSION_CODE": "94"
    };

    final body = jsonEncode(userDetails); // Dynamic body passed as param

    final url =
        Uri.parse('https://backend.designboxconsuting.com/poster/user/v1/user');
    try {
      final response = await http.put(url, headers: headers, body: body);
      if (response.statusCode == 200) {
        print('User details saved successfully');
      } else {
        print('Failed to save user details: ${response.statusCode}');
      }
    } catch (e) {
      print('Error saving user details: $e');
    }
  }
}
