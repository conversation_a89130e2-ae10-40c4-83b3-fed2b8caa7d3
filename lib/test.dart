//  import 'package:flutter/material.dart';
// import 'package:screenshot/screenshot.dart';
// import 'dart:io';
// import 'dart:typed_data';
// import 'package:auto_size_text/auto_size_text.dart';
// import 'package:transparent_image/transparent_image.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:share_plus/share_plus.dart';

// class ScreenshotExample extends StatefulWidget {
//   final String imageUrl;
//   final Function? onImageAdded;

//   ScreenshotExample({required this.imageUrl, this.onImageAdded});

//   @override
//   _ScreenshotExampleState createState() => _ScreenshotExampleState();
// }

// class _ScreenshotExampleState extends State<ScreenshotExample> {
//   ScreenshotController _controller = ScreenshotController();
//   Uint8List? _capturedImage;
//   var viewModel = ViewModel();

//   Future<void> captureScreenshot() async {
//     final imageFile = await _controller.capture(pixelRatio: 3);
//     setState(() {
//       _capturedImage = imageFile;
//     });
//   }

//   Future<void> shareScreenshot() async {
//     if (_capturedImage == null) {
//       await captureScreenshot();
//     }
//     final tempDir = await getTemporaryDirectory();
//     final path = tempDir.path;
//     File file = await File('$path/image.png').create();
//     file.writeAsBytesSync(_capturedImage!);
//     Share.shareXFiles([XFile('$path/image.png')]);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Screenshot Example'),
//         actions: [
//           IconButton(
//             icon: Icon(Icons.share),
//             onPressed: shareScreenshot,
//           )
//         ],
//       ),
//       body: Column(
//         children: [
//           Screenshot(
//             controller: _controller,
//             child: Stack(
//               alignment: Alignment.bottomCenter,
//               children: [
//                 Column(
//                   children: [
//                     Stack(
//                       children: [
//                         ClipRRect(
//                           clipBehavior: Clip.hardEdge,
//                           child: FadeInImage.memoryNetwork(
//                             image: widget.imageUrl,
//                             placeholder: kTransparentImage,
//                           ),
//                         ),
//                         SizedBox(
//                           height: 100,
//                           child: ListView.builder(
//                             itemCount: viewModel.imageData.length,
//                             scrollDirection: Axis.horizontal,
//                             itemBuilder: (context, index) {
//                               return GestureDetector(
//                                 onTap: captureScreenshot,
//                                 child: Container(
//                                   width: 50.0,
//                                   margin: EdgeInsets.only(bottom: 40, left: 5, top: 5),
//                                   decoration: BoxDecoration(
//                                     border: Border.all(color: Colors.green),
//                                     borderRadius: BorderRadius.circular(5.0),
//                                     image: DecorationImage(
//                                       image: viewModel.imageData[index].startsWith('http')
//                                           ? NetworkImage(viewModel.imageData[index])
//                                           : FileImage(File(viewModel.imageData[index])) as ImageProvider,
//                                       fit: BoxFit.cover,
//                                     ),
//                                   ),
//                                 ),
//                               );
//                             },
//                           ),
//                         ),
//                       ],
//                     ),
//                     Container(
//                       width: MediaQuery.of(context).size.width,
//                       padding: EdgeInsets.only(left: 16, right: MediaQuery.of(context).size.width / 2.2, top: 12, bottom: 8),
//                       color: viewModel.containerColor,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           AutoSizeText(
//                             viewModel.userDetails[0],
//                             textAlign: TextAlign.left,
//                             maxLines: 1,
//                             maxFontSize: 24,
//                             minFontSize: 18,
//                             style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                                   fontFamily: 'Arya',
//                                   color: Colors.white,
//                                   height: 1.2,
//                                   fontWeight: FontWeight.w800,
//                                 ),
//                           ),
//                           SizedBox(height: 4),
//                           AutoSizeText(
//                             viewModel.userDetails[1],
//                             maxFontSize: 18,
//                             textAlign: TextAlign.left,
//                             style: Theme.of(context).textTheme.displayMedium?.copyWith(
//                                   color: Colors.white,
//                                   fontFamily: 'Mukta',
//                                   height: 1.4,
//                                 ),
//                             maxLines: 2,
//                           ),
//                           SizedBox(height: 4),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//                 Positioned(
//                   right: 0,
//                   bottom: 0,
//                   child: GestureDetector(
//                     onTap: () async {
//                       var changedImage = await viewModel.userImageChange(context);
//                       if (changedImage != null) {
//                         await removeBackground(changedImage);
//                         widget.onImageAdded?.call();
//                       }
//                     },
//                     child: FutureBuilder(
//                       future: viewModel.userImage(),
//                       builder: (context, snapshot) {
//                         return Container(
//                           alignment: Alignment.bottomRight,
//                           height: 180,
//                           width: 180,
//                           child: snapshot.data,
//                         );
//                       },
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           SizedBox(height: 20),
//           _capturedImage != null
//               ? Image.memory(_capturedImage!)
//               : Container(
//                   margin: EdgeInsets.only(top: 20),
//                   child: Text('No screenshot captured'),
//                 ),
//         ],
//       ),
//     );
//   }
// }

// class ViewModel {
//   List<String> imageData = [
//     'http://example.com/image1.jpg',
//     'http://example.com/image2.jpg'
//   ];
//   List<String> userDetails = ['User Name', 'User Details'];
//   Color containerColor = Colors.blue;

//   Future<String> userImage() async {
//     // Simulate a network call
//     await Future.delayed(Duration(seconds: 2));
//     return 'http://example.com/userImage.jpg';
//   }

//   Future<String?> userImageChange(BuildContext context) async {
//     // Simulate an image change operation
//     await Future.delayed(Duration(seconds: 2));
//     return 'http://example.com/changedImage.jpg';
//   }
// }

// Future<void> removeBackground(String imagePath) async {
//   // Simulate a background removal operation
//   await Future.delayed(Duration(seconds: 2));
// }
// //  Screenshot(
// //             controller: _controller,
// //             child: Stack(
// //               alignment: Alignment.bottomCenter,
// //               children: [
// //                 Column(
// //                   children: [
// //                     Stack(
// //                       children: [
// //                         ClipRRect(
// //                             clipBehavior: Clip.hardEdge,
// //                             child: FadeInImage.memoryNetwork(
// //                                 image: widget.imageUrl,
// //                                 placeholder: kTransparentImage)),
// //                 SizedBox(
// //                    height: 100,
// //                    child: ListView.builder(

// //                           itemCount: viewModel.imageData.length,
// //                          scrollDirection: Axis.horizontal,
// //                           itemBuilder: (context, index) {
// //                             return GestureDetector(
// //                               onTap: () {
// //                                print(viewModel.imageData.length) ;

// //                               },
// //                               child:Container(

// //   width: 50.0,
// //   margin: EdgeInsets.only(bottom: 40 ,left: 5 ,top: 5),

// //   decoration: BoxDecoration(
// //     border: Border.all(color: Colors.green),
// //     borderRadius: BorderRadius.circular(5.0),
// //     image: DecorationImage(
// //       image: viewModel.imageData[index].startsWith('http')
// //           ? NetworkImage(viewModel.imageData[index])
// //           : FileImage(File(viewModel.imageData[index])) as ImageProvider,
// //         fit: BoxFit.cover,
// //     ),
// //   ),
// // ));
// //                           },
// //                         ),
// //               ) ,

// //                       ],
// //                     ),
// //                     Container(
// //                       width: MediaQuery.of(context).size.width,
// //                       padding: EdgeInsets.only(
// //                           left: 16,
// //                           right: MediaQuery.of(context).size.width / 2.2,
// //                           top: 12,
// //                           bottom: 8),
// //                       color: viewModel.containerColor,
// //                       child: Column(
// //                         crossAxisAlignment: CrossAxisAlignment.start,
// //                         mainAxisAlignment: MainAxisAlignment.center,
// //                         children: [
// //                           AutoSizeText(
// //                             viewModel.userDetails[0],
// //                             textAlign: TextAlign.left,
// //                             maxLines: 1,
// //                             maxFontSize: 24,
// //                             minFontSize: 18,
// //                             style: Theme.of(context)
// //                                 .textTheme
// //                                 .headlineMedium
// //                                 ?.copyWith(
// //                                   fontFamily: 'Arya',
// //                                   color: Colors.white,
// //                                   height: 1.2,
// //                                   fontWeight: FontWeight.w800,
// //                                 ),
// //                           ),
// //                           SizedBox(
// //                             height: 4,
// //                           ),
// //                           AutoSizeText(
// //                             viewModel.userDetails[1],
// //                             maxFontSize: 18,
// //                             textAlign: TextAlign.left,
// //                             style: Theme.of(context)
// //                                 .textTheme
// //                                 .displayMedium
// //                                 ?.copyWith(
// //                                     color: Colors.white,
// //                                     fontFamily: 'Mukta',
// //                                     height: 1.4),
// //                             maxLines: 2,
// //                           ),
// //                           SizedBox(
// //                             height: 4,
// //                           )
// //                         ],
// //                       ),
// //                     )
// //                   ],
// //                 ),
// //                 Positioned(
// //                     right: 0,
// //                     bottom: 0,
// //                     child: GestureDetector(
// //                       onTap: () async {
// //                         var changedImage =
// //                             await viewModel.userImageChange(context);
// //                         if (changedImage != null) {
// //                           await removeBackground(changedImage);
// //                           widget.onImageAdded?.call();
// //                         }
// //                       },
// //                       child: FutureBuilder(
// //                           future: viewModel.userImage(),
// //                           builder: (context, snapshot) {
// //                             return Container(
// //                               alignment: Alignment.bottomRight,
// //                               height: 180,
// //                               width: 180,
// //                               child: snapshot.data,
// //                             );
// //                           }),
// //                     ))
// //               ],
// //             ),
// //           ),
