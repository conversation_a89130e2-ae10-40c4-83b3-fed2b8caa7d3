import 'dart:convert';
import 'dart:io';

import 'package:bjpnew/global/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

String decodeUtf8(String input) {
  try {
    // Decode the UTF-8 encoded string
    List<int> bytes = input.codeUnits;
    return utf8.decode(bytes);
  } catch (e) {
    print("Error decoding UTF-8 string: $e");
    return input; // Return the original input if decoding fails
  }
}

class Utils {
  ImageProvider loadImageP(String? imagePath) {
    if (imagePath != null && imagePath.isNotEmpty) {
      if (imagePath.startsWith('http')) {
        return NetworkImage(imagePath);
      } else {
        File file = File(imagePath);
        if (file.existsSync()) {
          return FileImage(file);
        }
      }
    }
    return const AssetImage('Asset/SVG/ImagePlaceholder.svg');
  }

  Widget loadImage(String? imagePath) {
    if (imagePath != null && imagePath.isNotEmpty) {
      if (imagePath.startsWith("http")) {
        return Image.network(
          imagePath,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _placeholder(),
        );
      } else {
        File file = File(imagePath);
        if (file.existsSync()) {
          return Image.file(
            file,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => _placeholder(),
          );
        }
      }
    }
    return _placeholder();
  }

  Widget _placeholder() => const Center(child: Text("Tap to select an image"));

  static bool _isDialogShowing = false;

  /// Show a loading alert dialog
  static void showLoaderDialog(BuildContext context,
      {String message = "Loading..."}) {
    if (_isDialogShowing) return; // Prevent multiple dialogs
    _isDialogShowing = true;

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent user from closing it manually
      builder: (context) {
        return WillPopScope(
          onWillPop: () async => false, // Disable back button
          child: AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            contentPadding: EdgeInsets.all(16),
            backgroundColor: Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(
                  message,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static Future<void> showSuccessDialog({
    required BuildContext context,
    required String title,
    required String subText,
    VoidCallback? onTap, // Optional onTap callback
  }) async {
    ThemeData themeData = Theme.of(context);
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: EdgeInsets.all(16),
          backgroundColor: Colors.white,
          content: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: 164,
                  child: Lottie.asset('Asset/Lottie/success-lottie.json'),
                ),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 24,
                      fontFamily: 'Mukta',
                      height: 1.2,
                      color: Colors.black87),
                ),
                SizedBox(height: 12),
                Text(
                  subText,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 20,
                      fontFamily: 'Mukta',
                      height: 1.2,
                      color: Colors.black54),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            PrimaryButton(
              isEnabled: true,
              height: 48,
              isLoading: false,
              onTap: () {
                Navigator.pop(context);
                if (onTap != null) {
                  onTap(); // Execute custom action if provided
                }
              },
              label: 'Okay',
              color: themeData.colorScheme.primary,
            ),
          ],
        );
      },
    );
  }

  /// Hide the loading alert dialog
  static void hideLoaderDialog(BuildContext context) {
    if (_isDialogShowing) {
      Navigator.of(context, rootNavigator: true).pop();
      _isDialogShowing = false;
    }
  }
}
