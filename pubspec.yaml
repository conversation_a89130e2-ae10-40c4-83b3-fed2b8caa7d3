name: bjpnew
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 0.0.1+7

environment:
  sdk: ">=2.18.0 <4.0.0"
  # flutter: ">=3.24.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  ffmpeg_kit_flutter: 4.5.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
 
  hexcolor: ^3.0.1
  carousel_slider: ^4.2.1
  firebase_auth: ^4.2.7
  firebase_database: ^10.0.14
  google_sign_in: ^6.0.2
  lottie: ^2.2.0
  firebase_core: ^2.5.0
  cloud_firestore: ^4.4.3
  shared_preferences: ^2.0.17
  flutter_svg: ^2.0.1
  share_plus: ^7.0.2
  path_provider: ^2.0.12
  permission_handler: ^10.2.0
  image_picker: ^0.8.6+2
  dio: ^5.0.0
  flutter_downloader: ^1.10.2
  flutter_local_notifications: ^18.0.1
  screenshot: ^3.0.0
  widgets_to_image: ^0.0.2
  flutter_launcher_icons: ^0.13.1
  firebase_storage: ^11.1.0
  open_filex: ^4.3.2
  amplitude_flutter: ^3.13.0
  url_launcher: ^6.1.11
  facebook_app_events: ^0.19.0
  firebase_messaging: ^14.6.4

  # flutter_native_image: ^0.0.6
  image: ^4.3.0
  
  auto_size_text: ^3.0.0
  transparent_image: ^2.0.1
  device_info_plus: ^9.0.3
  image_cropper: ^5.0.1
  phone_pe_pg:  
  in_app_update: ^4.2.2
  stacked: ^3.4.1+1
  auto_route: ^7.8.4
  stacked_services: ^1.3.0
  build_runner: ^2.4.6
  stacked_themes: ^0.3.12
  get_it: ^7.6.4
  razorpay_flutter: ^1.3.6
  intl: ^0.19.0
  video_player: ^2.8.5
  ffmpeg_kit_flutter_platform_interface: ^0.2.1
  persistent_bottom_nav_bar: ^6.2.1
  dotted_border: ^2.1.0
  flutter_image_compress: ^2.3.0
  path:  
  timezone: ^0.10.0
  text_3d: ^0.0.4
  
  android_play_install_referrer:
    git:
      url: https://github.com/kartikcy/android_play_install_referrer.git
  
 
  # video_player: ^2.8.4

dependency_overrides:
  ffi: ^2.0.1

dev_dependencies:
  auto_route_generator:
  flutter_test:
    sdk: flutter
  stacked_generator: 
   
flutter_icons:
  image_path: "Asset/Logo/Poster-App-Logo.png"
  android: true
  ios: false

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - Asset/Images/
    - Asset/Fonts/
    - Asset/Icons/
    - Asset/Logo/
    - Asset/Lottie/
    - Asset/SVG/
    - Asset/Political Logos/
    - Asset/Nameplates/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Work-Sans
      fonts:
        - asset: Asset/Fonts/WorkSans-Bold.ttf
          weight: 700
        - asset: Asset/Fonts/WorkSans-ExtraBold.ttf
          weight: 900
        - asset: Asset/Fonts/WorkSans-Medium.ttf
          weight: 500
        - asset: Asset/Fonts/WorkSans-Regular.ttf
          weight: 300

    - family: Arya
      fonts:
        - asset: Asset/Fonts/Arya-Bold.ttf
        - asset: Asset/Fonts/Arya-Regular.ttf

    - family: Mukta
      fonts:
        - asset: Asset/Fonts/Mukta-Regular.ttf
          weight: 400
        - asset: Asset/Fonts/Mukta-Medium.ttf
          weight: 600
        - asset: Asset/Fonts/Mukta-Bold.ttf
          weight: 800
          
    - family: Anek
      fonts:
        - asset: Asset/Fonts/AnekTelugu-VariableFont_wdth,wght.ttf
    
    - family: Baloo2
      fonts:
        - asset: Asset/Fonts/Baloo2-Regular.ttf
    
    - family: AnekTelugu
      fonts:
        - asset: Asset/Fonts/AnekTelugu-Light.ttf


  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
